#!/bin/bash

# Exit on error
set -e

# Define project root
PROJECT_ROOT="src"

# Generate modules
## Auth module
nest g res modules/auth --no-spec

nest g gu modules/auth/guards/auth.guard.ts --no-spec
nest g gu modules/auth/guards/roles.guard.ts --no-spec
touch $PROJECT_ROOT/modules/auth/guards/index.ts

touch $PROJECT_ROOT/modules/auth/strategies/jwt.strategy.ts
touch $PROJECT_ROOT/modules/auth/strategies/local.strategy.ts
touch $PROJECT_ROOT/modules/auth/strategies/google.strategy.ts
touch $PROJECT_ROOT/modules/auth/strategies/index.ts

## User module
nest g res modules/users --no-spec

## Domain module
nest g res modules/domains --no-spec

## Server module
nest g res modules/servers --no-spec

## Deployment module
nest g res modules/deployments --no-spec
touch $PROJECT_ROOT/modules/deployments/workers/deployment.worker.ts

# Guards
touch $PROJECT_ROOT/modules/shared/guards/jwt.guard.ts
touch $PROJECT_ROOT/modules/shared/guards/local.guard.ts
touch $PROJECT_ROOT/modules/shared/guards/roles.guard.ts
touch $PROJECT_ROOT/modules/shared/guards/public.guard.ts
touch $PROJECT_ROOT/modules/shared/guards/permissions.guard.ts

# Interceptors
touch $PROJECT_ROOT/modules/shared/interceptors/response.interceptor.ts

# Decorators
touch $PROJECT_ROOT/modules/shared/decorators/public.decorator.ts
touch $PROJECT_ROOT/modules/shared/decorators/roles.decorator.ts
touch $PROJECT_ROOT/modules/shared/decorators/whoami.decorator.ts
touch $PROJECT_ROOT/modules/shared/decorators/permissions.decorator.ts

# Filters
touch $PROJECT_ROOT/modules/shared/filters/all-exceptions.filter.ts

# Logger
touch $PROJECT_ROOT/modules/shared/logger.ts

echo "Project structure generated successfully!"

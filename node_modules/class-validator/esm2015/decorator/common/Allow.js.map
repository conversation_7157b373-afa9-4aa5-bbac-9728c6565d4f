{"version": 3, "file": "Allow.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/Allow.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AAEpE;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,MAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,eAAe,CAAC,SAAS;YAC/B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,iBAAiB,EAAE,iBAAiB;SACrC,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * If object has both allowed and not allowed properties a validation error will be thrown.\n */\nexport function Allow(validationOptions?: ValidationOptions): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.WHITELIST,\n      target: object.constructor,\n      propertyName: propertyName,\n      validationOptions: validationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}
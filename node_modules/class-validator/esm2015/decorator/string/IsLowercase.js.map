{"version": 3, "file": "IsLowercase.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsLowercase.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,MAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,sCAAsC,EACjE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLowercaseValidator from 'validator/lib/isLowercase';\n\nexport const IS_LOWERCASE = 'isLowercase';\n\n/**\n * Checks if the string is lowercase.\n * If given value is not a string, then it returns false.\n */\nexport function isLowercase(value: unknown): boolean {\n  return typeof value === 'string' && isLowercaseValidator(value);\n}\n\n/**\n * Checks if the string is lowercase.\n * If given value is not a string, then it returns false.\n */\nexport function IsLowercase(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LOWERCASE,\n      validator: {\n        validate: (value, args): boolean => isLowercase(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a lowercase string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
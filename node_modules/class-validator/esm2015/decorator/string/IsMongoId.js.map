{"version": 3, "file": "IsMongoId.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMongoId.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAEzD,MAAM,CAAC,MAAM,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;YACpD,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gCAAgC,EAAE,iBAAiB,CAAC;SAC7G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMongoIdValidator from 'validator/lib/isMongoId';\n\nexport const IS_MONGO_ID = 'isMongoId';\n\n/**\n * Checks if the string is a valid hex-encoded representation of a MongoDB ObjectId.\n * If given value is not a string, then it returns false.\n */\nexport function isMongoId(value: unknown): boolean {\n  return typeof value === 'string' && isMongoIdValidator(value);\n}\n\n/**\n * Checks if the string is a valid hex-encoded representation of a MongoDB ObjectId.\n * If given value is not a string, then it returns false.\n */\nexport function IsMongoId(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MONGO_ID,\n      validator: {\n        validate: (value, args): boolean => isMongoId(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a mongodb id', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
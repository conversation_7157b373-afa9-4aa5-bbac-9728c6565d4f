{"version": 3, "file": "IsBase58.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBase58.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,kCAAkC,EAAE,iBAAiB,CAAC;SAC/G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,4BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBase58Validator from 'validator/lib/isBase58';\n\nexport const IS_BASE58 = 'isBase58';\n\n/**\n * Checks if a string is base58 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function isBase58(value: unknown): boolean {\n  return typeof value === 'string' && isBase58Validator(value);\n}\n\n/**\n * Checks if a string is base58 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function IsBase58(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BASE58,\n      validator: {\n        validate: (value, args): boolean => isBase58(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be base58 encoded', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
{"version": 3, "file": "IsISSN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISSN.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAGtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc,EAAE,OAAmC;IACxE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,OAAmC,EAAE,iBAAqC;IAC/F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAZD,wBAYC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISSNValidator from 'validator/lib/isISSN';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ISSN = 'isISSN';\n\n/**\n * Checks if the string is a ISSN.\n * If given value is not a string, then it returns false.\n */\nexport function isISSN(value: unknown, options?: ValidatorJS.IsISSNOptions): boolean {\n  return typeof value === 'string' && isISSNValidator(value, options);\n}\n\n/**\n * Checks if the string is a ISSN.\n * If given value is not a string, then it returns false.\n */\nexport function IsISSN(options?: ValidatorJS.IsISSNOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISSN,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isISSN(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a ISSN', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
{"version": 3, "file": "IsCreditCard.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsCreditCard.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,8EAA+D;AAElD,QAAA,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,SAAgB,YAAY,CAAC,KAAc;IACzC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,sBAAqB,EAAC,KAAK,CAAC,CAAC;AACnE,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAAC,iBAAqC;IAChE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC;YACvD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iCAAiC,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,oCAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isCreditCardValidator from 'validator/lib/isCreditCard';\n\nexport const IS_CREDIT_CARD = 'isCreditCard';\n\n/**\n * Checks if the string is a credit card.\n * If given value is not a string, then it returns false.\n */\nexport function isCreditCard(value: unknown): boolean {\n  return typeof value === 'string' && isCreditCardValidator(value);\n}\n\n/**\n * Checks if the string is a credit card.\n * If given value is not a string, then it returns false.\n */\nexport function IsCreditCard(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_CREDIT_CARD,\n      validator: {\n        validate: (value, args): boolean => isCreditCard(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a credit card', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
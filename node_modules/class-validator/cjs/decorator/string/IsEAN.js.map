{"version": 3, "file": "IsEAN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsEAN.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gEAAiD;AAEpC,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,SAAgB,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,eAAc,EAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAFD,sBAEC;AAED;;;GAGG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAChD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oDAAoD,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,sBAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isEANValidator from 'validator/lib/isEAN';\n\nexport const IS_EAN = 'isEAN';\n\n/**\n * Check if the string is an EAN (European Article Number).\n * If given value is not a string, then it returns false.\n */\nexport function isEAN(value: unknown): boolean {\n  return typeof value === 'string' && isEANValidator(value);\n}\n\n/**\n * Check if the string is an EAN (European Article Number).\n * If given value is not a string, then it returns false.\n */\nexport function IsEAN(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_EAN,\n      validator: {\n        validate: (value, args): boolean => isEAN(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an EAN (European Article Number)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
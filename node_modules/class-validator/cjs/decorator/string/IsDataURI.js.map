{"version": 3, "file": "IsDataURI.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsDataURI.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAE5C,QAAA,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,iBAAqC;IAC7D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,mBAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;YACpD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,qCAAqC,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,8BAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isDataURIValidator from 'validator/lib/isDataURI';\n\nexport const IS_DATA_URI = 'isDataURI';\n\n/**\n * Check if the string is a data uri format.\n * If given value is not a string, then it returns false.\n */\nexport function isDataURI(value: unknown): boolean {\n  return typeof value === 'string' && isDataURIValidator(value);\n}\n\n/**\n * Check if the string is a data uri format.\n * If given value is not a string, then it returns false.\n */\nexport function IsDataURI(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DATA_URI,\n      validator: {\n        validate: (value, args): boolean => isDataURI(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a data uri format',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
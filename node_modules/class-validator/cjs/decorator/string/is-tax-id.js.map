{"version": 3, "file": "is-tax-id.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/is-tax-id.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAExC,QAAA,SAAS,GAAG,SAAS,CAAC;AAEnC;;;;;;;GAOG;AACH,SAAgB,OAAO,CAAC,KAAc,EAAE,MAAe;IACrD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,EAAE,MAAM,IAAI,OAAO,CAAC,CAAC;AACjF,CAAC;AAFD,0BAEC;AAED;;;;;;;GAOG;AACH,SAAgB,OAAO,CAAC,MAAe,EAAE,iBAAqC;IAC5E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+CAA+C,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,0BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isTaxIDValidator from 'validator/lib/isTaxID';\n\nexport const IS_TAX_ID = 'isTaxId';\n\n/**\n * Checks if the string is a valid tax ID. Default locale is `en-US`.\n * If given value is not a string, then it returns false.\n *\n * Supported locales: bg-BG, cs-CZ, de-AT, de-DE, dk-DK, el-CY, el-GR, en-CA,\n * en-IE, en-US, es-ES, et-EE, fi-FI, fr-BE, fr-FR, fr-LU, hr-HR, hu-HU, it-IT,\n * lv-LV, mt-MT, nl-NL, pl-PL, pt-BR, pt-PT, ro-RO, sk-SK, sl-SI, sv-SE.\n */\nexport function isTaxId(value: unknown, locale?: string): boolean {\n  return typeof value === 'string' && isTaxIDValidator(value, locale || 'en-US');\n}\n\n/**\n * Checks if the string is a valid tax ID. Default locale is `en-US`.\n * If given value is not a string, then it returns false.\n *\n * Supported locales: bg-BG, cs-CZ, de-AT, de-DE, dk-DK, el-CY, el-GR, en-CA,\n * en-IE, en-US, es-ES, et-EE, fi-FI, fr-BE, fr-FR, fr-LU, hr-HR, hu-HU, it-IT,\n * lv-LV, mt-MT, nl-NL, pl-PL, pt-BR, pt-PT, ro-RO, sk-SK, sl-SI, sv-SE.\n */\nexport function IsTaxId(locale?: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_TAX_ID,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isTaxId(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a Tax Identification Number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
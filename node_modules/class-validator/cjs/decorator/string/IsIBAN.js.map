{"version": 3, "file": "IsIBAN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsIBAN.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAEtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,2BAA2B,EAAE,iBAAiB,CAAC;SACxG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,wBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIBANValidator from 'validator/lib/isIBAN';\n\nexport const IS_IBAN = 'isIBAN';\n\n/**\n * Check if a string is a IBAN (International Bank Account Number).\n * If given value is not a string, then it returns false.\n */\nexport function isIBAN(value: unknown): boolean {\n  return typeof value === 'string' && isIBANValidator(value);\n}\n\n/**\n * Check if a string is a IBAN (International Bank Account Number).\n * If given value is not a string, then it returns false.\n */\nexport function IsIBAN(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_IBAN,\n      validator: {\n        validate: (value, args): boolean => isIBAN(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an IBAN', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
{"version": 3, "file": "IsEmail.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsEmail.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAGxC,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAc,EAAE,OAAoC;IAC1E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvE,CAAC;AAFD,0BAEC;AAED;;;GAGG;AACH,SAAgB,OAAO,CACrB,OAAoC,EACpC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,4BAA4B,EAAE,iBAAiB,CAAC;SACzG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,0BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isEmailValidator from 'validator/lib/isEmail';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_EMAIL = 'isEmail';\n\n/**\n * Checks if the string is an email.\n * If given value is not a string, then it returns false.\n */\nexport function isEmail(value: unknown, options?: ValidatorJS.IsEmailOptions): boolean {\n  return typeof value === 'string' && isEmailValidator(value, options);\n}\n\n/**\n * Checks if the string is an email.\n * If given value is not a string, then it returns false.\n */\nexport function IsEmail(\n  options?: ValidatorJS.IsEmailOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_EMAIL,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isEmail(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an email', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
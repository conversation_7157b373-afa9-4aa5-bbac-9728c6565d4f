{"version": 3, "file": "IsAlpha.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAlpha.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAGxC,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAc,EAAE,MAAgC;IACtE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtE,CAAC;AAFD,0BAEC;AAED;;;GAGG;AACH,SAAgB,OAAO,CAAC,MAAgC,EAAE,iBAAqC;IAC7F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,8CAA8C,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,0BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAlphaValidator from 'validator/lib/isAlpha';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ALPHA = 'isAlpha';\n\n/**\n * Checks if the string contains only letters (a-zA-Z).\n * If given value is not a string, then it returns false.\n */\nexport function isAlpha(value: unknown, locale?: ValidatorJS.AlphaLocale): boolean {\n  return typeof value === 'string' && isAlphaValidator(value, locale);\n}\n\n/**\n * Checks if the string contains only letters (a-zA-Z).\n * If given value is not a string, then it returns false.\n */\nexport function IsAlpha(locale?: ValidatorJS.AlphaLocale, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ALPHA,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isAlpha(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only letters (a-zA-Z)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
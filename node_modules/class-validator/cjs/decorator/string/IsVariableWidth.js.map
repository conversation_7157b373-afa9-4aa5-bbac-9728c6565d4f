{"version": 3, "file": "IsVariableWidth.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsVariableWidth.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oFAAqE;AAExD,QAAA,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,yBAAwB,EAAC,KAAK,CAAC,CAAC;AACtE,CAAC;AAFD,0CAEC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAAC,iBAAqC;IACnE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,yBAAiB;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;YAC1D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+DAA+D,EAC1F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,0CAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isVariableWidthValidator from 'validator/lib/isVariableWidth';\n\nexport const IS_VARIABLE_WIDTH = 'isVariableWidth';\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function isVariableWidth(value: unknown): boolean {\n  return typeof value === 'string' && isVariableWidthValidator(value);\n}\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsVariableWidth(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_VARIABLE_WIDTH,\n      validator: {\n        validate: (value, args): boolean => isVariableWidth(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a full-width and half-width characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
{"version": 3, "file": "IsPhoneNumber.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsPhoneNumber.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAChE,+CAAsE;AAEzD,QAAA,eAAe,GAAG,eAAe,CAAC;AAE/C;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,KAAa,EAAE,MAAoB;IAC/D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;QACxD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,IAAA,sBAAgB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEpD;;;YAGI;QACJ,IAAI,MAAM,IAAI,WAAW,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,WAAW,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AApBD,sCAoBC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,MAAoB,EAAE,iBAAqC;IACvF,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,uBAAe;QACrB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,wCAAwC,EACnE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,sCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport { parsePhoneNumber, CountryCode } from 'libphonenumber-js/max';\n\nexport const IS_PHONE_NUMBER = 'isPhoneNumber';\n\n/**\n * Checks if the string is a valid phone number. To successfully validate any phone number the text must include\n * the intl. calling code, if the calling code wont be provided then the region must be set.\n *\n * @param value the potential phone number string to test\n * @param region 2 characters uppercase country code (e.g. DE, US, CH) for country specific validation.\n * If text doesn't start with the international calling code (e.g. +41), then you must set this parameter.\n */\nexport function isPhoneNumber(value: string, region?: CountryCode): boolean {\n  if (typeof value !== 'string' || value.trim() !== value) {\n    return false;\n  }\n\n  try {\n    const phoneNumber = parsePhoneNumber(value, region);\n\n    /**\n     * We fail the validation if the user provided a region code\n     * and it doesn't match with the country code of the parsed number.\n     **/\n    if (region && phoneNumber.country !== region) {\n      return false;\n    }\n\n    return phoneNumber.isValid();\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Checks if the string is a valid phone number. To successfully validate any phone number the text must include\n * the intl. calling code, if the calling code wont be provided then the region must be set.\n *\n * @param region 2 characters uppercase country code (e.g. DE, US, CH) for country specific validation.\n * If text doesn't start with the international calling code (e.g. +41), then you must set this parameter.\n */\nexport function IsPhoneNumber(region?: CountryCode, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_PHONE_NUMBER,\n      constraints: [region],\n      validator: {\n        validate: (value, args): boolean => isPhoneNumber(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid phone number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
{"version": 3, "file": "IsAlphanumeric.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAlphanumeric.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kFAAmE;AAGtD,QAAA,eAAe,GAAG,gBAAgB,CAAC;AAEhD;;;GAGG;AACH,SAAgB,cAAc,CAAC,KAAc,EAAE,MAAuC;IACpF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,wBAAuB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC;AAFD,wCAEC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAC5B,MAAuC,EACvC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,uBAAe;QACrB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iDAAiD,EAC5E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,wCAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAlphanumericValidator from 'validator/lib/isAlphanumeric';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ALPHANUMERIC = 'isAlphanumeric';\n\n/**\n * Checks if the string contains only letters and numbers.\n * If given value is not a string, then it returns false.\n */\nexport function isAlphanumeric(value: unknown, locale?: ValidatorJS.AlphanumericLocale): boolean {\n  return typeof value === 'string' && isAlphanumericValidator(value, locale);\n}\n\n/**\n * Checks if the string contains only letters and numbers.\n * If given value is not a string, then it returns false.\n */\nexport function IsAlphanumeric(\n  locale?: ValidatorJS.AlphanumericLocale,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ALPHANUMERIC,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isAlphanumeric(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only letters and numbers',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
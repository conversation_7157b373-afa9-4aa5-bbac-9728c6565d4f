{"version": 3, "file": "IsCurrency.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsCurrency.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,0EAA2D;AAG9C,QAAA,WAAW,GAAG,YAAY,CAAC;AAExC;;;GAGG;AACH,SAAgB,UAAU,CAAC,KAAc,EAAE,OAAuC;IAChF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,oBAAmB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1E,CAAC;AAFD,gCAEC;AAED;;;GAGG;AACH,SAAgB,UAAU,CACxB,OAAuC,EACvC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,mBAAW;QACjB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC3E,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,8BAA8B,EAAE,iBAAiB,CAAC;SAC3G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,gCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isCurrencyValidator from 'validator/lib/isCurrency';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_CURRENCY = 'isCurrency';\n\n/**\n * Checks if the string is a valid currency amount.\n * If given value is not a string, then it returns false.\n */\nexport function isCurrency(value: unknown, options?: ValidatorJS.IsCurrencyOptions): boolean {\n  return typeof value === 'string' && isCurrencyValidator(value, options);\n}\n\n/**\n * Checks if the string is a valid currency amount.\n * If given value is not a string, then it returns false.\n */\nexport function IsCurrency(\n  options?: ValidatorJS.IsCurrencyOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_CURRENCY,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isCurrency(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a currency', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
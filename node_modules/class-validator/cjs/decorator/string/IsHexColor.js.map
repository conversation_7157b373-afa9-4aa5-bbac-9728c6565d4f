{"version": 3, "file": "IsHexColor.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHexColor.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,0EAA2D;AAE9C,QAAA,YAAY,GAAG,YAAY,CAAC;AAEzC;;;GAGG;AACH,SAAgB,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,oBAAmB,EAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAFD,gCAEC;AAED;;;GAGG;AACH,SAAgB,UAAU,CAAC,iBAAqC;IAC9D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,oBAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;YACrD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uCAAuC,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,gCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHexColorValidator from 'validator/lib/isHexColor';\n\nexport const IS_HEX_COLOR = 'isHexColor';\n\n/**\n * Checks if the string is a hexadecimal color.\n * If given value is not a string, then it returns false.\n */\nexport function isHexColor(value: unknown): boolean {\n  return typeof value === 'string' && isHexColorValidator(value);\n}\n\n/**\n * Checks if the string is a hexadecimal color.\n * If given value is not a string, then it returns false.\n */\nexport function IsHexColor(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HEX_COLOR,\n      validator: {\n        validate: (value, args): boolean => isHexColor(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a hexadecimal color',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
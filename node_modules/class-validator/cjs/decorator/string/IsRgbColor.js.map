{"version": 3, "file": "IsRgbColor.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsRgbColor.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,0EAA2D;AAE9C,QAAA,YAAY,GAAG,YAAY,CAAC;AAEzC;;;;GAIG;AACH,SAAgB,UAAU,CAAC,KAAc,EAAE,oBAA8B;IACvE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,oBAAmB,EAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;AACvF,CAAC;AAFD,gCAEC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,oBAA8B,EAAE,iBAAqC;IAC9F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,oBAAY;QAClB,WAAW,EAAE,CAAC,oBAAoB,CAAC;QACnC,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC3E,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,6BAA6B,EAAE,iBAAiB,CAAC;SAC1G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAZD,gCAYC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isRgbColorValidator from 'validator/lib/isRgbColor';\n\nexport const IS_RGB_COLOR = 'isRgbColor';\n\n/**\n * Check if the string is a rgb or rgba color.\n * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.\n * If given value is not a string, then it returns false.\n */\nexport function isRgbColor(value: unknown, includePercentValues?: boolean): boolean {\n  return typeof value === 'string' && isRgbColorValidator(value, includePercentValues);\n}\n\n/**\n * Check if the string is a rgb or rgba color.\n * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.\n * If given value is not a string, then it returns false.\n */\nexport function IsRgbColor(includePercentValues?: boolean, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_RGB_COLOR,\n      constraints: [includePercentValues],\n      validator: {\n        validate: (value, args): boolean => isRgbColor(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be RGB color', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
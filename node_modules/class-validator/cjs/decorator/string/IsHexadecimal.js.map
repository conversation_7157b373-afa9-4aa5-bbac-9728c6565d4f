{"version": 3, "file": "IsHexadecimal.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHexadecimal.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gFAAiE;AAEpD,QAAA,cAAc,GAAG,eAAe,CAAC;AAE9C;;;GAGG;AACH,SAAgB,aAAa,CAAC,KAAc;IAC1C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,uBAAsB,EAAC,KAAK,CAAC,CAAC;AACpE,CAAC;AAFD,sCAEC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,iBAAqC;IACjE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;YACxD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,wCAAwC,EACnE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,sCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHexadecimalValidator from 'validator/lib/isHexadecimal';\n\nexport const IS_HEXADECIMAL = 'isHexadecimal';\n\n/**\n * Checks if the string is a hexadecimal number.\n * If given value is not a string, then it returns false.\n */\nexport function isHexadecimal(value: unknown): boolean {\n  return typeof value === 'string' && isHexadecimalValidator(value);\n}\n\n/**\n * Checks if the string is a hexadecimal number.\n * If given value is not a string, then it returns false.\n */\nexport function IsHexadecimal(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HEXADECIMAL,\n      validator: {\n        validate: (value, args): boolean => isHexadecimal(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a hexadecimal number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
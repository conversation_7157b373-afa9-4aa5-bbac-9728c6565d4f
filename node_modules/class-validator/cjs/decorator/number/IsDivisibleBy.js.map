{"version": 3, "file": "IsDivisibleBy.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/IsDivisibleBy.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gFAAiE;AAEpD,QAAA,eAAe,GAAG,eAAe,CAAC;AAE/C;;GAEG;AACH,SAAgB,aAAa,CAAC,KAAc,EAAE,GAAW;IACvD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,IAAA,uBAAsB,EAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5G,CAAC;AAFD,sCAEC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,GAAW,EAAE,iBAAqC;IAC9E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,uBAAe;QACrB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,6CAA6C,EACxE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,sCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isDivisibleByValidator from 'validator/lib/isDivisibleBy';\n\nexport const IS_DIVISIBLE_BY = 'isDivisibleBy';\n\n/**\n * Checks if value is a number that's divisible by another.\n */\nexport function isDivisibleBy(value: unknown, num: number): boolean {\n  return typeof value === 'number' && typeof num === 'number' && isDivisibleByValidator(String(value), num);\n}\n\n/**\n * Checks if value is a number that's divisible by another.\n */\nexport function IsDivisibleBy(num: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DIVISIBLE_BY,\n      constraints: [num],\n      validator: {\n        validate: (value, args): boolean => isDivisibleBy(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be divisible by $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
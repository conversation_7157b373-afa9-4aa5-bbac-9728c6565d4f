{"version": 3, "file": "IsDefined.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsDefined.ts"], "names": [], "mappings": ";;;AACA,6CAAwD;AACxD,sEAAmE;AAEnE,oCAAoC;AACvB,QAAA,UAAU,GAAG,iCAAe,CAAC,UAAU,CAAC;AAErD;;GAEG;AACH,SAAgB,SAAS,CAAI,KAA2B;IACtD,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC;AAC/C,CAAC;AAFD,8BAEC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,iBAAqC;IAC7D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;YAC9C,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,2CAA2C,EACtE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,8BAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from './ValidateBy';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\n\n// isDefined is (yet) a special case\nexport const IS_DEFINED = ValidationTypes.IS_DEFINED;\n\n/**\n * Checks if value is defined (!== undefined, !== null).\n */\nexport function isDefined<T>(value: T | undefined | null): value is T {\n  return value !== undefined && value !== null;\n}\n\n/**\n * Checks if value is defined (!== undefined, !== null).\n */\nexport function IsDefined(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DEFINED,\n      validator: {\n        validate: (value): boolean => isDefined(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not be null or undefined',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
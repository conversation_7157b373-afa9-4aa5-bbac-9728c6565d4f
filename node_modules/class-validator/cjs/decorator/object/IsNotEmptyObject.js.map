{"version": 3, "file": "IsNotEmptyObject.js", "sourceRoot": "", "sources": ["../../../../src/decorator/object/IsNotEmptyObject.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAChE,sDAAmD;AAEtC,QAAA,mBAAmB,GAAG,kBAAkB,CAAC;AAEtD;;;GAGG;AACH,SAAgB,gBAAgB,CAAC,KAAc,EAAE,OAAgC;IAC/E,IAAI,CAAC,IAAA,mBAAQ,EAAC,KAAK,CAAC,EAAE,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,MAAK,KAAK,EAAE,CAAC;QAChC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,SAAS,CAAC,CAAC;IAC7G,CAAC;IAED,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAhBD,4CAgBC;AAED;;;GAGG;AACH,SAAgB,gBAAgB,CAC9B,OAAgC,EAChC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,2BAAmB;QACzB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACjF,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,sCAAsC,EACjE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,4CAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport { isObject } from '../typechecker/IsObject';\n\nexport const IS_NOT_EMPTY_OBJECT = 'isNotEmptyObject';\n\n/**\n * Checks if the value is valid Object & not empty.\n * Returns false if the value is not an object or an empty valid object.\n */\nexport function isNotEmptyObject(value: unknown, options?: { nullable?: boolean }): boolean {\n  if (!isObject(value)) {\n    return false;\n  }\n\n  if (options?.nullable === false) {\n    return !Object.values(value).every(propertyValue => propertyValue === null || propertyValue === undefined);\n  }\n\n  for (const key in value) {\n    if (value.hasOwnProperty(key)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Checks if the value is valid Object & not empty.\n * Returns false if the value is not an object or an empty valid object.\n */\nexport function IsNotEmptyObject(\n  options?: { nullable?: boolean },\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NOT_EMPTY_OBJECT,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isNotEmptyObject(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a non-empty object',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).ClassValidator={})}(this,(function(t){"use strict";var e=function(t){this.groups=[],this.each=!1,this.context=void 0,this.type=t.type,this.name=t.name,this.target=t.target,this.propertyName=t.propertyName,this.constraints=null==t?void 0:t.constraints,this.constraintCls=t.constraintCls,this.validationTypeOptions=t.validationTypeOptions,t.validationOptions&&(this.message=t.validationOptions.message,this.groups=t.validationOptions.groups,this.always=t.validationOptions.always,this.each=t.validationOptions.each,this.context=t.validationOptions.context)},d=function(){function t(){}return t.prototype.transform=function(t){var d=[];return Object.keys(t.properties).forEach((function(r){t.properties[r].forEach((function(n){var a={message:n.message,groups:n.groups,always:n.always,each:n.each},o={type:n.type,name:n.name,target:t.name,propertyName:r,constraints:n.constraints,validationTypeOptions:n.options,validationOptions:a};d.push(new e(o))}))})),d},t}();function r(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0}function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.then}var a=function(t){var e="function"==typeof Symbol&&Symbol.iterator,d=e&&t[e],r=0;if(d)return d.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o=function(t,e){var d="function"==typeof Symbol&&t[Symbol.iterator];if(!d)return t;var r,n,a=d.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(t){n={error:t}}finally{try{r&&!r.done&&(d=a.return)&&d.call(a)}finally{if(n)throw n.error}}return o},i=function(t,e,d){if(d||2===arguments.length)for(var r,n=0,a=e.length;n<a;n++)!r&&n in e||(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return t.concat(r||Array.prototype.slice.call(e))},u=function(){function t(){this.validationMetadatas=new Map,this.constraintMetadatas=new Map}return Object.defineProperty(t.prototype,"hasValidationMetaData",{get:function(){return!!this.validationMetadatas.size},enumerable:!1,configurable:!0}),t.prototype.addValidationSchema=function(t){var e=this;(new d).transform(t).forEach((function(t){return e.addValidationMetadata(t)}))},t.prototype.addValidationMetadata=function(t){var e=this.validationMetadatas.get(t.target);e?e.push(t):this.validationMetadatas.set(t.target,[t])},t.prototype.addConstraintMetadata=function(t){var e=this.constraintMetadatas.get(t.target);e?e.push(t):this.constraintMetadatas.set(t.target,[t])},t.prototype.groupByPropertyName=function(t){var e={};return t.forEach((function(t){e[t.propertyName]||(e[t.propertyName]=[]),e[t.propertyName].push(t)})),e},t.prototype.getTargetValidationMetadatas=function(t,e,d,r,n){var u,s,l=function(t){return void 0!==t.always?t.always:(!t.groups||!t.groups.length)&&d},c=function(t){return!(!r||n&&n.length||!t.groups||!t.groups.length)},f=(this.validationMetadatas.get(t)||[]).filter((function(d){return(d.target===t||d.target===e)&&(!!l(d)||!c(d)&&(!(n&&n.length>0)||d.groups&&!!d.groups.find((function(t){return-1!==n.indexOf(t)}))))})),$=[];try{for(var p=a(this.validationMetadatas.entries()),v=p.next();!v.done;v=p.next()){var y=o(v.value,2),m=y[0],g=y[1];t.prototype instanceof m&&$.push.apply($,i([],o(g),!1))}}catch(t){u={error:t}}finally{try{v&&!v.done&&(s=p.return)&&s.call(p)}finally{if(u)throw u.error}}var h=$.filter((function(e){return"string"!=typeof e.target&&(e.target!==t&&((!(e.target instanceof Function)||t.prototype instanceof e.target)&&(!!l(e)||!c(e)&&(!(n&&n.length>0)||e.groups&&!!e.groups.find((function(t){return-1!==n.indexOf(t)}))))))})),A=h.filter((function(t){return!f.find((function(e){return e.propertyName===t.propertyName&&e.type===t.type}))}));return f.concat(A)},t.prototype.getTargetValidatorConstraints=function(t){return this.constraintMetadatas.get(t)||[]},t}();function s(){var t=r();return t.classValidatorMetadataStorage||(t.classValidatorMetadataStorage=new u),t.classValidatorMetadataStorage}var l=function(){function t(){}return t.prototype.toString=function(t,e,d,r){var n=this;void 0===t&&(t=!1),void 0===e&&(e=!1),void 0===d&&(d=""),void 0===r&&(r=!1);var a=t?"[1m":"",o=t?"[22m":"",i=function(t){return" - property ".concat(a).concat(d).concat(t).concat(o," has failed the following constraints: ").concat(a).concat((r?Object.values:Object.keys)(null!==(e=n.constraints)&&void 0!==e?e:{}).join(", ")).concat(o," \n");var e};if(e){var u=Number.isInteger(+this.property)?"[".concat(this.property,"]"):"".concat(d?".":"").concat(this.property);return this.constraints?i(u):this.children?this.children.map((function(e){return e.toString(t,!0,"".concat(d).concat(u),r)})).join(""):""}return"An instance of ".concat(a).concat(this.target?this.target.constructor.name:"an object").concat(o," has failed the validation:\n")+(this.constraints?i(this.property):"")+(this.children?this.children.map((function(e){return e.toString(t,!0,n.property,r)})).join(""):"")},t}(),c=function(){function t(){}return t.isValid=function(t){var e=this;return"isValid"!==t&&"getMessage"!==t&&-1!==Object.keys(this).map((function(t){return e[t]})).indexOf(t)},t.CUSTOM_VALIDATION="customValidation",t.NESTED_VALIDATION="nestedValidation",t.PROMISE_VALIDATION="promiseValidation",t.CONDITIONAL_VALIDATION="conditionalValidation",t.WHITELIST="whitelistValidation",t.IS_DEFINED="isDefined",t}();var f,$,p=function(){function t(){}return t.replaceMessageSpecialTokens=function(t,e){var d;return t instanceof Function?d=t(e):"string"==typeof t&&(d=t),d&&Array.isArray(e.constraints)&&e.constraints.forEach((function(t,e){d=d.replace(new RegExp("\\$constraint".concat(e+1),"g"),function(t){return Array.isArray(t)?t.join(", "):("symbol"==typeof t&&(t=t.description),"".concat(t))}(t))})),d&&void 0!==e.value&&null!==e.value&&["string","boolean","number"].includes(typeof e.value)&&(d=d.replace(/\$value/g,e.value)),d&&(d=d.replace(/\$property/g,e.property)),d&&(d=d.replace(/\$target/g,e.targetName)),d},t}(),v=function(t,e){var d="function"==typeof Symbol&&t[Symbol.iterator];if(!d)return t;var r,n,a=d.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(t){n={error:t}}finally{try{r&&!r.done&&(d=a.return)&&d.call(a)}finally{if(n)throw n.error}}return o},y=function(){function t(t,e){this.validator=t,this.validatorOptions=e,this.awaitingPromises=[],this.ignoreAsyncValidations=!1,this.metadataStorage=s()}return t.prototype.execute=function(t,e,d){var r,n,a=this;this.metadataStorage.hasValidationMetaData||!0!==(null===(r=this.validatorOptions)||void 0===r?void 0:r.enableDebugMessages)||console.warn("No validation metadata found. No validation will be  performed. There are multiple possible reasons:\n  - There may be multiple class-validator versions installed. You will need to flatten your dependencies to fix the issue.\n  - This validation runs before any file with validation decorator was parsed by NodeJS.");var o=this.validatorOptions?this.validatorOptions.groups:void 0,i=this.validatorOptions&&this.validatorOptions.strictGroups||!1,u=this.validatorOptions&&this.validatorOptions.always||!1,s=void 0===(null===(n=this.validatorOptions)||void 0===n?void 0:n.forbidUnknownValues)||!1!==this.validatorOptions.forbidUnknownValues,f=this.metadataStorage.getTargetValidationMetadatas(t.constructor,e,u,i,o),$=this.metadataStorage.groupByPropertyName(f);if(s&&!f.length){var p=new l;return this.validatorOptions&&this.validatorOptions.validationError&&void 0!==this.validatorOptions.validationError.target&&!0!==this.validatorOptions.validationError.target||(p.target=t),p.value=void 0,p.property=void 0,p.children=[],p.constraints={unknownValue:"an unknown value was passed to the validate function"},void d.push(p)}this.validatorOptions&&this.validatorOptions.whitelist&&this.whitelist(t,$,d),Object.keys($).forEach((function(e){var r=t[e],n=$[e].filter((function(t){return t.type===c.IS_DEFINED})),o=$[e].filter((function(t){return t.type!==c.IS_DEFINED&&t.type!==c.WHITELIST}));r instanceof Promise&&o.find((function(t){return t.type===c.PROMISE_VALIDATION}))?a.awaitingPromises.push(r.then((function(r){a.performValidations(t,r,e,n,o,d)}))):a.performValidations(t,r,e,n,o,d)}))},t.prototype.whitelist=function(t,e,d){var r=this,n=[];Object.keys(t).forEach((function(t){e[t]&&0!==e[t].length||n.push(t)})),n.length>0&&(this.validatorOptions&&this.validatorOptions.forbidNonWhitelisted?n.forEach((function(e){var n,a=r.generateValidationError(t,t[e],e);a.constraints=((n={})[c.WHITELIST]="property ".concat(e," should not exist"),n),a.children=void 0,d.push(a)})):n.forEach((function(e){return delete t[e]})))},t.prototype.stripEmptyErrors=function(t){var e=this;return t.filter((function(t){if(t.children&&(t.children=e.stripEmptyErrors(t.children)),0===Object.keys(t.constraints).length){if(0===t.children.length)return!1;delete t.constraints}return!0}))},t.prototype.performValidations=function(t,e,d,r,n,a){var o=n.filter((function(t){return t.type===c.CUSTOM_VALIDATION})),i=n.filter((function(t){return t.type===c.NESTED_VALIDATION})),u=n.filter((function(t){return t.type===c.CONDITIONAL_VALIDATION})),s=this.generateValidationError(t,e,d);a.push(s),this.conditionalValidations(t,e,u)&&(this.customValidations(t,e,r,s),this.mapContexts(t,e,r,s),void 0===e&&this.validatorOptions&&!0===this.validatorOptions.skipUndefinedProperties||null===e&&this.validatorOptions&&!0===this.validatorOptions.skipNullProperties||null==e&&this.validatorOptions&&!0===this.validatorOptions.skipMissingProperties||(this.customValidations(t,e,o,s),this.nestedValidations(e,i,s),this.mapContexts(t,e,n,s),this.mapContexts(t,e,o,s)))},t.prototype.generateValidationError=function(t,e,d){var r=new l;return this.validatorOptions&&this.validatorOptions.validationError&&void 0!==this.validatorOptions.validationError.target&&!0!==this.validatorOptions.validationError.target||(r.target=t),this.validatorOptions&&this.validatorOptions.validationError&&void 0!==this.validatorOptions.validationError.value&&!0!==this.validatorOptions.validationError.value||(r.value=e),r.property=d,r.children=[],r.constraints={},r},t.prototype.conditionalValidations=function(t,e,d){return d.map((function(d){return d.constraints[0](t,e)})).reduce((function(t,e){return t&&e}),!0)},t.prototype.customValidations=function(t,e,d,r){var a=this;d.forEach((function(d){a.metadataStorage.getTargetValidatorConstraints(d.constraintCls).forEach((function(o){if(!(o.async&&a.ignoreAsyncValidations||a.validatorOptions&&a.validatorOptions.stopAtFirstError&&Object.keys(r.constraints||{}).length>0)){var i={targetName:t.constructor?t.constructor.name:void 0,property:d.propertyName,object:t,value:e,constraints:d.constraints};if(d.each&&(Array.isArray(e)||e instanceof Set||e instanceof Map)){var u,s=((u=e)instanceof Map?Array.from(u.values()):Array.isArray(u)?u:Array.from(u)).map((function(t){return o.instance.validate(t,i)}));if(s.some((function(t){return n(t)}))){var l=s.map((function(t){return n(t)?t:Promise.resolve(t)})),c=Promise.all(l).then((function(n){if(!n.every((function(t){return t}))){var i=v(a.createValidationError(t,e,d,o),2),u=i[0],s=i[1];r.constraints[u]=s,d.context&&(r.contexts||(r.contexts={}),r.contexts[u]=Object.assign(r.contexts[u]||{},d.context))}}));a.awaitingPromises.push(c)}else{if(!s.every((function(t){return t}))){var f=v(a.createValidationError(t,e,d,o),2);m=f[0],g=f[1];r.constraints[m]=g}}}else{var $=o.instance.validate(e,i);if(n($)){var p=$.then((function(n){if(!n){var i=v(a.createValidationError(t,e,d,o),2),u=i[0],s=i[1];r.constraints[u]=s,d.context&&(r.contexts||(r.contexts={}),r.contexts[u]=Object.assign(r.contexts[u]||{},d.context))}}));a.awaitingPromises.push(p)}else if(!$){var y=v(a.createValidationError(t,e,d,o),2),m=y[0],g=y[1];r.constraints[m]=g}}}}))}))},t.prototype.nestedValidations=function(t,e,d){var r=this;void 0!==t&&e.forEach((function(n){if((n.type===c.NESTED_VALIDATION||n.type===c.PROMISE_VALIDATION)&&!(r.validatorOptions&&r.validatorOptions.stopAtFirstError&&Object.keys(d.constraints||{}).length>0))if(Array.isArray(t)||t instanceof Set||t instanceof Map)(t instanceof Set?Array.from(t):t).forEach((function(n,a){r.performValidations(t,n,a.toString(),[],e,d.children)}));else if(t instanceof Object){var a="string"==typeof n.target?n.target:n.target.name;r.execute(t,a,d.children)}else{var o=v(r.createValidationError(n.target,t,n),2),i=o[0],u=o[1];d.constraints[i]=u}}))},t.prototype.mapContexts=function(t,e,d,r){var n=this;return d.forEach((function(t){if(t.context){var e=void 0;if(t.type===c.CUSTOM_VALIDATION)e=n.metadataStorage.getTargetValidatorConstraints(t.constraintCls)[0];var d=n.getConstraintType(t,e);r.constraints[d]&&(r.contexts||(r.contexts={}),r.contexts[d]=Object.assign(r.contexts[d]||{},t.context))}}))},t.prototype.createValidationError=function(t,e,d,r){var n=t.constructor?t.constructor.name:void 0,a=this.getConstraintType(d,r),o={targetName:n,property:d.propertyName,object:t,value:e,constraints:d.constraints},i=d.message||"";return d.message||this.validatorOptions&&(!this.validatorOptions||this.validatorOptions.dismissDefaultMessages)||r&&r.instance.defaultMessage instanceof Function&&(i=r.instance.defaultMessage(o)),[a,p.replaceMessageSpecialTokens(i,o)]},t.prototype.getConstraintType=function(t,e){return e&&e.name?e.name:t.type},t}(),m=function(t,e,d,r){return new(d||(d=Promise))((function(n,a){function o(t){try{u(r.next(t))}catch(t){a(t)}}function i(t){try{u(r.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof d?e:new d((function(t){t(e)}))).then(o,i)}u((r=r.apply(t,e||[])).next())}))},g=function(t,e){var d,r,n,a,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return a={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function i(i){return function(u){return function(i){if(d)throw new TypeError("Generator is already executing.");for(;a&&(a=0,i[0]&&(o=0)),o;)try{if(d=1,r&&(n=2&i[0]?r.return:i[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,i[1])).done)return n;switch(r=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,r=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!(n=o.trys,(n=n.length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){o=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){o.label=i[1];break}if(6===i[0]&&o.label<n[1]){o.label=n[1],n=i;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(i);break}n[2]&&o.ops.pop(),o.trys.pop();continue}i=e.call(t,o)}catch(t){i=[6,t],r=0}finally{d=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},h=function(){function t(){}return t.prototype.validate=function(t,e,d){return this.coreValidate(t,e,d)},t.prototype.validateOrReject=function(t,e,d){return m(this,void 0,void 0,(function(){var r;return g(this,(function(n){switch(n.label){case 0:return[4,this.coreValidate(t,e,d)];case 1:return(r=n.sent()).length?[2,Promise.reject(r)]:[2]}}))}))},t.prototype.validateSync=function(t,e,d){var r="string"==typeof t?e:t,n="string"==typeof t?t:void 0,a=new y(this,"string"==typeof t?d:e);a.ignoreAsyncValidations=!0;var o=[];return a.execute(r,n,o),a.stripEmptyErrors(o)},t.prototype.coreValidate=function(t,e,d){var r="string"==typeof t?e:t,n="string"==typeof t?t:void 0,a=new y(this,"string"==typeof t?d:e),o=[];return a.execute(r,n,o),Promise.all(a.awaitingPromises).then((function(){return a.stripEmptyErrors(o)}))},t}(),A=new(function(){function t(){this.instances=[]}return t.prototype.get=function(t){var e=this.instances.find((function(e){return e.type===t}));return e||(e={type:t,object:new t},this.instances.push(e)),e.object},t}());function b(t){if(f)try{var e=f.get(t);if(e)return e;if(!$||!$.fallback)return e}catch(t){if(!$||!$.fallbackOnErrors)throw t}return A.get(t)}var I=function(){function t(t,e,d){void 0===d&&(d=!1),this.target=t,this.name=e,this.async=d}return Object.defineProperty(t.prototype,"instance",{get:function(){return b(this.target)},enumerable:!1,configurable:!0}),t}();function M(t){var d;if(t.validator instanceof Function){if(d=t.validator,b(u).getTargetValidatorConstraints(t.validator).length>1)throw"More than one implementation of ValidatorConstraintInterface found for validator on: ".concat(t.target.name,":").concat(t.propertyName)}else{var r=t.validator;d=function(){function t(){}return t.prototype.validate=function(t,e){return r.validate(t,e)},t.prototype.defaultMessage=function(t){return r.defaultMessage?r.defaultMessage(t):""},t}(),s().addConstraintMetadata(new I(d,t.name,t.async))}var n={type:t.name&&c.isValid(t.name)?t.name:c.CUSTOM_VALIDATION,name:t.name,target:t.target,propertyName:t.propertyName,validationOptions:t.options,constraintCls:d,constraints:t.constraints};s().addValidationMetadata(new e(n))}function S(t,e){return function(d){var r=e&&e.each?"each value in ":"";return t(r,d)}}function _(t,e){return function(d,r){M({name:t.name,target:d.constructor,propertyName:r,options:e,constraints:t.constraints,validator:t.validator})}}var O=c.IS_DEFINED;function x(t){return null!=t}var E="isOptional";var C=function(){return C=Object.assign||function(t){for(var e,d=1,r=arguments.length;d<r;d++)for(var n in e=arguments[d])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},C.apply(this,arguments)};function N(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var P={exports:{}},L={exports:{}};!function(t,e){function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(!("string"==typeof t||t instanceof String)){var e=d(t);throw null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a ".concat(e))}},t.exports=e.default,t.exports.default=e.default}(L,L.exports);var R=L.exports,T={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;for(var d in e)void 0===t[d]&&(t[d]=e[d]);return t},t.exports=e.default,t.exports.default=e.default}(T,T.exports);var D=T.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e=(0,r.default)(e,s),!t.includes(","))return!1;var n=t.split(",");if(n[0].startsWith("(")&&!n[1].endsWith(")")||n[1].endsWith(")")&&!n[0].startsWith("("))return!1;if(e.checkDMS)return i.test(n[0])&&u.test(n[1]);return a.test(n[0])&&o.test(n[1])};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}var a=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,o=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,i=/^(([1-8]?\d)\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|90\D+0\D+0)\D+[NSns]?$/i,u=/^\s*([1-7]?\d{1,2}\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|180\D+0\D+0)\D+[EWew]?$/i,s={checkDMS:!1};t.exports=e.default,t.exports.default=e.default}(P,P.exports);var w=P.exports,B=N(w),F="isLatLong";function Z(t){return"string"==typeof t&&B(t)}var G="isLatitude";function U(t){return("number"==typeof t||"string"==typeof t)&&Z("".concat(t,",0"))}var j="isLongitude";function H(t){return("number"==typeof t||"string"==typeof t)&&Z("0,".concat(t))}var V="equals";function K(t,e){return t===e}var k="notEquals";function W(t,e){return t!==e}var Y="isEmpty";function X(t){return""===t||null==t}var z="isNotEmpty";function J(t){return""!==t&&null!=t}var q="isIn";function Q(t,e){return Array.isArray(e)&&e.some((function(e){return e===t}))}var tt="isNotIn";function et(t,e){return!Array.isArray(e)||!e.some((function(e){return e===t}))}var dt={exports:{}},rt={exports:{}},nt={},at={};Object.defineProperty(at,"__esModule",{value:!0}),at.commaDecimal=at.dotDecimal=at.bengaliLocales=at.farsiLocales=at.arabicLocales=at.englishLocales=at.decimal=at.alphanumeric=at.alpha=void 0;var ot={"en-US":/^[A-Z]+$/i,"az-AZ":/^[A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ώ]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fa-IR":/^[ابپتثجچحخدذرزژسشصضطظعغفقکگلمنوهی]+$/i,"fi-FI":/^[A-ZÅÄÖ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๐\s]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"vi-VN":/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,"ko-KR":/^[ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[א-ת]+$/,fa:/^['آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,"hi-IN":/^[\u0900-\u0961]+[\u0972-\u097F]*$/i,"si-LK":/^[\u0D80-\u0DFF]+$/};at.alpha=ot;var it={"en-US":/^[0-9A-Z]+$/i,"az-AZ":/^[0-9A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fi-FI":/^[0-9A-ZÅÄÖ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[0-9０-９ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๙\s]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ko-KR":/^[0-9ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,"vi-VN":/^[0-9A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[0-9א-ת]+$/,fa:/^['0-9آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی۱۲۳۴۵۶۷۸۹۰']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣ০১২৩৪৫৬৭৮৯ৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,"hi-IN":/^[\u0900-\u0963]+[\u0966-\u097F]*$/i,"si-LK":/^[0-9\u0D80-\u0DFF]+$/};at.alphanumeric=it;var ut={"en-US":".",ar:"٫"};at.decimal=ut;var st=["AU","GB","HK","IN","NZ","ZA","ZM"];at.englishLocales=st;for(var lt,ct=0;ct<st.length;ct++)ot[lt="en-".concat(st[ct])]=ot["en-US"],it[lt]=it["en-US"],ut[lt]=ut["en-US"];var ft=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"];at.arabicLocales=ft;for(var $t,pt=0;pt<ft.length;pt++)ot[$t="ar-".concat(ft[pt])]=ot.ar,it[$t]=it.ar,ut[$t]=ut.ar;var vt=["IR","AF"];at.farsiLocales=vt;for(var yt,mt=0;mt<vt.length;mt++)it[yt="fa-".concat(vt[mt])]=it.fa,ut[yt]=ut.ar;var gt=["BD","IN"];at.bengaliLocales=gt;for(var ht,At=0;At<gt.length;At++)ot[ht="bn-".concat(gt[At])]=ot.bn,it[ht]=it.bn,ut[ht]=ut["en-US"];var bt=["ar-EG","ar-LB","ar-LY"];at.dotDecimal=bt;var It=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","es-ES","fr-CA","fr-FR","id-ID","it-IT","ku-IQ","hi-IN","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","si-LK","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA","vi-VN"];at.commaDecimal=It;for(var Mt=0;Mt<bt.length;Mt++)ut[bt[Mt]]=ut["en-US"];for(var St=0;St<It.length;St++)ut[It[St]]=",";ot["fr-CA"]=ot["fr-FR"],it["fr-CA"]=it["fr-FR"],ot["pt-BR"]=ot["pt-PT"],it["pt-BR"]=it["pt-PT"],ut["pt-BR"]=ut["pt-PT"],ot["pl-Pl"]=ot["pl-PL"],it["pl-Pl"]=it["pl-PL"],ut["pl-Pl"]=ut["pl-PL"],ot["fa-AF"]=ot.fa,Object.defineProperty(nt,"__esModule",{value:!0}),nt.default=function(t,e){(0,Ot.default)(t),e=e||{};var d=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?xt.decimal[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));if(""===t||"."===t||","===t||"-"===t||"+"===t)return!1;var r=parseFloat(t.replace(",","."));return d.test(t)&&(!e.hasOwnProperty("min")||r>=e.min)&&(!e.hasOwnProperty("max")||r<=e.max)&&(!e.hasOwnProperty("lt")||r<e.lt)&&(!e.hasOwnProperty("gt")||r>e.gt)},nt.locales=void 0;var _t,Ot=(_t=R)&&_t.__esModule?_t:{default:_t},xt=at;var Et=Object.keys(xt.decimal);nt.locales=Et,function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t)?parseFloat(t):NaN};var d=function(t){return t&&t.__esModule?t:{default:t}}(nt);t.exports=e.default,t.exports.default=e.default}(rt,rt.exports);var Ct=rt.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),(0,r.default)(t)%parseInt(e,10)==0};var d=n(R),r=n(Ct);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(dt,dt.exports);var Nt=dt.exports,Pt=N(Nt),Lt="isDivisibleBy";function Rt(t,e){return"number"==typeof t&&"number"==typeof e&&Pt(String(t),e)}var Tt="isPositive";function Dt(t){return"number"==typeof t&&t>0}var wt="isNegative";function Bt(t){return"number"==typeof t&&t<0}function Ft(t,e){return"number"==typeof t&&"number"==typeof e&&t<=e}function Zt(t,e){return"number"==typeof t&&"number"==typeof e&&t>=e}var Gt="minDate";function Ut(t,e){return t instanceof Date&&t.getTime()>=(e instanceof Date?e:e()).getTime()}var jt="maxDate";function Ht(t,e){return t instanceof Date&&t.getTime()<=(e instanceof Date?e:e()).getTime()}var Vt={exports:{}},Kt={exports:{}};!function(t,e){function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){"object"===d(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t="");return String(t)},t.exports=e.default,t.exports.default=e.default}(Kt,Kt.exports);var kt=Kt.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,a){if((0,d.default)(t),(a=(0,n.default)(a,o)).ignoreCase)return t.toLowerCase().split((0,r.default)(e).toLowerCase()).length>a.minOccurrences;return t.split((0,r.default)(e)).length>a.minOccurrences};var d=a(R),r=a(kt),n=a(D);function a(t){return t&&t.__esModule?t:{default:t}}var o={ignoreCase:!1,minOccurrences:1};t.exports=e.default,t.exports.default=e.default}(Vt,Vt.exports);var Wt=Vt.exports,Yt=N(Wt),Xt="contains";function zt(t,e){return"string"==typeof t&&Yt(t,e)}var Jt="notContains";function qt(t,e){return"string"==typeof t&&!Yt(t,e)}var Qt={};Object.defineProperty(Qt,"__esModule",{value:!0});var te=Qt.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,ee.default)(t);var r=t,n=d.ignore;if(n)if(n instanceof RegExp)r=r.replace(n,"");else{if("string"!=typeof n)throw new Error("ignore should be instance of a String or RegExp");r=r.replace(new RegExp("[".concat(n.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in de.alpha)return de.alpha[e].test(r);throw new Error("Invalid locale '".concat(e,"'"))};Qt.locales=void 0;var ee=function(t){return t&&t.__esModule?t:{default:t}}(R),de=at;var re=Object.keys(de.alpha);Qt.locales=re;var ne="isAlpha";function ae(t,e){return"string"==typeof t&&te(t,e)}var oe={};Object.defineProperty(oe,"__esModule",{value:!0});var ie=oe.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,ue.default)(t);var r=t,n=d.ignore;if(n)if(n instanceof RegExp)r=r.replace(n,"");else{if("string"!=typeof n)throw new Error("ignore should be instance of a String or RegExp");r=r.replace(new RegExp("[".concat(n.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in se.alphanumeric)return se.alphanumeric[e].test(r);throw new Error("Invalid locale '".concat(e,"'"))};oe.locales=void 0;var ue=function(t){return t&&t.__esModule?t:{default:t}}(R),se=at;var le=Object.keys(se.alphanumeric);oe.locales=le;var ce="isAlphanumeric";function fe(t,e){return"string"==typeof t&&ie(t,e)}var $e={exports:{}},pe={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var d=function(t,e){return t.some((function(t){return e===t}))};e.default=d,t.exports=e.default,t.exports.default=e.default}(pe,pe.exports);var ve=pe.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,r.default)(t),(e=(0,d.default)(e,i)).locale in a.decimal)return!(0,n.default)(u,t.replace(/ /g,""))&&function(t){return new RegExp("^[-+]?([0-9]+)?(\\".concat(a.decimal[t.locale],"[0-9]{").concat(t.decimal_digits,"})").concat(t.force_decimal?"":"?","$"))}(e).test(t);throw new Error("Invalid locale '".concat(e.locale,"'"))};var d=o(D),r=o(R),n=o(ve),a=at;function o(t){return t&&t.__esModule?t:{default:t}}var i={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},u=["","-","+"];t.exports=e.default,t.exports.default=e.default}($e,$e.exports);var ye=$e.exports,me=N(ye),ge="isDecimal";function he(t,e){return"string"==typeof t&&me(t,e)}var Ae={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[\x00-\x7F]+$/;t.exports=e.default,t.exports.default=e.default}(Ae,Ae.exports);var be=Ae.exports,Ie=N(be),Me="isAscii";function Se(t){return"string"==typeof t&&Ie(t)}var _e={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t),e=(0,r.default)(e,i);var n=t.length;if(e.urlSafe)return o.test(t);if(n%4!=0||a.test(t))return!1;var u=t.indexOf("=");return-1===u||u===n-1||u===n-2&&"="===t[n-1]};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}var a=/[^A-Z0-9+\/=]/i,o=/^[A-Z0-9_\-]*$/i,i={urlSafe:!1};t.exports=e.default,t.exports.default=e.default}(_e,_e.exports);var Oe=_e.exports,xe=N(Oe),Ee="isBase64";function Ce(t,e){return"string"==typeof t&&xe(t,e)}var Ne={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var n,a;(0,d.default)(t),"object"===r(e)?(n=e.min||0,a=e.max):(n=arguments[1],a=arguments[2]);var o=encodeURI(t).split(/%..|./).length-1;return o>=n&&(void 0===a||o<=a)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}t.exports=e.default,t.exports.default=e.default}(Ne,Ne.exports);var Pe=Ne.exports,Le=N(Pe),Re="isByteLength";function Te(t,e,d){return"string"==typeof t&&Le(t,{min:e,max:d})}var De={exports:{}},we={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);for(var e,r,n,a=t.replace(/[- ]+/g,""),o=0,i=a.length-1;i>=0;i--)e=a.substring(i,i+1),r=parseInt(e,10),o+=n&&(r*=2)>=10?r%10+1:r,n=!n;return!(o%10!=0||!a)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(we,we.exports);var Be=we.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,d.default)(t);var n=e.provider,i=t.replace(/[- ]+/g,"");if(n&&n.toLowerCase()in a){if(!a[n.toLowerCase()].test(i))return!1}else{if(n&&!(n.toLowerCase()in a))throw new Error("".concat(n," is not a valid credit card provider."));if(!o.test(i))return!1}return(0,r.default)(t)};var d=n(R),r=n(Be);function n(t){return t&&t.__esModule?t:{default:t}}var a={amex:/^3[47][0-9]{13}$/,dinersclub:/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,discover:/^6(?:011|5[0-9][0-9])[0-9]{12,15}$/,jcb:/^(?:2131|1800|35\d{3})\d{11}$/,mastercard:/^5[1-5][0-9]{2}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}$/,unionpay:/^(6[27][0-9]{14}|^(81[0-9]{14,17}))$/,visa:/^(?:4[0-9]{12})(?:[0-9]{3,6})?$/},o=/^(?:4[0-9]{12}(?:[0-9]{3,6})?|5[1-5][0-9]{14}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}|6(?:011|5[0-9][0-9])[0-9]{12,15}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11}|6[27][0-9]{14}|^(81[0-9]{14,17}))$/;t.exports=e.default,t.exports.default=e.default}(De,De.exports);var Fe=De.exports,Ze=N(Fe),Ge="isCreditCard";function Ue(t){return"string"==typeof t&&Ze(t)}var je={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,r.default)(t),function(t){var e="\\d{".concat(t.digits_after_decimal[0],"}");t.digits_after_decimal.forEach((function(t,d){0!==d&&(e="".concat(e,"|\\d{").concat(t,"}"))}));var d="(".concat(t.symbol.replace(/\W/,(function(t){return"\\".concat(t)})),")").concat(t.require_symbol?"":"?"),r="-?",n="[1-9]\\d{0,2}(\\".concat(t.thousands_separator,"\\d{3})*"),a="(".concat(["0","[1-9]\\d*",n].join("|"),")?"),o="(\\".concat(t.decimal_separator,"(").concat(e,"))").concat(t.require_decimal?"":"?"),i=a+(t.allow_decimal||t.require_decimal?o:"");t.allow_negatives&&!t.parens_for_negatives&&(t.negative_sign_after_digits?i+=r:t.negative_sign_before_digits&&(i=r+i));t.allow_negative_sign_placeholder?i="( (?!\\-))?".concat(i):t.allow_space_after_symbol?i=" ?".concat(i):t.allow_space_after_digits&&(i+="( (?!$))?");t.symbol_after_digits?i+=d:i=d+i;t.allow_negatives&&(t.parens_for_negatives?i="(\\(".concat(i,"\\)|").concat(i,")"):t.negative_sign_before_digits||t.negative_sign_after_digits||(i=r+i));return new RegExp("^(?!-? )(?=.*\\d)".concat(i,"$"))}(e=(0,d.default)(e,a)).test(t)};var d=n(D),r=n(R);function n(t){return t&&t.__esModule?t:{default:t}}var a={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};t.exports=e.default,t.exports.default=e.default}(je,je.exports);var He=je.exports,Ve=N(He),Ke="isCurrency";function ke(t,e){return"string"==typeof t&&Ve(t,e)}var We={exports:{}},Ye={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t),(e=(0,r.default)(e,a)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));!0===e.allow_wildcard&&0===t.indexOf("*.")&&(t=t.substring(2));var n=t.split("."),o=n[n.length-1];if(e.require_tld){if(n.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(o))return!1;if(/\s/.test(o))return!1}if(!e.allow_numeric_tld&&/^\d+$/.test(o))return!1;return n.every((function(t){return!(t.length>63&&!e.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)&&(!/[\uff01-\uff5e]/.test(t)&&(!/^-|-$/.test(t)&&!(!e.allow_underscores&&/_/.test(t)))))}))};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}var a={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};t.exports=e.default,t.exports.default=e.default}(Ye,Ye.exports);var Xe=Ye.exports,ze=N(Xe),Je={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if((0,d.default)(e),!(r=String(r)))return t(e,4)||t(e,6);if("4"===r)return a.test(e);if("6"===r)return i.test(e);return!1};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",n="(".concat(r,"[.]){3}").concat(r),a=new RegExp("^".concat(n,"$")),o="(?:[0-9a-fA-F]{1,4})",i=new RegExp("^("+"(?:".concat(o,":){7}(?:").concat(o,"|:)|")+"(?:".concat(o,":){6}(?:").concat(n,"|:").concat(o,"|:)|")+"(?:".concat(o,":){5}(?::").concat(n,"|(:").concat(o,"){1,2}|:)|")+"(?:".concat(o,":){4}(?:(:").concat(o,"){0,1}:").concat(n,"|(:").concat(o,"){1,3}|:)|")+"(?:".concat(o,":){3}(?:(:").concat(o,"){0,2}:").concat(n,"|(:").concat(o,"){1,4}|:)|")+"(?:".concat(o,":){2}(?:(:").concat(o,"){0,3}:").concat(n,"|(:").concat(o,"){1,5}|:)|")+"(?:".concat(o,":){1}(?:(:").concat(o,"){0,4}:").concat(n,"|(:").concat(o,"){1,6}|:)|")+"(?::((?::".concat(o,"){0,5}:").concat(n,"|(?::").concat(o,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");t.exports=e.default,t.exports.default=e.default}(Je,Je.exports);var qe=Je.exports,Qe=N(qe);!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),(e=(0,r.default)(e,u)).require_display_name||e.allow_display_name){var i=t.match(s);if(i){var v=i[1];if(t=t.replace(v,"").replace(/(^<|>$)/g,""),v.endsWith(" ")&&(v=v.slice(0,-1)),!function(t){var e=t.replace(/^"(.+)"$/,"$1");if(!e.trim())return!1;if(/[\.";<>]/.test(e)){if(e===t)return!1;if(!(e.split('"').length===e.split('\\"').length))return!1}return!0}(v))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&t.length>254)return!1;var y=t.split("@"),m=y.pop(),g=m.toLowerCase();if(e.host_blacklist.includes(g))return!1;if(e.host_whitelist.length>0&&!e.host_whitelist.includes(g))return!1;var h=y.join("@");if(e.domain_specific_validation&&("gmail.com"===g||"googlemail.com"===g)){var A=(h=h.toLowerCase()).split("+")[0];if(!(0,n.default)(A.replace(/\./g,""),{min:6,max:30}))return!1;for(var b=A.split("."),I=0;I<b.length;I++)if(!c.test(b[I]))return!1}if(!(!1!==e.ignore_max_length||(0,n.default)(h,{max:64})&&(0,n.default)(m,{max:254})))return!1;if(!(0,a.default)(m,{require_tld:e.require_tld,ignore_max_length:e.ignore_max_length})){if(!e.allow_ip_domain)return!1;if(!(0,o.default)(m)){if(!m.startsWith("[")||!m.endsWith("]"))return!1;var M=m.slice(1,-1);if(0===M.length||!(0,o.default)(M))return!1}}if('"'===h[0])return h=h.slice(1,h.length-1),e.allow_utf8_local_part?p.test(h):f.test(h);for(var S=e.allow_utf8_local_part?$:l,_=h.split("."),O=0;O<_.length;O++)if(!S.test(_[O]))return!1;if(e.blacklisted_chars&&-1!==h.search(new RegExp("[".concat(e.blacklisted_chars,"]+"),"g")))return!1;return!0};var d=i(R),r=i(D),n=i(Pe),a=i(Xe),o=i(qe);function i(t){return t&&t.__esModule?t:{default:t}}var u={allow_display_name:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},s=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,l=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,c=/^[a-z\d]+$/,f=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,$=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,p=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i;t.exports=e.default,t.exports.default=e.default}(We,We.exports);var td=We.exports,ed=N(td),dd="isEmail";function rd(t,e){return"string"==typeof t&&ed(t,e)}var nd="isFqdn";function ad(t,e){return"string"==typeof t&&ze(t,e)}var od={};Object.defineProperty(od,"__esModule",{value:!0});var id=od.default=function(t){return(0,ud.default)(t),sd.test(t)};od.fullWidth=void 0;var ud=function(t){return t&&t.__esModule?t:{default:t}}(R);var sd=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;od.fullWidth=sd;var ld="isFullWidth";function cd(t){return"string"==typeof t&&id(t)}var fd={};Object.defineProperty(fd,"__esModule",{value:!0});var $d=fd.default=function(t){return(0,pd.default)(t),vd.test(t)};fd.halfWidth=void 0;var pd=function(t){return t&&t.__esModule?t:{default:t}}(R);var vd=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;fd.halfWidth=vd;var yd="isHalfWidth";function md(t){return"string"==typeof t&&$d(t)}var gd={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.fullWidth.test(t)&&n.halfWidth.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R),r=od,n=fd;t.exports=e.default,t.exports.default=e.default}(gd,gd.exports);var hd=gd.exports,Ad=N(hd),bd="isVariableWidth";function Id(t){return"string"==typeof t&&Ad(t)}var Md={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^#?([0-9A-F]{3}|[0-9A-F]{4}|[0-9A-F]{6}|[0-9A-F]{8})$/i;t.exports=e.default,t.exports.default=e.default}(Md,Md.exports);var Sd=Md.exports,_d=N(Sd),Od="isHexColor";function xd(t){return"string"==typeof t&&_d(t)}var Ed={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(0x|0h)?[0-9A-F]+$/i;t.exports=e.default,t.exports.default=e.default}(Ed,Ed.exports);var Cd=Ed.exports,Nd=N(Cd),Pd="isHexadecimal";function Ld(t){return"string"==typeof t&&Nd(t)}function Rd(t){return!!t&&("each"in t||"message"in t||"groups"in t||"always"in t||"context"in t)}var Td={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e,s){(0,d.default)(e),null!=s&&s.eui&&(s.eui=String(s.eui));if(null!=s&&s.no_colons||null!=s&&s.no_separators)return"48"===s.eui?n.test(e):"64"===s.eui?i.test(e):n.test(e)||i.test(e);if("48"===(null==s?void 0:s.eui))return r.test(e)||a.test(e);if("64"===(null==s?void 0:s.eui))return o.test(e)||u.test(e);return t(e,{eui:"48"})||t(e,{eui:"64"})};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){4}([0-9a-fA-F]{2})$/,n=/^([0-9a-fA-F]){12}$/,a=/^([0-9a-fA-F]{4}\.){2}([0-9a-fA-F]{4})$/,o=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){6}([0-9a-fA-F]{2})$/,i=/^([0-9a-fA-F]){16}$/,u=/^([0-9a-fA-F]{4}\.){3}([0-9a-fA-F]{4})$/;t.exports=e.default,t.exports.default=e.default}(Td,Td.exports);var Dd=Td.exports,wd=N(Dd),Bd="isMacAddress";function Fd(t,e){return"string"==typeof t&&wd(t,e)}var Zd="isIp";function Gd(t,e){var d=e?"".concat(e):void 0;return"string"==typeof t&&Qe(t,d)}var Ud={exports:{}},jd={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var a=(e=e||{}).hasOwnProperty("allow_leading_zeroes")&&!e.allow_leading_zeroes?r:n,o=!e.hasOwnProperty("min")||t>=e.min,i=!e.hasOwnProperty("max")||t<=e.max,u=!e.hasOwnProperty("lt")||t<e.lt,s=!e.hasOwnProperty("gt")||t>e.gt;return a.test(t)&&o&&i&&u&&s};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,n=/^[-+]?[0-9]+$/;t.exports=e.default,t.exports.default=e.default}(jd,jd.exports);var Hd=jd.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t,{min:0,max:65535})};var d=function(t){return t&&t.__esModule?t:{default:t}}(Hd);t.exports=e.default,t.exports.default=e.default}(Ud,Ud.exports);var Vd=Ud.exports,Kd=N(Vd),kd="isPort";function Wd(t){return"string"==typeof t&&Kd(t)}var Yd={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e,o){(0,d.default)(e);var i=String((null==o?void 0:o.version)||o);if(!(null!=o&&o.version||o))return t(e,{version:10})||t(e,{version:13});var u=e.replace(/[\s-]+/g,""),s=0;if("10"===i){if(!r.test(u))return!1;for(var l=0;l<i-1;l++)s+=(l+1)*u.charAt(l);if("X"===u.charAt(9)?s+=100:s+=10*u.charAt(9),s%11==0)return!0}else if("13"===i){if(!n.test(u))return!1;for(var c=0;c<12;c++)s+=a[c%2]*u.charAt(c);if(u.charAt(12)-(10-s%10)%10==0)return!0}return!1};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(?:[0-9]{9}X|[0-9]{10})$/,n=/^(?:[0-9]{13})$/,a=[1,3];t.exports=e.default,t.exports.default=e.default}(Yd,Yd.exports);var Xd=Yd.exports,zd=N(Xd),Jd="isIsbn";function qd(t,e){var d=e?"".concat(e):void 0;return"string"==typeof t&&zd(t,d)}var Qd={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if((0,d.default)(t),!r.test(t))return!1;for(var e=!0,n=0,a=t.length-2;a>=0;a--)if(t[a]>="A"&&t[a]<="Z")for(var o=t[a].charCodeAt(0)-55,i=o%10,u=Math.trunc(o/10),s=0,l=[i,u];s<l.length;s++){var c=l[s];n+=e?c>=5?1+2*(c-5):2*c:c,e=!e}else{var f=t[a].charCodeAt(0)-"0".charCodeAt(0);n+=e?f>=5?1+2*(f-5):2*f:f,e=!e}var $=10*Math.trunc((n+9)/10)-n;return+t[t.length-1]===$};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;t.exports=e.default,t.exports.default=e.default}(Qd,Qd.exports);var tr=Qd.exports,er=N(tr),dr="isIsin";function rr(t){return"string"==typeof t&&er(t)}var nr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,d.default)(t);var o=e.strictSeparator?n.test(t):r.test(t);return o&&e.strict?a(t):o};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,n=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,a=function(t){var e=t.match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/);if(e){var d=Number(e[1]),r=Number(e[2]);return d%4==0&&d%100!=0||d%400==0?r<=366:r<=365}var n=t.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number),a=n[1],o=n[2],i=n[3],u=o?"0".concat(o).slice(-2):o,s=i?"0".concat(i).slice(-2):i,l=new Date("".concat(a,"-").concat(u||"01","-").concat(s||"01"));return!o||!i||l.getUTCFullYear()===a&&l.getUTCMonth()+1===o&&l.getUTCDate()===i};t.exports=e.default,t.exports.default=e.default}(nr,nr.exports);var ar=nr.exports,or=N(ar),ir="isIso8601";function ur(t,e){return"string"==typeof t&&or(t,e)}var sr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);try{e=(0,r.default)(e,o);var n=[];e.allow_primitives&&(n=[null,!1,!0]);var i=JSON.parse(t);return n.includes(i)||!!i&&"object"===a(i)}catch(t){}return!1};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var o={allow_primitives:!1};t.exports=e.default,t.exports.default=e.default}(sr,sr.exports);var lr=sr.exports,cr=N(lr),fr="isJson";function $r(t){return"string"==typeof t&&cr(t)}var pr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);var e=t.split("."),n=e.length;if(n>3||n<2)return!1;return e.reduce((function(t,e){return t&&(0,r.default)(e,{urlSafe:!0})}),!0)};var d=n(R),r=n(Oe);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(pr,pr.exports);var vr=pr.exports,yr=N(vr),mr="isJwt";function gr(t){return"string"==typeof t&&yr(t)}var hr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),t===t.toLowerCase()};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(hr,hr.exports);var Ar=hr.exports,br=N(Ar),Ir="isLowercase";function Mr(t){return"string"==typeof t&&br(t)}var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0});var _r=Sr.default=function(t,e,d){if((0,Or.default)(t),d&&d.strictMode&&!t.startsWith("+"))return!1;if(Array.isArray(e))return e.some((function(e){if(xr.hasOwnProperty(e)&&xr[e].test(t))return!0;return!1}));if(e in xr)return xr[e].test(t);if(!e||"any"===e){for(var r in xr){if(xr.hasOwnProperty(r))if(xr[r].test(t))return!0}return!1}throw new Error("Invalid locale '".concat(e,"'"))};Sr.locales=void 0;var Or=function(t){return t&&t.__esModule?t:{default:t}}(R);var xr={"am-AM":/^(\+?374|0)((10|[9|7][0-9])\d{6}$|[2-4]\d{7}$)/,"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-LB":/^(\+?961)?((3|81)\d{6}|7\d{7})$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)([569]\d{7}|41\d{6})$/,"ar-LY":/^((\+?218)|0)?(9[1-6]\d{7}|[1-8]\d{7,9})$/,"ar-MA":/^(?:(?:\+|00)212|0)[5-7]\d{8}$/,"ar-OM":/^((\+|00)968)?(9[1-9])\d{6}$/,"ar-PS":/^(\+?970|0)5[6|9](\d{7})$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"az-AZ":/^(\+994|0)(10|5[015]|7[07]|99)\d{7}$/,"bs-BA":/^((((\+|00)3876)|06))((([0-3]|[5-6])\d{6})|(4\d{7}))$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[13456789][0-9]{8}$/,"ca-AD":/^(\+376)?[346]\d{5}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^((\+49|0)1)(5[0-25-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7,9}$/,"de-AT":/^(\+43|0)\d{1,4}\d{3,12}$/,"de-CH":/^(\+41|0)([1-9])\d{1,9}$/,"de-LU":/^(\+352)?((6\d1)\d{6})$/,"dv-MV":/^(\+?960)?(7[2-9]|9[1-9])\d{5}$/,"el-GR":/^(\+?30|0)?6(8[5-9]|9(?![26])[0-9])\d{7}$/,"el-CY":/^(\+?357?)?(9(9|6)\d{6})$/,"en-AI":/^(\+?1|0)264(?:2(35|92)|4(?:6[1-2]|76|97)|5(?:3[6-9]|8[1-4])|7(?:2(4|9)|72))\d{4}$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-AG":/^(?:\+1|1)268(?:464|7(?:1[3-9]|[28]\d|3[0246]|64|7[0-689]))\d{4}$/,"en-BM":/^(\+?1)?441(((3|7)\d{6}$)|(5[0-3][0-9]\d{4}$)|(59\d{5}$))/,"en-BS":/^(\+?1[-\s]?|0)?\(?242\)?[-\s]?\d{3}[-\s]?\d{4}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-GG":/^(\+?44|0)1481\d{6}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|28|55|59)\d{7}$/,"en-GY":/^(\+592|0)6\d{6}$/,"en-HK":/^(\+?852[-\s]?)?[456789]\d{3}[-\s]?\d{4}$/,"en-MO":/^(\+?853[-\s]?)?[6]\d{3}[-\s]?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-JM":/^(\+?876)?\d{7}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"en-SS":/^(\+?211|0)(9[1257])\d{7}$/,"en-KI":/^((\+686|686)?)?( )?((6|7)(2|3|8)[0-9]{6})$/,"en-KN":/^(?:\+1|1)869(?:46\d|48[89]|55[6-8]|66\d|76[02-7])\d{4}$/,"en-LS":/^(\+?266)(22|28|57|58|59|27|52)\d{6}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-NA":/^(\+?264|0)(6|8)\d{7}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PG":/^(\+?675|0)?(7\d|8[18])\d{6}$/,"en-PK":/^((00|\+)?92|0)3[0-6]\d{8}$/,"en-PH":/^(09|\+639)\d{9}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[3689]\d{7}$/,"en-SL":/^(\+?232|0)\d{8}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"en-ZW":/^(\+263)[0-9]{9}$/,"en-BW":/^(\+?267)?(7[1-8]{1})\d{6}$/,"es-AR":/^\+?549(11|[2368]\d)\d{8}$/,"es-BO":/^(\+?591)?(6|7)\d{7}$/,"es-CO":/^(\+?57)?3(0(0|1|2|4|5)|1\d|2[0-4]|5(0|1))\d{7}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-CR":/^(\+506)?[2-8]\d{7}$/,"es-CU":/^(\+53|0053)?5\d{7}/,"es-DO":/^(\+?1)?8[024]9\d{7}$/,"es-HN":/^(\+?504)?[9|8|3|2]\d{7}$/,"es-EC":/^(\+?593|0)([2-7]|9[2-9])\d{7}$/,"es-ES":/^(\+?34)?[6|7]\d{8}$/,"es-PE":/^(\+?51)?9\d{8}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-NI":/^(\+?505)\d{7,8}$/,"es-PA":/^(\+?507)\d{7,8}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-SV":/^(\+?503)?[67]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"es-VE":/^(\+?58)?(2|4)\d{9}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4[0-6]|50)\s?(\d\s?){4,8}$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-BF":/^(\+226|0)[67]\d{7}$/,"fr-BJ":/^(\+229)\d{8}$/,"fr-CD":/^(\+?243|0)?(8|9)\d{8}$/,"fr-CM":/^(\+?237)6[0-9]{8}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"fr-GF":/^(\+?594|0|00594)[67]\d{8}$/,"fr-GP":/^(\+?590|0|00590)[67]\d{8}$/,"fr-MQ":/^(\+?596|0|00596)[67]\d{8}$/,"fr-PF":/^(\+?689)?8[789]\d{6}$/,"fr-RE":/^(\+?262|0|00262)[67]\d{8}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36|06)(20|30|31|50|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"ir-IR":/^(\+98|0)?9\d{9}$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"it-SM":/^((\+378)|(0549)|(\+390549)|(\+3780549))?6\d{5,9}$/,"ja-JP":/^(\+81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,"ka-GE":/^(\+?995)?(79\d{7}|5\d{8})$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"ky-KG":/^(\+?7\s?\+?7|0)\s?\d{2}\s?\d{3}\s?\d{4}$/,"lt-LT":/^(\+370|8)\d{8}$/,"lv-LV":/^(\+?371)2\d{7}$/,"mg-MG":/^((\+?261|0)(2|3)\d)?\d{7}$/,"mn-MN":/^(\+|00|011)?976(77|81|88|91|94|95|96|99)\d{6}$/,"my-MM":/^(\+?959|09|9)(2[5-7]|3[1-2]|4[0-5]|6[6-9]|7[5-9]|9[6-9])[0-9]{7}$/,"ms-MY":/^(\+?60|0)1(([0145](-|\s)?\d{7,8})|([236-9](-|\s)?\d{7}))$/,"mz-MZ":/^(\+?258)?8[234567]\d{7}$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"ne-NP":/^(\+?977)?9[78]\d{8}$/,"nl-BE":/^(\+?32|0)4\d{8}$/,"nl-NL":/^(((\+|00)?31\(0\))|((\+|00)?31)|0)6{1}\d{8}$/,"nl-AW":/^(\+)?297(56|59|64|73|74|99)\d{5}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?[5-8]\d ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^((\+?55\ ?[1-9]{2}\ ?)|(\+?55\ ?\([1-9]{2}\)\ ?)|(0[1-9]{2}\ ?)|(\([1-9]{2}\)\ ?)|([1-9]{2}\ ?))((\d{4}\-?\d{4})|(9[1-9]{1}\d{3}\-?\d{4}))$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"pt-AO":/^(\+244)\d{9}$/,"ro-MD":/^(\+?373|0)((6(0|1|2|6|7|8|9))|(7(6|7|8|9)))\d{6}$/,"ro-RO":/^(\+?40|0)\s?7\d{2}(\/|\s|\.|-)?\d{3}(\s|\.|-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"si-LK":/^(?:0|94|\+94)?(7(0|1|2|4|5|6|7|8)( |-)?)\d{7}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"sq-AL":/^(\+355|0)6[789]\d{6}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"tg-TJ":/^(\+?992)?[5][5]\d{7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"tk-TM":/^(\+993|993|8)\d{8}$/,"uk-UA":/^(\+?38|8)?0\d{9}$/,"uz-UZ":/^(\+?998)?(6[125-79]|7[1-69]|88|9\d)\d{7}$/,"vi-VN":/^((\+?84)|0)((3([2-9]))|(5([25689]))|(7([0|6-9]))|(8([1-9]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?(1[3-9]|9[28])\d{9}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/,"dz-BT":/^(\+?975|0)?(17|16|77|02)\d{6}$/,"ar-YE":/^(((\+|00)9677|0?7)[0137]\d{7}|((\+|00)967|0)[1-7]\d{6})$/,"ar-EH":/^(\+?212|0)[\s\-]?(5288|5289)[\s\-]?\d{5}$/,"fa-AF":/^(\+93|0)?(2{1}[0-8]{1}|[3-5]{1}[0-4]{1})(\d{7})$/};xr["en-CA"]=xr["en-US"],xr["fr-CA"]=xr["en-CA"],xr["fr-BE"]=xr["nl-BE"],xr["zh-HK"]=xr["en-HK"],xr["zh-MO"]=xr["en-MO"],xr["ga-IE"]=xr["en-IE"],xr["fr-CH"]=xr["de-CH"],xr["it-CH"]=xr["fr-CH"];var Er=Object.keys(xr);Sr.locales=Er;var Cr="isMobilePhone";function Nr(t,e,d){return"string"==typeof t&&_r(t,e,d)}var Pr={};Object.defineProperty(Pr,"__esModule",{value:!0});var Lr=Pr.default=function(t){return(0,Rr.default)(t),Tr.has(t.toUpperCase())};Pr.CountryCodes=void 0;var Rr=function(t){return t&&t.__esModule?t:{default:t}}(R);var Tr=new Set(["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"]);var Dr=Tr;Pr.CountryCodes=Dr;var wr="isISO31661Alpha2";function Br(t){return"string"==typeof t&&Lr(t)}var Fr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.has(t.toUpperCase())};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=new Set(["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"]);t.exports=e.default,t.exports.default=e.default}(Fr,Fr.exports);var Zr=Fr.exports,Gr=N(Zr),Ur="isISO31661Alpha3";function jr(t){return"string"==typeof t&&Gr(t)}var Hr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),(0,r.default)(t)&&24===t.length};var d=n(R),r=n(Cd);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(Hr,Hr.exports);var Vr=Hr.exports,Kr=N(Vr),kr="isMongoId";function Wr(t){return"string"==typeof t&&Kr(t)}var Yr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/[^\x00-\x7F]/;t.exports=e.default,t.exports.default=e.default}(Yr,Yr.exports);var Xr=Yr.exports,zr=N(Xr),Jr="isMultibyte";function qr(t){return"string"==typeof t&&zr(t)}var Qr={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;t.exports=e.default,t.exports.default=e.default}(Qr,Qr.exports);var tn=Qr.exports,en=N(tn),dn="isSurrogatePair";function rn(t){return"string"==typeof t&&en(t)}var nn={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=(0,a.default)(e,u)).validate_length&&t.length>=2083)return!1;if(!e.allow_fragments&&t.includes("#"))return!1;if(!e.allow_query_components&&(t.includes("?")||t.includes("&")))return!1;var o,l,f,$,p,v,y,m;if(y=t.split("#"),t=y.shift(),y=t.split("?"),t=y.shift(),(y=t.split("://")).length>1){if(o=y.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(o))return!1}else{if(e.require_protocol)return!1;if("//"===t.slice(0,2)){if(!e.allow_protocol_relative_urls)return!1;y[0]=t.slice(2)}}if(""===(t=y.join("://")))return!1;if(y=t.split("/"),""===(t=y.shift())&&!e.require_host)return!0;if((y=t.split("@")).length>1){if(e.disallow_auth)return!1;if(""===y[0])return!1;if((l=y.shift()).indexOf(":")>=0&&l.split(":").length>2)return!1;var g=function(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var d=[],r=!0,n=!1,a=void 0;try{for(var o,i=t[Symbol.iterator]();!(r=(o=i.next()).done)&&(d.push(o.value),!e||d.length!==e);r=!0);}catch(t){n=!0,a=t}finally{try{r||null==i.return||i.return()}finally{if(n)throw a}}return d}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return i(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return i(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(l.split(":"),2),h=g[0],A=g[1];if(""===h&&""===A)return!1}$=y.join("@"),v=null,m=null;var b=$.match(s);b?(f="",m=b[1],v=b[2]||null):(y=$.split(":"),f=y.shift(),y.length&&(v=y.join(":")));if(null!==v&&v.length>0){if(p=parseInt(v,10),!/^[0-9]+$/.test(v)||p<=0||p>65535)return!1}else if(e.require_port)return!1;if(e.host_whitelist)return c(f,e.host_whitelist);if(""===f&&!e.require_host)return!0;if(!((0,n.default)(f)||(0,r.default)(f,e)||m&&(0,n.default)(m,6)))return!1;if(f=f||m,e.host_blacklist&&c(f,e.host_blacklist))return!1;return!0};var d=o(R),r=o(Xe),n=o(qe),a=o(D);function o(t){return t&&t.__esModule?t:{default:t}}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}var u={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},s=/^\[([^\]]+)\](?::([0-9]+))?$/;function l(t){return"[object RegExp]"===Object.prototype.toString.call(t)}function c(t,e){for(var d=0;d<e.length;d++){var r=e[d];if(t===r||l(r)&&r.test(t))return!0}return!1}t.exports=e.default,t.exports.default=e.default}(nn,nn.exports);var an=nn.exports,on=N(an),un="isUrl";function sn(t,e){return"string"==typeof t&&on(t,e)}var ln={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var n=r[[void 0,null].includes(e)?"all":e];return!!n&&n.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r={1:/^[0-9A-F]{8}-[0-9A-F]{4}-1[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,2:/^[0-9A-F]{8}-[0-9A-F]{4}-2[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};t.exports=e.default,t.exports.default=e.default}(ln,ln.exports);var cn=ln.exports,fn=N(cn),$n="isUuid";function pn(t,e){return"string"==typeof t&&fn(t,e)}var vn="IsFirebasePushId";function yn(t){return"string"==typeof t&&20===t.length&&/^[a-zA-Z0-9_-]*$/.test(t)}var mn={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),t===t.toUpperCase()};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(mn,mn.exports);var gn=mn.exports,hn=N(gn),An="isUppercase";function bn(t){return"string"==typeof t&&hn(t)}var In={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var n,a;(0,d.default)(t),"object"===r(e)?(n=e.min||0,a=e.max):(n=arguments[1]||0,a=arguments[2]);var o=t.match(/(\uFE0F|\uFE0E)/g)||[],i=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],u=t.length-o.length-i.length;return u>=n&&(void 0===a||u<=a)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}t.exports=e.default,t.exports.default=e.default}(In,In.exports);var Mn=In.exports,Sn=N(Mn),_n="isLength";function On(t,e,d){return"string"==typeof t&&Sn(t,{min:e,max:d})}var xn="maxLength";function En(t,e){return"string"==typeof t&&Sn(t,{min:0,max:e})}var Cn="minLength";function Nn(t,e){return"string"==typeof t&&Sn(t,{min:e})}var Pn={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,r){(0,d.default)(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,r));return!!t.match(e)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(Pn,Pn.exports);var Ln=Pn.exports,Rn=N(Ln),Tn="matches";function Dn(t,e,d){return"string"==typeof t&&Rn(t,e,d)}var wn={version:4,country_calling_codes:{1:["US","AG","AI","AS","BB","BM","BS","CA","DM","DO","GD","GU","JM","KN","KY","LC","MP","MS","PR","SX","TC","TT","VC","VG","VI"],7:["RU","KZ"],20:["EG"],27:["ZA"],30:["GR"],31:["NL"],32:["BE"],33:["FR"],34:["ES"],36:["HU"],39:["IT","VA"],40:["RO"],41:["CH"],43:["AT"],44:["GB","GG","IM","JE"],45:["DK"],46:["SE"],47:["NO","SJ"],48:["PL"],49:["DE"],51:["PE"],52:["MX"],53:["CU"],54:["AR"],55:["BR"],56:["CL"],57:["CO"],58:["VE"],60:["MY"],61:["AU","CC","CX"],62:["ID"],63:["PH"],64:["NZ"],65:["SG"],66:["TH"],81:["JP"],82:["KR"],84:["VN"],86:["CN"],90:["TR"],91:["IN"],92:["PK"],93:["AF"],94:["LK"],95:["MM"],98:["IR"],211:["SS"],212:["MA","EH"],213:["DZ"],216:["TN"],218:["LY"],220:["GM"],221:["SN"],222:["MR"],223:["ML"],224:["GN"],225:["CI"],226:["BF"],227:["NE"],228:["TG"],229:["BJ"],230:["MU"],231:["LR"],232:["SL"],233:["GH"],234:["NG"],235:["TD"],236:["CF"],237:["CM"],238:["CV"],239:["ST"],240:["GQ"],241:["GA"],242:["CG"],243:["CD"],244:["AO"],245:["GW"],246:["IO"],247:["AC"],248:["SC"],249:["SD"],250:["RW"],251:["ET"],252:["SO"],253:["DJ"],254:["KE"],255:["TZ"],256:["UG"],257:["BI"],258:["MZ"],260:["ZM"],261:["MG"],262:["RE","YT"],263:["ZW"],264:["NA"],265:["MW"],266:["LS"],267:["BW"],268:["SZ"],269:["KM"],290:["SH","TA"],291:["ER"],297:["AW"],298:["FO"],299:["GL"],350:["GI"],351:["PT"],352:["LU"],353:["IE"],354:["IS"],355:["AL"],356:["MT"],357:["CY"],358:["FI","AX"],359:["BG"],370:["LT"],371:["LV"],372:["EE"],373:["MD"],374:["AM"],375:["BY"],376:["AD"],377:["MC"],378:["SM"],380:["UA"],381:["RS"],382:["ME"],383:["XK"],385:["HR"],386:["SI"],387:["BA"],389:["MK"],420:["CZ"],421:["SK"],423:["LI"],500:["FK"],501:["BZ"],502:["GT"],503:["SV"],504:["HN"],505:["NI"],506:["CR"],507:["PA"],508:["PM"],509:["HT"],590:["GP","BL","MF"],591:["BO"],592:["GY"],593:["EC"],594:["GF"],595:["PY"],596:["MQ"],597:["SR"],598:["UY"],599:["CW","BQ"],670:["TL"],672:["NF"],673:["BN"],674:["NR"],675:["PG"],676:["TO"],677:["SB"],678:["VU"],679:["FJ"],680:["PW"],681:["WF"],682:["CK"],683:["NU"],685:["WS"],686:["KI"],687:["NC"],688:["TV"],689:["PF"],690:["TK"],691:["FM"],692:["MH"],850:["KP"],852:["HK"],853:["MO"],855:["KH"],856:["LA"],880:["BD"],886:["TW"],960:["MV"],961:["LB"],962:["JO"],963:["SY"],964:["IQ"],965:["KW"],966:["SA"],967:["YE"],968:["OM"],970:["PS"],971:["AE"],972:["IL"],973:["BH"],974:["QA"],975:["BT"],976:["MN"],977:["NP"],992:["TJ"],993:["TM"],994:["AZ"],995:["GE"],996:["KG"],998:["UZ"]},countries:{AC:["247","00","(?:[01589]\\d|[46])\\d{4}",[5,6],0,0,0,0,0,0,0,[["6[2-467]\\d{3}",[5]],["4\\d{4}",[5]],0,0,0,0,["(?:0[1-9]|[1589]\\d)\\d{4}",[6]]]],AD:["376","00","(?:1|6\\d)\\d{7}|[135-9]\\d{5}",[6,8,9],[["(\\d{3})(\\d{3})","$1 $2",["[135-9]"]],["(\\d{4})(\\d{4})","$1 $2",["1"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]]],0,0,0,0,0,0,[["[78]\\d{5}",[6]],["690\\d{6}|[356]\\d{5}",[6,9]],["180[02]\\d{4}",[8]],["[19]\\d{5}",[6]]]],AE:["971","00","(?:[4-7]\\d|9[0-689])\\d{7}|800\\d{2,9}|[2-4679]\\d{7}",[5,6,7,8,9,10,11,12],[["(\\d{3})(\\d{2,9})","$1 $2",["60|8"]],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[236]|[479][2-8]"],"0$1"],["(\\d{3})(\\d)(\\d{5})","$1 $2 $3",["[479]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["5"],"0$1"]],"0",0,0,0,0,0,[["[2-4679][2-8]\\d{6}",[8]],["5[024-68]\\d{7}",[9]],["400\\d{6}|800\\d{2,9}"],["900[02]\\d{5}",[9]],0,0,["600[25]\\d{5}",[9]],0,0,["700[05]\\d{5}",[9]]]],AF:["93","00","[2-7]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[2-7]"],"0$1"]],"0",0,0,0,0,0,[["(?:[25][0-8]|[34][0-4]|6[0-5])[2-9]\\d{6}"],["7\\d{8}"]]],AG:["1","011","(?:268|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([457]\\d{6})$|1","268$1",0,"268",[["268(?:4(?:6[0-38]|84)|56[0-2])\\d{4}"],["268(?:464|7(?:1[3-9]|[28]\\d|3[0246]|64|7[0-689]))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,0,["26840[69]\\d{4}"],["26848[01]\\d{4}"]]],AI:["1","011","(?:264|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2457]\\d{6})$|1","264$1",0,"264",[["264(?:292|4(?:6[12]|9[78]))\\d{4}"],["264(?:235|4(?:69|76)|5(?:3[6-9]|8[1-4])|7(?:29|72))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,0,["264724\\d{4}"]]],AL:["355","00","(?:700\\d\\d|900)\\d{3}|8\\d{5,7}|(?:[2-5]|6\\d)\\d{7}",[6,7,8,9],[["(\\d{3})(\\d{3,4})","$1 $2",["80|9"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["4[2-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2358][2-5]|4"],"0$1"],["(\\d{3})(\\d{5})","$1 $2",["[23578]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["6"],"0$1"]],"0",0,0,0,0,0,[["4505[0-2]\\d{3}|(?:[2358][16-9]\\d[2-9]|4410)\\d{4}|(?:[2358][2-5][2-9]|4(?:[2-57-9][2-9]|6\\d))\\d{5}",[8]],["6(?:[78][2-9]|9\\d)\\d{6}",[9]],["800\\d{4}",[7]],["900[1-9]\\d\\d",[6]],["700[2-9]\\d{4}",[8]],0,0,0,0,["808[1-9]\\d\\d",[6]]]],AM:["374","00","(?:[1-489]\\d|55|60|77)\\d{6}",[8],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[89]0"],"0 $1"],["(\\d{3})(\\d{5})","$1 $2",["2|3[12]"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["1|47"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["[3-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:(?:1[0-25]|47)\\d|2(?:2[2-46]|3[1-8]|4[2-69]|5[2-7]|6[1-9]|8[1-7])|3[12]2)\\d{5}"],["(?:33|4[1349]|55|77|88|9[13-9])\\d{6}"],["800\\d{5}"],["90[016]\\d{5}"],0,0,0,0,["60(?:2[78]|3[5-9]|4[02-9]|5[0-46-9]|[6-8]\\d|9[0-2])\\d{4}"],["80[1-4]\\d{5}"]]],AO:["244","00","[29]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[29]"]]],0,0,0,0,0,0,[["2\\d(?:[0134][25-9]|[25-9]\\d)\\d{5}"],["9[1-59]\\d{7}"]]],AR:["54","00","(?:11|[89]\\d\\d)\\d{8}|[2368]\\d{9}",[10,11],[["(\\d{4})(\\d{2})(\\d{4})","$1 $2-$3",["2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])","2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)","2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]","2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]"],"0$1",1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2-$3",["1"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["[68]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2-$3",["[23]"],"0$1",1],["(\\d)(\\d{4})(\\d{2})(\\d{4})","$2 15-$3-$4",["9(?:2[2-469]|3[3-578])","9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))","9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)","9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]","9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]"],"0$1",0,"$1 $2 $3-$4"],["(\\d)(\\d{2})(\\d{4})(\\d{4})","$2 15-$3-$4",["91"],"0$1",0,"$1 $2 $3-$4"],["(\\d{3})(\\d{3})(\\d{5})","$1-$2-$3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{4})","$2 15-$3-$4",["9"],"0$1",0,"$1 $2 $3-$4"]],"0",0,"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?","9$1",0,0,[["3(?:7(?:1[15]|81)|8(?:21|4[16]|69|9[12]))[46]\\d{5}|(?:2(?:657|9(?:54|66))|3(?:7(?:55|77)|865))[2-8]\\d{5}|(?:2(?:2(?:2[59]|44|52)|3(?:26|44)|473|9(?:[07]2|2[26]|34|46))|3327)[45]\\d{5}|(?:2(?:284|3(?:02|23)|920)|3(?:4(?:46|8[27]|92)|541|878))[2-7]\\d{5}|(?:2(?:(?:26|62)2|320|477|9(?:42|83))|3(?:329|4(?:62|76|89)|564))[2-6]\\d{5}|(?:(?:11[1-8]|670)\\d|2(?:2(?:0[45]|1[2-6]|3[3-6])|3(?:[06]4|7[45])|494|6(?:04|1[2-8]|[36][45]|4[3-6])|80[45]|9(?:[17][4-6]|[48][45]|9[3-6]))|3(?:364|4(?:1[2-8]|[235][4-6]|84)|5(?:1[2-9]|[38][4-6])|6(?:2[45]|44)|7[069][45]|8(?:0[45]|[17][2-6]|3[4-6]|5[3-6]|8[3-68])))\\d{6}|2(?:2(?:21|4[23]|6[145]|7[1-4]|8[356]|9[267])|3(?:16|3[13-8]|43|5[346-8]|9[3-5])|475|6(?:2[46]|4[78]|5[1568])|9(?:03|2[1457-9]|3[1356]|4[08]|[56][23]|82))4\\d{5}|(?:2(?:2(?:57|81)|3(?:24|46|92)|9(?:01|23|64))|3(?:4(?:42|71)|5(?:25|37|4[347]|71)|7(?:18|5[17])))[3-6]\\d{5}|(?:2(?:2(?:02|2[3467]|4[156]|5[45]|6[6-8]|91)|3(?:1[47]|25|[45][25]|96)|47[48]|625|932)|3(?:38[2578]|4(?:0[0-24-9]|3[78]|4[457]|58|6[03-9]|72|83|9[136-8])|5(?:2[124]|[368][23]|4[2689]|7[2-6])|7(?:16|2[15]|3[145]|4[13]|5[468]|7[2-5]|8[26])|8(?:2[5-7]|3[278]|4[3-5]|5[78]|6[1-378]|[78]7|94)))[4-6]\\d{5}",[10]],["93(?:7(?:1[15]|81)|8(?:21|4[16]|69|9[12]))[46]\\d{5}|9(?:2(?:657|9(?:54|66))|3(?:7(?:55|77)|865))[2-8]\\d{5}|9(?:2(?:2(?:2[59]|44|52)|3(?:26|44)|473|9(?:[07]2|2[26]|34|46))|3327)[45]\\d{5}|9(?:2(?:284|3(?:02|23)|920)|3(?:4(?:46|8[27]|92)|541|878))[2-7]\\d{5}|9(?:2(?:(?:26|62)2|320|477|9(?:42|83))|3(?:329|4(?:62|76|89)|564))[2-6]\\d{5}|(?:675\\d|9(?:11[1-8]\\d|2(?:2(?:0[45]|1[2-6]|3[3-6])|3(?:[06]4|7[45])|494|6(?:04|1[2-8]|[36][45]|4[3-6])|80[45]|9(?:[17][4-6]|[48][45]|9[3-6]))|3(?:364|4(?:1[2-8]|[235][4-6]|84)|5(?:1[2-9]|[38][4-6])|6(?:2[45]|44)|7[069][45]|8(?:0[45]|[17][2-6]|3[4-6]|5[3-6]|8[3-68]))))\\d{6}|92(?:2(?:21|4[23]|6[145]|7[1-4]|8[356]|9[267])|3(?:16|3[13-8]|43|5[346-8]|9[3-5])|475|6(?:2[46]|4[78]|5[1568])|9(?:03|2[1457-9]|3[1356]|4[08]|[56][23]|82))4\\d{5}|9(?:2(?:2(?:57|81)|3(?:24|46|92)|9(?:01|23|64))|3(?:4(?:42|71)|5(?:25|37|4[347]|71)|7(?:18|5[17])))[3-6]\\d{5}|9(?:2(?:2(?:02|2[3467]|4[156]|5[45]|6[6-8]|91)|3(?:1[47]|25|[45][25]|96)|47[48]|625|932)|3(?:38[2578]|4(?:0[0-24-9]|3[78]|4[457]|58|6[03-9]|72|83|9[136-8])|5(?:2[124]|[368][23]|4[2689]|7[2-6])|7(?:16|2[15]|3[145]|4[13]|5[468]|7[2-5]|8[26])|8(?:2[5-7]|3[278]|4[3-5]|5[78]|6[1-378]|[78]7|94)))[4-6]\\d{5}"],["800\\d{7,8}"],["60[04579]\\d{7}",[10]],0,0,["810\\d{7}",[10]]]],AS:["1","011","(?:[58]\\d\\d|684|900)\\d{7}",[10],0,"1",0,"([267]\\d{6})$|1","684$1",0,"684",[["6846(?:22|33|44|55|77|88|9[19])\\d{4}"],["684(?:2(?:48|5[2468]|7[26])|7(?:3[13]|70|82))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],AT:["43","00","1\\d{3,12}|2\\d{6,12}|43(?:(?:0\\d|5[02-9])\\d{3,9}|2\\d{4,5}|[3467]\\d{4}|8\\d{4,6}|9\\d{4,7})|5\\d{4,12}|8\\d{7,12}|9\\d{8,12}|(?:[367]\\d|4[0-24-9])\\d{4,11}",[4,5,6,7,8,9,10,11,12,13],[["(\\d)(\\d{3,12})","$1 $2",["1(?:11|[2-9])"],"0$1"],["(\\d{3})(\\d{2})","$1 $2",["517"],"0$1"],["(\\d{2})(\\d{3,5})","$1 $2",["5[079]"],"0$1"],["(\\d{3})(\\d{3,10})","$1 $2",["(?:31|4)6|51|6(?:5[0-3579]|[6-9])|7(?:20|32|8)|[89]"],"0$1"],["(\\d{4})(\\d{3,9})","$1 $2",["[2-467]|5[2-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["5"],"0$1"],["(\\d{2})(\\d{4})(\\d{4,7})","$1 $2 $3",["5"],"0$1"]],"0",0,0,0,0,0,[["1(?:11\\d|[2-9]\\d{3,11})|(?:316|463|(?:51|66|73)2)\\d{3,10}|(?:2(?:1[467]|2[13-8]|5[2357]|6[1-46-8]|7[1-8]|8[124-7]|9[1458])|3(?:1[1-578]|3[23568]|4[5-7]|5[1378]|6[1-38]|8[3-68])|4(?:2[1-8]|35|7[1368]|8[2457])|5(?:2[1-8]|3[357]|4[147]|5[12578]|6[37])|6(?:13|2[1-47]|4[135-8]|5[468])|7(?:2[1-8]|35|4[13478]|5[68]|6[16-8]|7[1-6]|9[45]))\\d{4,10}"],["6(?:5[0-3579]|6[013-9]|[7-9]\\d)\\d{4,10}",[7,8,9,10,11,12,13]],["800\\d{6,10}",[9,10,11,12,13]],["(?:8[69][2-68]|9(?:0[01]|3[019]))\\d{6,10}",[9,10,11,12,13]],0,0,0,0,["5(?:0[1-9]|17|[79]\\d)\\d{2,10}|7[28]0\\d{6,10}",[5,6,7,8,9,10,11,12,13]],["8(?:10|2[018])\\d{6,10}|828\\d{5}",[8,9,10,11,12,13]]]],AU:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{7}(?:\\d(?:\\d{2})?)?|8[0-24-9]\\d{7})|[2-478]\\d{8}|1\\d{4,7}",[5,6,7,8,9,10,12],[["(\\d{2})(\\d{3,4})","$1 $2",["16"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,4})","$1 $2 $3",["16"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["14|4"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["[2378]"],"(0$1)"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1(?:30|[89])"]]],"0",0,"(183[12])|0",0,0,0,[["(?:(?:(?:2(?:[0-26-9]\\d|3[0-8]|4[02-9]|5[0135-9])|7(?:[013-57-9]\\d|2[0-8]))\\d|3(?:(?:[0-3589]\\d|6[1-9]|7[0-35-9])\\d|4(?:[0-578]\\d|90)))\\d\\d|8(?:51(?:0(?:0[03-9]|[12479]\\d|3[2-9]|5[0-8]|6[1-9]|8[0-7])|1(?:[0235689]\\d|1[0-69]|4[0-589]|7[0-47-9])|2(?:0[0-79]|[18][13579]|2[14-9]|3[0-46-9]|[4-6]\\d|7[89]|9[0-4])|3\\d\\d)|(?:6[0-8]|[78]\\d)\\d{3}|9(?:[02-9]\\d{3}|1(?:(?:[0-58]\\d|6[0135-9])\\d|7(?:0[0-24-9]|[1-9]\\d)|9(?:[0-46-9]\\d|5[0-79])))))\\d{3}",[9]],["4(?:(?:79|94)[01]|83[0-389])\\d{5}|4(?:[0-3]\\d|4[047-9]|5[0-25-9]|6[0-36-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]],["180(?:0\\d{3}|2)\\d{3}",[7,10]],["190[0-26]\\d{6}",[10]],0,0,0,["163\\d{2,6}",[5,6,7,8,9]],["14(?:5(?:1[0458]|[23][458])|71\\d)\\d{4}",[9]],["13(?:00\\d{6}(?:\\d{2})?|45[0-4]\\d{3})|13\\d{4}",[6,8,10,12]]],"0011"],AW:["297","00","(?:[25-79]\\d\\d|800)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[25-9]"]]],0,0,0,0,0,0,[["5(?:2\\d|8[1-9])\\d{4}"],["(?:290|5[69]\\d|6(?:[03]0|22|4[0-2]|[69]\\d)|7(?:[34]\\d|7[07])|9(?:6[45]|9[4-8]))\\d{4}"],["800\\d{4}"],["900\\d{4}"],0,0,0,0,["(?:28\\d|501)\\d{4}"]]],AX:["358","00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))","2\\d{4,9}|35\\d{4,5}|(?:60\\d\\d|800)\\d{4,6}|7\\d{5,11}|(?:[14]\\d|3[0-46-9]|50)\\d{4,8}",[5,6,7,8,9,10,11,12],0,"0",0,0,0,0,"18",[["18[1-8]\\d{3,6}",[6,7,8,9]],["4946\\d{2,6}|(?:4[0-8]|50)\\d{4,8}",[6,7,8,9,10]],["800\\d{4,6}",[7,8,9]],["[67]00\\d{5,6}",[8,9]],0,0,["20\\d{4,8}|60[12]\\d{5,6}|7(?:099\\d{4,5}|5[03-9]\\d{3,7})|20[2-59]\\d\\d|(?:606|7(?:0[78]|1|3\\d))\\d{7}|(?:10|29|3[09]|70[1-5]\\d)\\d{4,8}"]],"00"],AZ:["994","00","365\\d{6}|(?:[124579]\\d|60|88)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["90"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[28]|2|365|46","1[28]|2|365[45]|46","1[28]|2|365(?:4|5[02])|46"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[13-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:2[12]428|3655[02])\\d{4}|(?:2(?:22[0-79]|63[0-28])|3654)\\d{5}|(?:(?:1[28]|46)\\d|2(?:[014-6]2|[23]3))\\d{6}"],["36554\\d{4}|(?:[16]0|4[04]|5[015]|7[07]|99)\\d{7}"],["88\\d{7}"],["900200\\d{3}"]]],BA:["387","00","6\\d{8}|(?:[35689]\\d|49|70)\\d{6}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["6[1-3]|[7-9]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2-$3",["[3-5]|6[56]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["6"],"0$1"]],"0",0,0,0,0,0,[["(?:3(?:[05-79][2-9]|1[4579]|[23][24-9]|4[2-4689]|8[2457-9])|49[2-579]|5(?:0[2-49]|[13][2-9]|[268][2-4679]|4[4689]|5[2-79]|7[2-69]|9[2-4689]))\\d{5}",[8]],["6040\\d{5}|6(?:03|[1-356]|44|7\\d)\\d{6}"],["8[08]\\d{6}",[8]],["9[0246]\\d{6}",[8]],0,0,["703[235]0\\d{3}|70(?:2[0-5]|3[0146]|[56]0)\\d{4}",[8]],0,0,["8[12]\\d{6}",[8]]]],BB:["1","011","(?:246|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","246$1",0,"246",[["246521[0369]\\d{3}|246(?:2(?:2[78]|7[0-4])|4(?:1[024-6]|2\\d|3[2-9])|5(?:20|[34]\\d|54|7[1-3])|6(?:2\\d|38)|7[35]7|9(?:1[89]|63))\\d{4}"],["246(?:(?:2(?:[3568]\\d|4[0-57-9])|3(?:5[2-9]|6[0-6])|4(?:46|5\\d)|69[5-7]|8(?:[2-5]\\d|83))\\d|52(?:1[147]|20))\\d{3}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["(?:246976|900[2-9]\\d\\d)\\d{4}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,["246(?:292|367|4(?:1[7-9]|3[01]|4[47-9]|67)|7(?:1[2-9]|2\\d|3[016]|53))\\d{4}"],0,["24631\\d{5}"]]],BD:["880","00","[1-469]\\d{9}|8[0-79]\\d{7,8}|[2-79]\\d{8}|[2-9]\\d{7}|[3-9]\\d{6}|[57-9]\\d{5}",[6,7,8,9,10],[["(\\d{2})(\\d{4,6})","$1-$2",["31[5-8]|[459]1"],"0$1"],["(\\d{3})(\\d{3,7})","$1-$2",["3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]"],"0$1"],["(\\d{4})(\\d{3,6})","$1-$2",["[13-9]|22"],"0$1"],["(\\d)(\\d{7,8})","$1-$2",["2"],"0$1"]],"0",0,0,0,0,0,[["(?:4(?:31\\d\\d|423)|5222)\\d{3}(?:\\d{2})?|8332[6-9]\\d\\d|(?:3(?:03[56]|224)|4(?:22[25]|653))\\d{3,4}|(?:3(?:42[47]|529|823)|4(?:027|525|65(?:28|8))|562|6257|7(?:1(?:5[3-5]|6[12]|7[156]|89)|22[589]56|32|42675|52(?:[25689](?:56|8)|[347]8)|71(?:6[1267]|75|89)|92374)|82(?:2[59]|32)56|9(?:03[23]56|23(?:256|373)|31|5(?:1|2[4589]56)))\\d{3}|(?:3(?:02[348]|22[35]|324|422)|4(?:22[67]|32[236-9]|6(?:2[46]|5[57])|953)|5526|6(?:024|6655)|81)\\d{4,5}|(?:2(?:7(?:1[0-267]|2[0-289]|3[0-29]|4[01]|5[1-3]|6[013]|7[0178]|91)|8(?:0[125]|1[1-6]|2[0157-9]|3[1-69]|41|6[1-35]|7[1-5]|8[1-8]|9[0-6])|9(?:0[0-2]|1[0-4]|2[568]|3[3-6]|5[5-7]|6[0136-9]|7[0-7]|8[014-9]))|3(?:0(?:2[025-79]|3[2-4])|181|22[12]|32[2356]|824)|4(?:02[09]|22[348]|32[045]|523|6(?:27|54))|666(?:22|53)|7(?:22[57-9]|42[56]|82[35])8|8(?:0[124-9]|2(?:181|2[02-4679]8)|4[12]|[5-7]2)|9(?:[04]2|2(?:2|328)|81))\\d{4}|(?:2(?:222|[45]\\d)\\d|3(?:1(?:2[5-7]|[5-7])|425|822)|4(?:033|1\\d|[257]1|332|4(?:2[246]|5[25])|6(?:2[35]|56|62)|8(?:23|54)|92[2-5])|5(?:02[03489]|22[457]|32[35-79]|42[46]|6(?:[18]|53)|724|826)|6(?:023|2(?:2[2-5]|5[3-5]|8)|32[3478]|42[34]|52[47]|6(?:[18]|6(?:2[34]|5[24]))|[78]2[2-5]|92[2-6])|7(?:02|21\\d|[3-589]1|6[12]|72[24])|8(?:217|3[12]|[5-7]1)|9[24]1)\\d{5}|(?:(?:3[2-8]|5[2-57-9]|6[03-589])1|4[4689][18])\\d{5}|[59]1\\d{5}"],["(?:1[13-9]\\d|644)\\d{7}|(?:3[78]|44|66)[02-9]\\d{7}",[10]],["80[03]\\d{7}",[10]],0,0,0,0,0,["96(?:0[469]|1[0-47]|3[389]|43|6[69]|7[78])\\d{6}",[10]]]],BE:["32","00","4\\d{8}|[1-9]\\d{7}",[8,9],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["(?:80|9)0"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[239]|4[23]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[15-8]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["4"],"0$1"]],"0",0,0,0,0,0,[["80[2-8]\\d{5}|(?:1[0-69]|[23][2-8]|4[23]|5\\d|6[013-57-9]|71|8[1-79]|9[2-4])\\d{6}",[8]],["4[5-9]\\d{7}",[9]],["800[1-9]\\d{4}",[8]],["(?:70(?:2[0-57]|3[04-7]|44|6[4-69]|7[0579])|90\\d\\d)\\d{4}",[8]],0,0,["78(?:0[57]|1[014-8]|2[25]|3[15-8]|48|[56]0|7[06-8]|9\\d)\\d{4}",[8]],0,0,["7879\\d{4}",[8]]]],BF:["226","00","[025-7]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[025-7]"]]],0,0,0,0,0,0,[["2(?:0(?:49|5[23]|6[5-7]|9[016-9])|4(?:4[569]|5[4-6]|6[5-7]|7[0179])|5(?:[34]\\d|50|6[5-7]))\\d{4}"],["(?:0[1-35-7]|5[0-8]|[67]\\d)\\d{6}"]]],BG:["359","00","00800\\d{7}|[2-7]\\d{6,7}|[89]\\d{6,8}|2\\d{5}",[6,7,8,9,12],[["(\\d)(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["2"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["43[1-6]|70[1-9]"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["(?:70|8)0"],"0$1"],["(\\d{3})(\\d{3})(\\d{2})","$1 $2 $3",["43[1-7]|7"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[48]|9[08]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[["2\\d{5,7}|(?:43[1-6]|70[1-9])\\d{4,5}|(?:[36]\\d|4[124-7]|[57][1-9]|8[1-6]|9[1-7])\\d{5,6}",[6,7,8]],["(?:43[07-9]|99[69]\\d)\\d{5}|(?:8[7-9]|98)\\d{7}",[8,9]],["(?:00800\\d\\d|800)\\d{5}",[8,12]],["90\\d{6}",[8]],0,0,0,0,0,["700\\d{5}",[8]]]],BH:["973","00","[136-9]\\d{7}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[13679]|8[02-4679]"]]],0,0,0,0,0,0,[["(?:1(?:3[1356]|6[0156]|7\\d)\\d|6(?:1[16]\\d|500|6(?:0\\d|3[12]|44|55|7[7-9]|88)|9[69][69])|7(?:[07]\\d\\d|1(?:11|78)))\\d{4}"],["(?:3(?:[0-79]\\d|8[0-57-9])\\d|6(?:3(?:00|33|6[16])|441|6(?:3[03-9]|[69]\\d|7[0-689])))\\d{4}"],["8[02369]\\d{6}"],["(?:87|9[0-8])\\d{6}"],0,0,0,0,0,["84\\d{6}"]]],BI:["257","00","(?:[267]\\d|31)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2367]"]]],0,0,0,0,0,0,[["(?:22|31)\\d{6}"],["(?:29|[67][125-9])\\d{6}"]]],BJ:["229","00","[24-689]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24-689]"]]],0,0,0,0,0,0,[["2(?:02|1[037]|2[45]|3[68]|4\\d)\\d{5}"],["(?:4[0-7]|[56]\\d|9[013-9])\\d{6}"],0,0,0,0,["81\\d{6}"],0,["857[58]\\d{4}"]]],BL:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],0,"0",0,0,0,0,0,[["590(?:2[7-9]|3[3-7]|5[12]|87)\\d{4}"],["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"],["80[0-5]\\d{6}"],0,0,0,0,0,["9(?:(?:39[5-7]|76[018])\\d|475[0-5])\\d{4}"]]],BM:["1","011","(?:441|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","441$1",0,"441",[["441(?:[46]\\d\\d|5(?:4\\d|60|89))\\d{4}"],["441(?:[2378]\\d|5[0-39]|9[02])\\d{5}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],BN:["673","00","[2-578]\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-578]"]]],0,0,0,0,0,0,[["22[0-7]\\d{4}|(?:2[013-9]|[34]\\d|5[0-25-9])\\d{5}"],["(?:22[89]|[78]\\d\\d)\\d{4}"],0,0,0,0,0,0,["5[34]\\d{5}"]]],BO:["591","00(?:1\\d)?","(?:[2-467]\\d\\d|8001)\\d{5}",[8,9],[["(\\d)(\\d{7})","$1 $2",["[23]|4[46]"]],["(\\d{8})","$1",["[67]"]],["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["8"]]],"0",0,"0(1\\d)?",0,0,0,[["(?:2(?:2\\d\\d|5(?:11|[258]\\d|9[67])|6(?:12|2\\d|9[34])|8(?:2[34]|39|62))|3(?:3\\d\\d|4(?:6\\d|8[24])|8(?:25|42|5[257]|86|9[25])|9(?:[27]\\d|3[2-4]|4[248]|5[24]|6[2-6]))|4(?:4\\d\\d|6(?:11|[24689]\\d|72)))\\d{4}",[8]],["[67]\\d{7}",[8]],["8001[07]\\d{4}",[9]]]],BQ:["599","00","(?:[34]1|7\\d)\\d{5}",[7],0,0,0,0,0,0,"[347]",[["(?:318[023]|41(?:6[023]|70)|7(?:1[578]|2[05]|50)\\d)\\d{3}"],["(?:31(?:8[14-8]|9[14578])|416[14-9]|7(?:0[01]|7[07]|8\\d|9[056])\\d)\\d{3}"]]],BR:["55","00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)","(?:[1-46-9]\\d\\d|5(?:[0-46-9]\\d|5[0-46-9]))\\d{8}|[1-9]\\d{9}|[3589]\\d{8}|[34]\\d{7}",[8,9,10,11],[["(\\d{4})(\\d{4})","$1-$2",["300|4(?:0[02]|37)","4(?:02|37)0|[34]00"]],["(\\d{3})(\\d{2,3})(\\d{4})","$1 $2 $3",["(?:[358]|90)0"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2-$3",["(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]"],"($1)"],["(\\d{2})(\\d{5})(\\d{4})","$1 $2-$3",["[16][1-9]|[2-57-9]"],"($1)"]],"0",0,"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\d{10,11}))?","$2",0,0,[["(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-5]\\d{7}",[10]],["(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])(?:7|9\\d)\\d{7}",[10,11]],["800\\d{6,7}",[9,10]],["300\\d{6}|[59]00\\d{6,7}",[9,10]],0,0,0,0,0,["(?:30[03]\\d{3}|4(?:0(?:0\\d|20)|370))\\d{4}|300\\d{5}",[8,10]]]],BS:["1","011","(?:242|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([3-8]\\d{6})$|1","242$1",0,"242",[["242(?:3(?:02|[236][1-9]|4[0-24-9]|5[0-68]|7[347]|8[0-4]|9[2-467])|461|502|6(?:0[1-5]|12|2[013]|[45]0|7[67]|8[78]|9[89])|7(?:02|88))\\d{4}"],["242(?:3(?:5[79]|7[56]|95)|4(?:[23][1-9]|4[1-35-9]|5[1-8]|6[2-8]|7\\d|81)|5(?:2[45]|3[35]|44|5[1-46-9]|65|77)|6[34]6|7(?:27|38)|8(?:0[1-9]|1[02-9]|2\\d|3[0-4]|[89]9))\\d{4}"],["242300\\d{4}|8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,["242225\\d{4}"]]],BT:["975","00","[17]\\d{7}|[2-8]\\d{6}",[7,8],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[2-68]|7[246]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[67]|7"]]],0,0,0,0,0,0,[["(?:2[3-6]|[34][5-7]|5[236]|6[2-46]|7[246]|8[2-4])\\d{5}",[7]],["(?:1[67]|77)\\d{6}",[8]]]],BW:["267","00","(?:0800|(?:[37]|800)\\d)\\d{6}|(?:[2-6]\\d|90)\\d{5}",[7,8,10],[["(\\d{2})(\\d{5})","$1 $2",["90"]],["(\\d{3})(\\d{4})","$1 $2",["[24-6]|3[15-9]"]],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[37]"]],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["0"]],["(\\d{3})(\\d{4})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["(?:2(?:4[0-48]|6[0-24]|9[0578])|3(?:1[0-35-9]|55|[69]\\d|7[013]|81)|4(?:6[03]|7[1267]|9[0-5])|5(?:3[03489]|4[0489]|7[1-47]|88|9[0-49])|6(?:2[1-35]|5[149]|8[013467]))\\d{4}",[7]],["(?:321|7[1-8]\\d)\\d{5}",[8]],["(?:0800|800\\d)\\d{6}",[10]],["90\\d{5}",[7]],0,0,0,0,["79(?:1(?:[0-2]\\d|3[0-3])|2[0-7]\\d)\\d{3}",[8]]]],BY:["375","810","(?:[12]\\d|33|44|902)\\d{7}|8(?:0[0-79]\\d{5,7}|[1-7]\\d{9})|8(?:1[0-489]|[5-79]\\d)\\d{7}|8[1-79]\\d{6,7}|8[0-79]\\d{5}|8\\d{5}",[6,7,8,9,10,11],[["(\\d{3})(\\d{3})","$1 $2",["800"],"8 $1"],["(\\d{3})(\\d{2})(\\d{2,4})","$1 $2 $3",["800"],"8 $1"],["(\\d{4})(\\d{2})(\\d{3})","$1 $2-$3",["1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])","1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])"],"8 0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2-$3-$4",["1(?:[56]|7[467])|2[1-3]"],"8 0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2-$3-$4",["[1-4]"],"8 0$1"],["(\\d{3})(\\d{3,4})(\\d{4})","$1 $2 $3",["[89]"],"8 $1"]],"8",0,"0|80?",0,0,0,[["(?:1(?:5(?:1[1-5]|[24]\\d|6[2-4]|9[1-7])|6(?:[235]\\d|4[1-7])|7\\d\\d)|2(?:1(?:[246]\\d|3[0-35-9]|5[1-9])|2(?:[235]\\d|4[0-8])|3(?:[26]\\d|3[02-79]|4[024-7]|5[03-7])))\\d{5}",[9]],["(?:2(?:5[5-79]|9[1-9])|(?:33|44)\\d)\\d{6}",[9]],["800\\d{3,7}|8(?:0[13]|20\\d)\\d{7}"],["(?:810|902)\\d{7}",[10]],0,0,0,0,["249\\d{6}",[9]]],"8~10"],BZ:["501","00","(?:0800\\d|[2-8])\\d{6}",[7,11],[["(\\d{3})(\\d{4})","$1-$2",["[2-8]"]],["(\\d)(\\d{3})(\\d{4})(\\d{3})","$1-$2-$3-$4",["0"]]],0,0,0,0,0,0,[["(?:2(?:[02]\\d|36|[68]0)|[3-58](?:[02]\\d|[68]0)|7(?:[02]\\d|32|[68]0))\\d{4}",[7]],["6[0-35-7]\\d{5}",[7]],["0800\\d{7}",[11]]]],CA:["1","011","(?:[2-8]\\d|90)\\d{8}|3\\d{6}",[7,10],0,"1",0,0,0,0,0,[["(?:2(?:04|[23]6|[48]9|50|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|7[39])|90[25])[2-9]\\d{6}",[10]],["",[10]],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}",[10]],["900[2-9]\\d{6}",[10]],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|(?:5(?:00|2[125-9]|33|44|66|77|88)|622)[2-9]\\d{6}",[10]],0,["310\\d{4}",[7]],0,["600[2-9]\\d{6}",[10]]]],CC:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{8}(?:\\d{2})?|8[0-24-9]\\d{7})|[148]\\d{8}|1\\d{5,7}",[6,7,8,9,10,12],0,"0",0,"([59]\\d{7})$|0","8$1",0,0,[["8(?:51(?:0(?:02|31|60|89)|1(?:18|76)|223)|91(?:0(?:1[0-2]|29)|1(?:[28]2|50|79)|2(?:10|64)|3(?:[06]8|22)|4[29]8|62\\d|70[23]|959))\\d{3}",[9]],["4(?:(?:79|94)[01]|83[0-389])\\d{5}|4(?:[0-3]\\d|4[047-9]|5[0-25-9]|6[0-36-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]],["180(?:0\\d{3}|2)\\d{3}",[7,10]],["190[0-26]\\d{6}",[10]],0,0,0,0,["14(?:5(?:1[0458]|[23][458])|71\\d)\\d{4}",[9]],["13(?:00\\d{6}(?:\\d{2})?|45[0-4]\\d{3})|13\\d{4}",[6,8,10,12]]],"0011"],CD:["243","00","[189]\\d{8}|[1-68]\\d{6}",[7,9],[["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["88"],"0$1"],["(\\d{2})(\\d{5})","$1 $2",["[1-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[["12\\d{7}|[1-6]\\d{6}"],["88\\d{5}|(?:8[0-69]|9[017-9])\\d{7}"]]],CF:["236","00","(?:[27]\\d{3}|8776)\\d{4}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[278]"]]],0,0,0,0,0,0,[["2[12]\\d{6}"],["7[024-7]\\d{6}"],0,["8776\\d{4}"]]],CG:["242","00","222\\d{6}|(?:0\\d|80)\\d{7}",[9],[["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["8"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[02]"]]],0,0,0,0,0,0,[["222[1-589]\\d{5}"],["026(?:1[0-5]|6[6-9])\\d{4}|0(?:[14-6]\\d\\d|2(?:40|5[5-8]|6[07-9]))\\d{5}"],0,["80[0-2]\\d{6}"]]],CH:["41","00","8\\d{11}|[2-9]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8[047]|90"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-79]|81"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["8"],"0$1"]],"0",0,0,0,0,0,[["(?:2[12467]|3[1-4]|4[134]|5[256]|6[12]|[7-9]1)\\d{7}"],["(?:6[89]|7[235-9])\\d{7}"],["800\\d{6}"],["90[016]\\d{6}"],["878\\d{6}"],0,["5[18]\\d{7}"],["74[0248]\\d{6}"],0,["84[0248]\\d{6}"]]],CI:["225","00","[02]\\d{9}",[10],[["(\\d{2})(\\d{2})(\\d)(\\d{5})","$1 $2 $3 $4",["2"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3 $4",["0"]]],0,0,0,0,0,0,[["2(?:[15]\\d{3}|7(?:2(?:0[23]|1[2357]|2[245]|3[45]|4[3-5])|3(?:06|1[69]|[2-6]7)))\\d{5}"],["0[157]\\d{8}"]]],CK:["682","00","[2-578]\\d{4}",[5],[["(\\d{2})(\\d{3})","$1 $2",["[2-578]"]]],0,0,0,0,0,0,[["(?:2\\d|3[13-7]|4[1-5])\\d{3}"],["[578]\\d{4}"]]],CL:["56","(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0","12300\\d{6}|6\\d{9,10}|[2-9]\\d{8}",[9,10,11],[["(\\d{5})(\\d{4})","$1 $2",["219","2196"],"($1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["44"]],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2[1-36]"],"($1)"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["9[2-9]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])"],"($1)"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["60|8"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]],["(\\d{3})(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3 $4",["60"]]],0,0,0,0,0,0,[["2(?:1982[0-6]|3314[05-9])\\d{3}|(?:2(?:1(?:160|962)|3(?:2\\d\\d|3(?:[03467]\\d|1[0-35-9]|2[1-9]|5[0-24-9]|8[0-3])|600)|646[59])|80[1-9]\\d\\d|9(?:3(?:[0-57-9]\\d\\d|6(?:0[02-9]|[1-9]\\d))|6(?:[0-8]\\d\\d|9(?:[02-79]\\d|1[05-9]))|7[1-9]\\d\\d|9(?:[03-9]\\d\\d|1(?:[0235-9]\\d|4[0-24-9])|2(?:[0-79]\\d|8[0-46-9]))))\\d{4}|(?:22|3[2-5]|[47][1-35]|5[1-3578]|6[13-57]|8[1-9]|9[2458])\\d{7}",[9]],["",[9]],["(?:123|8)00\\d{6}",[9,11]],0,0,0,0,0,["44\\d{7}",[9]],["600\\d{7,8}",[10,11]]]],CM:["237","00","[26]\\d{8}|88\\d{6,7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["88"]],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["[26]|88"]]],0,0,0,0,0,0,[["2(?:22|33)\\d{6}",[9]],["(?:24[23]|6[25-9]\\d)\\d{6}",[9]],["88\\d{6,7}"]]],CN:["86","00|1(?:[12]\\d|79)\\d\\d00","1[127]\\d{8,9}|2\\d{9}(?:\\d{2})?|[12]\\d{6,7}|86\\d{6}|(?:1[03-689]\\d|6)\\d{7,9}|(?:[3-579]\\d|8[0-57-9])\\d{6,9}",[7,8,9,10,11,12],[["(\\d{2})(\\d{5,6})","$1 $2",["(?:10|2[0-57-9])[19]","(?:10|2[0-57-9])(?:10|9[56])","10(?:10|9[56])|2[0-57-9](?:100|9[56])"],"0$1"],["(\\d{3})(\\d{5,6})","$1 $2",["3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]","(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))[19]","85[23](?:10|95)|(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))(?:10|9[56])","85[23](?:100|95)|(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))(?:100|9[56])"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["(?:4|80)0"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["10|2(?:[02-57-9]|1[1-9])","10|2(?:[02-57-9]|1[1-9])","10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]"],"0$1",1],["(\\d{3})(\\d{7,8})","$1 $2",["9"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["80"],"0$1",1],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["[3-578]"],"0$1",1],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["1[3-9]"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3 $4",["[12]"],"0$1",1]],"0",0,"(1(?:[12]\\d|79)\\d\\d)|0",0,0,0,[["(?:10(?:[02-79]\\d\\d|[18](?:0[1-9]|[1-9]\\d))|21(?:[18](?:0[1-9]|[1-9]\\d)|[2-79]\\d\\d))\\d{5}|(?:43[35]|754)\\d{7,8}|8(?:078\\d{7}|51\\d{7,8})|(?:10|(?:2|85)1|43[35]|754)(?:100\\d\\d|95\\d{3,4})|(?:2[02-57-9]|3(?:11|7[179])|4(?:[15]1|3[12])|5(?:1\\d|2[37]|3[12]|51|7[13-79]|9[15])|7(?:[39]1|5[57]|6[09])|8(?:71|98))(?:[02-8]\\d{7}|1(?:0(?:0\\d\\d(?:\\d{3})?|[1-9]\\d{5})|[1-9]\\d{6})|9(?:[0-46-9]\\d{6}|5\\d{3}(?:\\d(?:\\d{2})?)?))|(?:3(?:1[02-9]|35|49|5\\d|7[02-68]|9[1-68])|4(?:1[02-9]|2[179]|3[46-9]|5[2-9]|6[47-9]|7\\d|8[23])|5(?:3[03-9]|4[36]|5[02-9]|6[1-46]|7[028]|80|9[2-46-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[17]\\d|2[248]|3[04-9]|4[3-6]|5[0-3689]|6[2368]|9[02-9])|8(?:1[236-8]|2[5-7]|3\\d|5[2-9]|7[02-9]|8[36-8]|9[1-7])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))(?:[02-8]\\d{6}|1(?:0(?:0\\d\\d(?:\\d{2})?|[1-9]\\d{4})|[1-9]\\d{5})|9(?:[0-46-9]\\d{5}|5\\d{3,5}))",[7,8,9,10,11]],["1740[0-5]\\d{6}|1(?:[38]\\d|4[57]|[59][0-35-9]|6[25-7]|7[0-35-8])\\d{8}",[11]],["(?:(?:10|21)8|8)00\\d{7}",[10,12]],["16[08]\\d{5}",[8]],0,0,0,0,0,["10(?:10\\d{4}|96\\d{3,4})|400\\d{7}|950\\d{7,8}|(?:2[0-57-9]|3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))96\\d{3,4}",[7,8,9,10,11]]],"00"],CO:["57","00(?:4(?:[14]4|56)|[579])","(?:60\\d\\d|9101)\\d{6}|(?:1\\d|3)\\d{9}",[10,11],[["(\\d{3})(\\d{7})","$1 $2",["6"],"($1)"],["(\\d{3})(\\d{7})","$1 $2",["3[0-357]|91"]],["(\\d)(\\d{3})(\\d{7})","$1-$2-$3",["1"],"0$1",0,"$1 $2 $3"]],"0",0,"0([3579]|4(?:[14]4|56))?",0,0,0,[["601055(?:[0-4]\\d|50)\\d\\d|6010(?:[0-4]\\d|5[0-4])\\d{4}|60(?:[124-7][2-9]|8[1-9])\\d{6}",[10]],["333301[0-5]\\d{3}|3333(?:00|2[5-9]|[3-9]\\d)\\d{4}|(?:3(?:24[1-9]|3(?:00|3[0-24-9]))|9101)\\d{6}|3(?:0[0-5]|1\\d|2[0-3]|5[01]|70)\\d{7}",[10]],["1800\\d{7}",[11]],["19(?:0[01]|4[78])\\d{7}",[11]]]],CR:["506","00","(?:8\\d|90)\\d{8}|(?:[24-8]\\d{3}|3005)\\d{4}",[8,10],[["(\\d{4})(\\d{4})","$1 $2",["[2-7]|8[3-9]"]],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["[89]"]]],0,0,"(19(?:0[0-2468]|1[09]|20|66|77|99))",0,0,0,[["210[7-9]\\d{4}|2(?:[024-7]\\d|1[1-9])\\d{5}",[8]],["(?:3005\\d|6500[01])\\d{3}|(?:5[07]|6[0-4]|7[0-3]|8[3-9])\\d{6}",[8]],["800\\d{7}",[10]],["90[059]\\d{7}",[10]],0,0,0,0,["(?:210[0-6]|4\\d{3}|5100)\\d{4}",[8]]]],CU:["53","119","(?:[2-7]|8\\d\\d)\\d{7}|[2-47]\\d{6}|[34]\\d{5}",[6,7,8,10],[["(\\d{2})(\\d{4,6})","$1 $2",["2[1-4]|[34]"],"(0$1)"],["(\\d)(\\d{6,7})","$1 $2",["7"],"(0$1)"],["(\\d)(\\d{7})","$1 $2",["[56]"],"0$1"],["(\\d{3})(\\d{7})","$1 $2",["8"],"0$1"]],"0",0,0,0,0,0,[["(?:3[23]|4[89])\\d{4,6}|(?:31|4[36]|8(?:0[25]|78)\\d)\\d{6}|(?:2[1-4]|4[1257]|7\\d)\\d{5,6}"],["(?:5\\d|6[2-4])\\d{6}",[8]],["800\\d{7}",[10]],0,0,0,0,0,0,["807\\d{7}",[10]]]],CV:["238","0","(?:[2-59]\\d\\d|800)\\d{4}",[7],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[2-589]"]]],0,0,0,0,0,0,[["2(?:2[1-7]|3[0-8]|4[12]|5[1256]|6\\d|7[1-3]|8[1-5])\\d{4}"],["(?:36|5[1-389]|9\\d)\\d{5}"],["800\\d{4}"],0,0,0,0,0,["(?:3[3-5]|4[356])\\d{5}"]]],CW:["599","00","(?:[34]1|60|(?:7|9\\d)\\d)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["[3467]"]],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["9[4-8]"]]],0,0,0,0,0,"[69]",[["9(?:4(?:3[0-5]|4[14]|6\\d)|50\\d|7(?:2[014]|3[02-9]|4[4-9]|6[357]|77|8[7-9])|8(?:3[39]|[46]\\d|7[01]|8[57-9]))\\d{4}"],["953[01]\\d{4}|9(?:5[12467]|6[5-9])\\d{5}"],0,0,0,0,0,["955\\d{5}",[8]],0,["60[0-2]\\d{4}",[7]]]],CX:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{8}(?:\\d{2})?|8[0-24-9]\\d{7})|[148]\\d{8}|1\\d{5,7}",[6,7,8,9,10,12],0,"0",0,"([59]\\d{7})$|0","8$1",0,0,[["8(?:51(?:0(?:01|30|59|88)|1(?:17|46|75)|2(?:22|35))|91(?:00[6-9]|1(?:[28]1|49|78)|2(?:09|63)|3(?:12|26|75)|4(?:56|97)|64\\d|7(?:0[01]|1[0-2])|958))\\d{3}",[9]],["4(?:(?:79|94)[01]|83[0-389])\\d{5}|4(?:[0-3]\\d|4[047-9]|5[0-25-9]|6[0-36-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]],["180(?:0\\d{3}|2)\\d{3}",[7,10]],["190[0-26]\\d{6}",[10]],0,0,0,0,["14(?:5(?:1[0458]|[23][458])|71\\d)\\d{4}",[9]],["13(?:00\\d{6}(?:\\d{2})?|45[0-4]\\d{3})|13\\d{4}",[6,8,10,12]]],"0011"],CY:["357","00","(?:[279]\\d|[58]0)\\d{6}",[8],[["(\\d{2})(\\d{6})","$1 $2",["[257-9]"]]],0,0,0,0,0,0,[["2[2-6]\\d{6}"],["9(?:10|[4-79]\\d)\\d{5}"],["800\\d{5}"],["90[09]\\d{5}"],["700\\d{5}"],0,["(?:50|77)\\d{6}"],0,0,["80[1-9]\\d{5}"]]],CZ:["420","00","(?:[2-578]\\d|60)\\d{7}|9\\d{8,11}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[2-8]|9[015-7]"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{2})","$1 $2 $3 $4",["96"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]]],0,0,0,0,0,0,[["(?:2\\d|3[1257-9]|4[16-9]|5[13-9])\\d{7}"],["(?:60[1-8]\\d|7(?:0(?:[2-5]\\d|60)|190|[2379]\\d\\d))\\d{5}"],["800\\d{6}"],["9(?:0[05689]|76)\\d{6}"],["70[01]\\d{6}"],0,["9(?:5\\d|7[2-4])\\d{6}"],0,["9[17]0\\d{6}"],["8[134]\\d{7}"]]],DE:["49","00","[2579]\\d{5,14}|49(?:[34]0|69|8\\d)\\d\\d?|49(?:37|49|60|7[089]|9\\d)\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\d{1,8}|(?:1|[368]\\d|4[0-8])\\d{3,13}|49(?:[015]\\d|2[13]|31|[46][1-8])\\d{1,9}",[4,5,6,7,8,9,10,11,12,13,14,15],[["(\\d{2})(\\d{3,13})","$1 $2",["3[02]|40|[68]9"],"0$1"],["(\\d{3})(\\d{3,12})","$1 $2",["2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1","2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1"],"0$1"],["(\\d{4})(\\d{2,11})","$1 $2",["[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]","[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["138"],"0$1"],["(\\d{5})(\\d{2,10})","$1 $2",["3"],"0$1"],["(\\d{3})(\\d{5,11})","$1 $2",["181"],"0$1"],["(\\d{3})(\\d)(\\d{4,10})","$1 $2 $3",["1(?:3|80)|9"],"0$1"],["(\\d{3})(\\d{7,8})","$1 $2",["1[67]"],"0$1"],["(\\d{3})(\\d{7,12})","$1 $2",["8"],"0$1"],["(\\d{5})(\\d{6})","$1 $2",["185","1850","18500"],"0$1"],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{4})(\\d{7})","$1 $2",["18[68]"],"0$1"],["(\\d{4})(\\d{7})","$1 $2",["15[1279]"],"0$1"],["(\\d{5})(\\d{6})","$1 $2",["15[03568]","15(?:[0568]|31)"],"0$1"],["(\\d{3})(\\d{8})","$1 $2",["18"],"0$1"],["(\\d{3})(\\d{2})(\\d{7,8})","$1 $2 $3",["1(?:6[023]|7)"],"0$1"],["(\\d{4})(\\d{2})(\\d{7})","$1 $2 $3",["15[279]"],"0$1"],["(\\d{3})(\\d{2})(\\d{8})","$1 $2 $3",["15"],"0$1"]],"0",0,0,0,0,0,[["32\\d{9,11}|49[1-6]\\d{10}|322\\d{6}|49[0-7]\\d{3,9}|(?:[34]0|[68]9)\\d{3,13}|(?:2(?:0[1-689]|[1-3569]\\d|4[0-8]|7[1-7]|8[0-7])|3(?:[3569]\\d|4[0-79]|7[1-7]|8[1-8])|4(?:1[02-9]|[2-48]\\d|5[0-6]|6[0-8]|7[0-79])|5(?:0[2-8]|[124-6]\\d|[38][0-8]|[79][0-7])|6(?:0[02-9]|[1-358]\\d|[47][0-8]|6[1-9])|7(?:0[2-8]|1[1-9]|[27][0-7]|3\\d|[4-6][0-8]|8[0-5]|9[013-7])|8(?:0[2-9]|1[0-79]|2\\d|3[0-46-9]|4[0-6]|5[013-9]|6[1-8]|7[0-8]|8[0-24-6])|9(?:0[6-9]|[1-4]\\d|[589][0-7]|6[0-8]|7[0-467]))\\d{3,12}",[5,6,7,8,9,10,11,12,13,14,15]],["15(?:[0-25-9]\\d\\d|310)\\d{6}|1(?:6[023]|7\\d)\\d{7,8}",[10,11]],["800\\d{7,12}",[10,11,12,13,14,15]],["(?:137[7-9]|900(?:[135]|9\\d))\\d{6}",[10,11]],["700\\d{8}",[11]],0,["18(?:1\\d{5,11}|[2-9]\\d{8})",[8,9,10,11,12,13,14]],["16(?:4\\d{1,10}|[89]\\d{1,11})",[4,5,6,7,8,9,10,11,12,13,14]],0,["180\\d{5,11}|13(?:7[1-6]\\d\\d|8)\\d{4}",[7,8,9,10,11,12,13,14]]]],DJ:["253","00","(?:2\\d|77)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[27]"]]],0,0,0,0,0,0,[["2(?:1[2-5]|7[45])\\d{5}"],["77\\d{6}"]]],DK:["45","00","[2-9]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-9]"]]],0,0,0,0,0,0,[["(?:(?:2\\d|9[1-46-9])\\d|3(?:[0-37]\\d|4[013]|5[0-58]|6[01347-9]|8[0-8]|9[0-79])|4(?:[0-25]\\d|[34][02-9]|6[013-579]|7[013579]|8[0-47]|9[0-27])|5(?:[0-36]\\d|4[0146-9]|5[03-57-9]|7[0568]|8[0-358]|9[0-69])|6(?:[013578]\\d|2[0-68]|4[02-8]|6[01689]|9[015689])|7(?:[0-69]\\d|7[03-9]|8[0147])|8(?:[16-9]\\d|2[0-58]))\\d{5}"],["(?:[2-7]\\d|8[126-9]|9[1-46-9])\\d{6}"],["80\\d{6}"],["90\\d{6}"]]],DM:["1","011","(?:[58]\\d\\d|767|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","767$1",0,"767",[["767(?:2(?:55|66)|4(?:2[01]|4[0-25-9])|50[0-4])\\d{4}"],["767(?:2(?:[2-4689]5|7[5-7])|31[5-7]|61[1-8]|70[1-6])\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],DO:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,0,0,0,"8001|8[024]9",[["8(?:[04]9[2-9]\\d\\d|29(?:2(?:[0-59]\\d|6[04-9]|7[0-27]|8[0237-9])|3(?:[0-35-9]\\d|4[7-9])|[45]\\d\\d|6(?:[0-27-9]\\d|[3-5][1-9]|6[0135-8])|7(?:0[013-9]|[1-37]\\d|4[1-35689]|5[1-4689]|6[1-57-9]|8[1-79]|9[1-8])|8(?:0[146-9]|1[0-48]|[248]\\d|3[1-79]|5[01589]|6[013-68]|7[124-8]|9[0-8])|9(?:[0-24]\\d|3[02-46-9]|5[0-79]|60|7[0169]|8[57-9]|9[02-9])))\\d{4}"],["8[024]9[2-9]\\d{6}"],["8(?:00(?:14|[2-9]\\d)|(?:33|44|55|66|77|88)[2-9]\\d)\\d{5}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],DZ:["213","00","(?:[1-4]|[5-79]\\d|80)\\d{7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[1-4]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["9"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-8]"],"0$1"]],"0",0,0,0,0,0,[["9619\\d{5}|(?:1\\d|2[013-79]|3[0-8]|4[013-689])\\d{6}"],["(?:5(?:4[0-29]|5\\d|6[0-2])|6(?:[569]\\d|7[0-6])|7[7-9]\\d)\\d{6}",[9]],["800\\d{6}",[9]],["80[3-689]1\\d{5}",[9]],0,0,0,0,["98[23]\\d{6}",[9]],["80[12]1\\d{5}",[9]]]],EC:["593","00","1\\d{9,10}|(?:[2-7]|9\\d)\\d{7}",[8,9,10,11],[["(\\d)(\\d{3})(\\d{4})","$1 $2-$3",["[2-7]"],"(0$1)",0,"$1-$2-$3"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["9"],"0$1"],["(\\d{4})(\\d{3})(\\d{3,4})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[["[2-7][2-7]\\d{6}",[8]],["964[0-2]\\d{5}|9(?:39|[57][89]|6[0-36-9]|[89]\\d)\\d{6}",[9]],["1800\\d{7}|1[78]00\\d{6}",[10,11]],0,0,0,0,0,["[2-7]890\\d{4}",[8]]]],EE:["372","00","8\\d{9}|[4578]\\d{7}|(?:[3-8]\\d|90)\\d{5}",[7,8,10],[["(\\d{3})(\\d{4})","$1 $2",["[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88","[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88"]],["(\\d{4})(\\d{3,4})","$1 $2",["[45]|8(?:00|[1-49])","[45]|8(?:00[1-9]|[1-49])"]],["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["7"]],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["(?:3[23589]|4[3-8]|6\\d|7[1-9]|88)\\d{5}",[7]],["(?:5\\d{5}|8(?:1(?:0(?:0(?:00|[178]\\d)|[3-9]\\d\\d)|(?:1(?:0[236]|1\\d)|(?:2[0-59]|[3-79]\\d)\\d)\\d)|2(?:0(?:0(?:00|4\\d)|(?:19|[2-7]\\d)\\d)|(?:(?:[124-6]\\d|3[5-9])\\d|7(?:[0-79]\\d|8[13-9])|8(?:[2-6]\\d|7[01]))\\d)|[349]\\d{4}))\\d\\d|5(?:(?:[02]\\d|5[0-478])\\d|1(?:[0-8]\\d|95)|6(?:4[0-4]|5[1-589]))\\d{3}",[7,8]],["800(?:(?:0\\d\\d|1)\\d|[2-9])\\d{3}"],["(?:40\\d\\d|900)\\d{4}",[7,8]],["70[0-2]\\d{5}",[8]]]],EG:["20","00","[189]\\d{8,9}|[24-6]\\d{8}|[135]\\d{7}",[8,9,10],[["(\\d)(\\d{7,8})","$1 $2",["[23]"],"0$1"],["(\\d{2})(\\d{6,7})","$1 $2",["1[35]|[4-6]|8[2468]|9[235-7]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{2})(\\d{8})","$1 $2",["1"],"0$1"]],"0",0,0,0,0,0,[["13[23]\\d{6}|(?:15|57)\\d{6,7}|(?:2[2-4]|3|4[05-8]|5[05]|6[24-689]|8[2468]|9[235-7])\\d{7}",[8,9]],["1[0-25]\\d{8}",[10]],["800\\d{7}",[10]],["900\\d{7}",[10]]]],EH:["212","00","[5-8]\\d{8}",[9],0,"0",0,0,0,0,"528[89]",[["528[89]\\d{5}"],["(?:6(?:[0-79]\\d|8[0-247-9])|7(?:[0167]\\d|2[0-4]|5[01]|8[0-3]))\\d{6}"],["80[0-7]\\d{6}"],["89\\d{7}"],0,0,0,0,["(?:592(?:4[0-2]|93)|80[89]\\d\\d)\\d{4}"]]],ER:["291","00","[178]\\d{6}",[7],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[178]"],"0$1"]],"0",0,0,0,0,0,[["(?:1(?:1[12568]|[24]0|55|6[146])|8\\d\\d)\\d{4}"],["(?:17[1-3]|7\\d\\d)\\d{4}"]]],ES:["34","00","[5-9]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[89]00"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-9]"]]],0,0,0,0,0,0,[["96906(?:0[0-8]|1[1-9]|[2-9]\\d)\\d\\d|9(?:69(?:0[0-57-9]|[1-9]\\d)|73(?:[0-8]\\d|9[1-9]))\\d{4}|(?:8(?:[1356]\\d|[28][0-8]|[47][1-9])|9(?:[135]\\d|[268][0-8]|4[1-9]|7[124-9]))\\d{6}"],["(?:590[16]00\\d|9(?:6906(?:09|10)|7390\\d\\d))\\d\\d|(?:6\\d|7[1-48])\\d{7}"],["[89]00\\d{6}"],["80[367]\\d{6}"],["70\\d{7}"],0,["51\\d{7}"],0,0,["90[12]\\d{6}"]]],ET:["251","00","(?:11|[2-579]\\d)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-579]"],"0$1"]],"0",0,0,0,0,0,[["11667[01]\\d{3}|(?:11(?:1(?:1[124]|2[2-7]|3[1-5]|5[5-8]|8[6-8])|2(?:13|3[6-8]|5[89]|7[05-9]|8[2-6])|3(?:2[01]|3[0-289]|4[1289]|7[1-4]|87)|4(?:1[69]|3[2-49]|4[0-3]|6[5-8])|5(?:1[578]|44|5[0-4])|6(?:1[578]|2[69]|39|4[5-7]|5[0-5]|6[0-59]|8[015-8]))|2(?:2(?:11[1-9]|22[0-7]|33\\d|44[1467]|66[1-68])|5(?:11[124-6]|33[2-8]|44[1467]|55[14]|66[1-3679]|77[124-79]|880))|3(?:3(?:11[0-46-8]|(?:22|55)[0-6]|33[0134689]|44[04]|66[01467])|4(?:44[0-8]|55[0-69]|66[0-3]|77[1-5]))|4(?:6(?:119|22[0-24-7]|33[1-5]|44[13-69]|55[14-689]|660|88[1-4])|7(?:(?:11|22)[1-9]|33[13-7]|44[13-6]|55[1-689]))|5(?:7(?:227|55[05]|(?:66|77)[14-8])|8(?:11[149]|22[013-79]|33[0-68]|44[013-8]|550|66[1-5]|77\\d)))\\d{4}"],["700[1-9]\\d{5}|(?:7(?:0[1-9]|1[0-8]|22|77|86|99)|9\\d\\d)\\d{6}"]]],FI:["358","00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))","[1-35689]\\d{4}|7\\d{10,11}|(?:[124-7]\\d|3[0-46-9])\\d{8}|[1-9]\\d{5,8}",[5,6,7,8,9,10,11,12],[["(\\d{5})","$1",["20[2-59]"],"0$1"],["(\\d{3})(\\d{3,7})","$1 $2",["(?:[1-3]0|[68])0|70[07-9]"],"0$1"],["(\\d{2})(\\d{4,8})","$1 $2",["[14]|2[09]|50|7[135]"],"0$1"],["(\\d{2})(\\d{6,10})","$1 $2",["7"],"0$1"],["(\\d)(\\d{4,9})","$1 $2",["(?:1[3-79]|[2568])[1-8]|3(?:0[1-9]|[1-9])|9"],"0$1"]],"0",0,0,0,0,"1[03-79]|[2-9]",[["(?:1[3-79][1-8]|[23568][1-8]\\d|9(?:00|[1-8]\\d))\\d{2,6}",[5,6,7,8,9]],["4946\\d{2,6}|(?:4[0-8]|50)\\d{4,8}",[6,7,8,9,10]],["800\\d{4,6}",[7,8,9]],["[67]00\\d{5,6}",[8,9]],0,0,["20\\d{4,8}|60[12]\\d{5,6}|7(?:099\\d{4,5}|5[03-9]\\d{3,7})|20[2-59]\\d\\d|(?:606|7(?:0[78]|1|3\\d))\\d{7}|(?:10|29|3[09]|70[1-5]\\d)\\d{4,8}"]],"00"],FJ:["679","0(?:0|52)","45\\d{5}|(?:0800\\d|[235-9])\\d{6}",[7,11],[["(\\d{3})(\\d{4})","$1 $2",["[235-9]|45"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["0"]]],0,0,0,0,0,0,[["603\\d{4}|(?:3[0-5]|6[25-7]|8[58])\\d{5}",[7]],["(?:[279]\\d|45|5[01568]|8[034679])\\d{5}",[7]],["0800\\d{7}",[11]]],"00"],FK:["500","00","[2-7]\\d{4}",[5],0,0,0,0,0,0,0,[["[2-47]\\d{4}"],["[56]\\d{4}"]]],FM:["691","00","(?:[39]\\d\\d|820)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[389]"]]],0,0,0,0,0,0,[["31(?:00[67]|208|309)\\d\\d|(?:3(?:[2357]0[1-9]|602|804|905)|(?:820|9[2-6]\\d)\\d)\\d{3}"],["31(?:00[67]|208|309)\\d\\d|(?:3(?:[2357]0[1-9]|602|804|905)|(?:820|9[2-7]\\d)\\d)\\d{3}"]]],FO:["298","00","[2-9]\\d{5}",[6],[["(\\d{6})","$1",["[2-9]"]]],0,0,"(10(?:01|[12]0|88))",0,0,0,[["(?:20|[34]\\d|8[19])\\d{4}"],["(?:[27][1-9]|5\\d|9[16])\\d{4}"],["80[257-9]\\d{3}"],["90(?:[13-5][15-7]|2[125-7]|9\\d)\\d\\d"],0,0,0,0,["(?:6[0-36]|88)\\d{4}"]]],FR:["33","00","[1-9]\\d{8}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0 $1"],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["[1-79]"],"0$1"]],"0",0,0,0,0,0,[["(?:26[013-9]|59[1-35-9])\\d{6}|(?:[13]\\d|2[0-57-9]|4[1-9]|5[0-8])\\d{7}"],["(?:6(?:[0-24-8]\\d|3[0-8]|9[589])|7[3-9]\\d)\\d{6}"],["80[0-5]\\d{6}"],["836(?:0[0-36-9]|[1-9]\\d)\\d{4}|8(?:1[2-9]|2[2-47-9]|3[0-57-9]|[569]\\d|8[0-35-9])\\d{6}"],0,0,["80[6-9]\\d{6}"],0,["9\\d{8}"],["8(?:1[01]|2[0156]|4[02]|84)\\d{6}"]]],GA:["241","00","(?:[067]\\d|11)\\d{6}|[2-7]\\d{6}",[7,8],[["(\\d)(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-7]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["0"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[167]"],"0$1"]],"0",0,"0(11\\d{6}|60\\d{6}|61\\d{6}|6[256]\\d{6}|7[467]\\d{6})","$1",0,0,[["[01]1\\d{6}",[8]],["(?:(?:0[2-7]|7[467])\\d|6(?:0[0-4]|10|[256]\\d))\\d{5}|[2-7]\\d{6}"]]],GB:["44","00","[1-357-9]\\d{9}|[18]\\d{8}|8\\d{6}",[7,9,10],[["(\\d{3})(\\d{4})","$1 $2",["800","8001","80011","800111","8001111"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["845","8454","84546","845464"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["800"],"0$1"],["(\\d{5})(\\d{4,5})","$1 $2",["1(?:38|5[23]|69|76|94)","1(?:(?:38|69)7|5(?:24|39)|768|946)","1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)"],"0$1"],["(\\d{4})(\\d{5,6})","$1 $2",["1(?:[2-69][02-9]|[78])"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["[25]|7(?:0|6[02-9])","[25]|7(?:0|6(?:[03-9]|2[356]))"],"0$1"],["(\\d{4})(\\d{6})","$1 $2",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[1389]"],"0$1"]],"0",0,0,0,0,0,[["(?:1(?:1(?:3(?:[0-58]\\d\\d|73[0235])|4(?:(?:[0-5]\\d|70)\\d|69[7-9])|(?:(?:5[0-26-9]|[78][0-49])\\d|6(?:[0-4]\\d|50))\\d)|(?:2(?:(?:0[024-9]|2[3-9]|3[3-79]|4[1-689]|[58][02-9]|6[0-47-9]|7[013-9]|9\\d)\\d|1(?:[0-7]\\d|8[0-2]))|(?:3(?:0\\d|1[0-8]|[25][02-9]|3[02-579]|[468][0-46-9]|7[1-35-79]|9[2-578])|4(?:0[03-9]|[137]\\d|[28][02-57-9]|4[02-69]|5[0-8]|[69][0-79])|5(?:0[1-35-9]|[16]\\d|2[024-9]|3[015689]|4[02-9]|5[03-9]|7[0-35-9]|8[0-468]|9[0-57-9])|6(?:0[034689]|1\\d|2[0-35689]|[38][013-9]|4[1-467]|5[0-69]|6[13-9]|7[0-8]|9[0-24578])|7(?:0[0246-9]|2\\d|3[0236-8]|4[03-9]|5[0-46-9]|6[013-9]|7[0-35-9]|8[024-9]|9[02-9])|8(?:0[35-9]|2[1-57-9]|3[02-578]|4[0-578]|5[124-9]|6[2-69]|7\\d|8[02-9]|9[02569])|9(?:0[02-589]|[18]\\d|2[02-689]|3[1-57-9]|4[2-9]|5[0-579]|6[2-47-9]|7[0-24578]|9[2-57]))\\d)\\d)|2(?:0[013478]|3[0189]|4[017]|8[0-46-9]|9[0-2])\\d{3})\\d{4}|1(?:2(?:0(?:46[1-4]|87[2-9])|545[1-79]|76(?:2\\d|3[1-8]|6[1-6])|9(?:7(?:2[0-4]|3[2-5])|8(?:2[2-8]|7[0-47-9]|8[3-5])))|3(?:6(?:38[2-5]|47[23])|8(?:47[04-9]|64[0157-9]))|4(?:044[1-7]|20(?:2[23]|8\\d)|6(?:0(?:30|5[2-57]|6[1-8]|7[2-8])|140)|8(?:052|87[1-3]))|5(?:2(?:4(?:3[2-79]|6\\d)|76\\d)|6(?:26[06-9]|686))|6(?:06(?:4\\d|7[4-79])|295[5-7]|35[34]\\d|47(?:24|61)|59(?:5[08]|6[67]|74)|9(?:55[0-4]|77[23]))|7(?:26(?:6[13-9]|7[0-7])|(?:442|688)\\d|50(?:2[0-3]|[3-68]2|76))|8(?:27[56]\\d|37(?:5[2-5]|8[239])|843[2-58])|9(?:0(?:0(?:6[1-8]|85)|52\\d)|3583|4(?:66[1-8]|9(?:2[01]|81))|63(?:23|3[1-4])|9561))\\d{3}",[9,10]],["7(?:457[0-57-9]|700[01]|911[028])\\d{5}|7(?:[1-3]\\d\\d|4(?:[0-46-9]\\d|5[0-689])|5(?:0[0-8]|[13-9]\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\d|8[02-9]|9[0-689])|8(?:[014-9]\\d|[23][0-8])|9(?:[024-9]\\d|1[02-9]|3[0-689]))\\d{6}",[10]],["80[08]\\d{7}|800\\d{6}|8001111"],["(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\d|8[2-49]))\\d{7}|845464\\d",[7,10]],["70\\d{8}",[10]],0,["(?:3[0347]|55)\\d{8}",[10]],["76(?:464|652)\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\d{6}",[10]],["56\\d{8}",[10]]],0," x"],GD:["1","011","(?:473|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","473$1",0,"473",[["473(?:2(?:3[0-2]|69)|3(?:2[89]|86)|4(?:[06]8|3[5-9]|4[0-4]|5[579]|73|90)|63[68]|7(?:58|84)|800|938)\\d{4}"],["473(?:4(?:0[2-79]|1[04-9]|2[0-5]|49|5[68])|5(?:2[01]|3[3-8])|901)\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],GE:["995","00","(?:[3-57]\\d\\d|800)\\d{6}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["70"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["32"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[57]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[348]"],"0$1"]],"0",0,0,0,0,0,[["(?:3(?:[256]\\d|4[124-9]|7[0-4])|4(?:1\\d|2[2-7]|3[1-79]|4[2-8]|7[239]|9[1-7]))\\d{6}"],["5(?:(?:(?:0555|1(?:[17]77|555))[5-9]|757(?:7[7-9]|8[01]))\\d|22252[0-4])\\d\\d|(?:5(?:0(?:0(?:0\\d|11|22|3[0-6]|44|5[05]|77|88|9[09])|111|77\\d)|1(?:1(?:[03][01]|[124]\\d)|4\\d\\d)|[23]555|4(?:4\\d\\d|555)|5(?:[0157-9]\\d\\d|200)|6[89]\\d\\d|7(?:[0147-9]\\d\\d|5(?:00|[57]5))|8(?:0(?:[018]\\d|2[0-4])|5(?:55|8[89])|8(?:55|88))|9(?:090|[1-35-9]\\d\\d))|790\\d\\d)\\d{4}|5(?:0(?:0[17]0|505)|1(?:0[01]0|1(?:07|33|51))|2(?:0[02]0|2[25]2)|3(?:0[03]0|3[35]3)|(?:40[04]|900)0|5222)[0-4]\\d{3}"],["800\\d{6}"],0,0,0,0,0,["70[67]\\d{6}"]]],GF:["594","00","[56]94\\d{6}|(?:80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[56]|9[47]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[89]"],"0$1"]],"0",0,0,0,0,0,[["594(?:[02-49]\\d|1[0-5]|5[6-9]|6[0-3]|80)\\d{4}"],["694(?:[0-249]\\d|3[0-8])\\d{4}"],["80[0-5]\\d{6}"],0,0,0,0,0,["9(?:(?:396|76\\d)\\d|476[0-5])\\d{4}"]]],GG:["44","00","(?:1481|[357-9]\\d{3})\\d{6}|8\\d{6}(?:\\d{2})?",[7,9,10],0,"0",0,"([25-9]\\d{5})$|0","1481$1",0,0,[["1481[25-9]\\d{5}",[10]],["7(?:(?:781|839)\\d|911[17])\\d{5}",[10]],["80[08]\\d{7}|800\\d{6}|8001111"],["(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\d|8[0-3]))\\d{7}|845464\\d",[7,10]],["70\\d{8}",[10]],0,["(?:3[0347]|55)\\d{8}",[10]],["76(?:464|652)\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\d{6}",[10]],["56\\d{8}",[10]]]],GH:["233","00","(?:[235]\\d{3}|800)\\d{5}",[8,9],[["(\\d{3})(\\d{5})","$1 $2",["8"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[235]"],"0$1"]],"0",0,0,0,0,0,[["3082[0-5]\\d{4}|3(?:0(?:[237]\\d|8[01])|[167](?:2[0-6]|7\\d|80)|2(?:2[0-5]|7\\d|80)|3(?:2[0-3]|7\\d|80)|4(?:2[013-9]|3[01]|7\\d|80)|5(?:2[0-7]|7\\d|80)|8(?:2[0-2]|7\\d|80)|9(?:[28]0|7\\d))\\d{5}",[9]],["(?:2(?:[0346-9]\\d|5[67])|5(?:[03-7]\\d|9[1-9]))\\d{6}",[9]],["800\\d{5}",[8]]]],GI:["350","00","(?:[25]\\d|60)\\d{6}",[8],[["(\\d{3})(\\d{5})","$1 $2",["2"]]],0,0,0,0,0,0,[["2190[0-2]\\d{3}|2(?:0(?:[02]\\d|3[01])|16[24-9]|2[2-5]\\d)\\d{4}"],["5251[0-4]\\d{3}|(?:5(?:[146-8]\\d\\d|250)|60(?:1[01]|6\\d))\\d{4}"]]],GL:["299","00","(?:19|[2-689]\\d|70)\\d{4}",[6],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["19|[2-9]"]]],0,0,0,0,0,0,[["(?:19|3[1-7]|[68][1-9]|70|9\\d)\\d{4}"],["[245]\\d{5}"],["80\\d{4}"],0,0,0,0,0,["3[89]\\d{4}"]]],GM:["220","00","[2-9]\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[["(?:4(?:[23]\\d\\d|4(?:1[024679]|[6-9]\\d))|5(?:5(?:3\\d|4[0-7])|6[67]\\d|7(?:1[04]|2[035]|3[58]|48))|8\\d{3})\\d{3}"],["(?:[23679]\\d|5[0-489])\\d{5}"]]],GN:["224","00","722\\d{6}|(?:3|6\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["3"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[67]"]]],0,0,0,0,0,0,[["3(?:0(?:24|3[12]|4[1-35-7]|5[13]|6[189]|[78]1|9[1478])|1\\d\\d)\\d{4}",[8]],["6[0-356]\\d{7}",[9]],0,0,0,0,0,0,["722\\d{6}",[9]]]],GP:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[569]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[["590(?:0[1-68]|[14][0-24-9]|2[0-68]|3[1-9]|5[3-579]|[68][0-689]|7[08]|9\\d)\\d{4}"],["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"],["80[0-5]\\d{6}"],0,0,0,0,0,["9(?:(?:39[5-7]|76[018])\\d|475[0-5])\\d{4}"]]],GQ:["240","00","222\\d{6}|(?:3\\d|55|[89]0)\\d{7}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[235]"]],["(\\d{3})(\\d{6})","$1 $2",["[89]"]]],0,0,0,0,0,0,[["33[0-24-9]\\d[46]\\d{4}|3(?:33|5\\d)\\d[7-9]\\d{4}"],["(?:222|55\\d)\\d{6}"],["80\\d[1-9]\\d{5}"],["90\\d[1-9]\\d{5}"]]],GR:["30","00","5005000\\d{3}|8\\d{9,11}|(?:[269]\\d|70)\\d{8}",[10,11,12],[["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["21|7"]],["(\\d{4})(\\d{6})","$1 $2",["2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[2689]"]],["(\\d{3})(\\d{3,4})(\\d{5})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["2(?:1\\d\\d|2(?:2[1-46-9]|[36][1-8]|4[1-7]|5[1-4]|7[1-5]|[89][1-9])|3(?:1\\d|2[1-57]|[35][1-3]|4[13]|7[1-7]|8[124-6]|9[1-79])|4(?:1\\d|2[1-8]|3[1-4]|4[13-5]|6[1-578]|9[1-5])|5(?:1\\d|[29][1-4]|3[1-5]|4[124]|5[1-6])|6(?:1\\d|[269][1-6]|3[1245]|4[1-7]|5[13-9]|7[14]|8[1-5])|7(?:1\\d|2[1-5]|3[1-6]|4[1-7]|5[1-57]|6[135]|9[125-7])|8(?:1\\d|2[1-5]|[34][1-4]|9[1-57]))\\d{6}",[10]],["68[57-9]\\d{7}|(?:69|94)\\d{8}",[10]],["800\\d{7,9}"],["90[19]\\d{7}",[10]],["70\\d{8}",[10]],0,["5005000\\d{3}",[10]],0,0,["8(?:0[16]|12|[27]5|50)\\d{7}",[10]]]],GT:["502","00","80\\d{6}|(?:1\\d{3}|[2-7])\\d{7}",[8,11],[["(\\d{4})(\\d{4})","$1 $2",["[2-8]"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[["[267][2-9]\\d{6}",[8]],["(?:[3-5]\\d\\d|80[0-4])\\d{5}",[8]],["18[01]\\d{8}",[11]],["19\\d{9}",[11]]]],GU:["1","011","(?:[58]\\d\\d|671|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","671$1",0,"671",[["671(?:2\\d\\d|3(?:00|3[39]|4[349]|55|6[26])|4(?:00|56|7[1-9]|8[02-46-9])|5(?:55|6[2-5]|88)|6(?:3[2-578]|4[24-9]|5[34]|78|8[235-9])|7(?:[0479]7|2[0167]|3[45]|8[7-9])|8(?:[2-57-9]8|6[48])|9(?:2[29]|6[79]|7[1279]|8[7-9]|9[78]))\\d{4}"],[""],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],GW:["245","00","[49]\\d{8}|4\\d{6}",[7,9],[["(\\d{3})(\\d{4})","$1 $2",["40"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[49]"]]],0,0,0,0,0,0,[["443\\d{6}",[9]],["9(?:5\\d|6[569]|77)\\d{6}",[9]],0,0,0,0,0,0,["40\\d{5}",[7]]]],GY:["592","001","(?:[2-8]\\d{3}|9008)\\d{3}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[["(?:2(?:1[6-9]|2[0-35-9]|3[1-4]|5[3-9]|6\\d|7[0-79])|3(?:2[25-9]|3\\d)|4(?:4[0-24]|5[56])|50[0-6]|77[1-57])\\d{4}"],["(?:510|6\\d\\d|7(?:[01]\\d|2[0-256]|3[0-2]|49))\\d{4}"],["(?:289|8(?:00|6[28]|88|99))\\d{4}"],["9008\\d{3}"],0,0,0,0,["515\\d{4}"]]],HK:["852","00(?:30|5[09]|[126-9]?)","8[0-46-9]\\d{6,7}|9\\d{4,7}|(?:[2-7]|9\\d{3})\\d{7}",[5,6,7,8,9,11],[["(\\d{3})(\\d{2,5})","$1 $2",["900","9003"]],["(\\d{4})(\\d{4})","$1 $2",["[2-7]|8[1-4]|9(?:0[1-9]|[1-8])"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]],["(\\d{3})(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]]],0,0,0,0,0,0,[["(?:2(?:[13-9]\\d|2[013-9])\\d|3(?:(?:[1569][0-24-9]|4[0-246-9]|7[0-24-69])\\d|8(?:4[0-8]|[59]\\d|6[01]))|58(?:0[1-9]|1[2-9]))\\d{4}",[8]],["(?:4(?:44[0-25-9]|6(?:1[0-7]|4[0-57-9]|6[0-4]))|5(?:73[0-6]|95[0-8])|6(?:26[013-8]|66[0-3])|70(?:7[1-8]|8[0-4])|848[0-35-9]|9(?:29[013-9]|39[01]|59[0-4]|899))\\d{4}|(?:4(?:4[0-35-8]|6[02357-9])|5(?:[1-59][0-46-9]|6[0-4689]|7[0-246-9])|6(?:0[1-9]|[13-59]\\d|[268][0-57-9]|7[0-79])|70[129]|84[0-29]|9(?:0[1-9]|1[02-9]|[2358][0-8]|[467]\\d))\\d{5}",[8]],["800\\d{6}",[9]],["900(?:[0-24-9]\\d{7}|3\\d{1,4})",[5,6,7,8,11]],["8(?:1[0-4679]\\d|2(?:[0-36]\\d|7[0-4])|3(?:[034]\\d|2[09]|70))\\d{4}",[8]],0,["30(?:0[1-9]|[15-7]\\d|2[047]|89)\\d{4}",[8]],["7(?:1(?:0[0-38]|1[0-3679]|3[013]|69|9[0136])|2(?:[02389]\\d|1[18]|7[27-9])|3(?:[0-38]\\d|7[0-369]|9[2357-9])|47\\d|5(?:[178]\\d|5[0-5])|6(?:0[0-7]|2[236-9]|[35]\\d)|7(?:[27]\\d|8[7-9])|8(?:[23689]\\d|7[1-9])|9(?:[025]\\d|6[0-246-8]|7[0-36-9]|8[238]))\\d{4}",[8]]],"00"],HN:["504","00","8\\d{10}|[237-9]\\d{7}",[8,11],[["(\\d{4})(\\d{4})","$1-$2",["[237-9]"]]],0,0,0,0,0,0,[["2(?:2(?:0[0-59]|1[1-9]|[23]\\d|4[02-6]|5[57]|6[245]|7[0135689]|8[01346-9]|9[0-2])|4(?:0[578]|2[3-59]|3[13-9]|4[0-68]|5[1-3589])|5(?:0[2357-9]|1[1-356]|4[03-5]|5\\d|6[014-69]|7[04]|80)|6(?:[056]\\d|17|2[067]|3[047]|4[0-378]|[78][0-8]|9[01])|7(?:0[5-79]|6[46-9]|7[02-9]|8[034]|91)|8(?:79|8[0-357-9]|9[1-57-9]))\\d{4}",[8]],["[37-9]\\d{7}",[8]],["8002\\d{7}",[11]]]],HR:["385","00","(?:[24-69]\\d|3[0-79])\\d{7}|80\\d{5,7}|[1-79]\\d{7}|6\\d{5,6}",[6,7,8,9],[["(\\d{2})(\\d{2})(\\d{2,3})","$1 $2 $3",["6[01]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{4})(\\d{3})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["6|7[245]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["9"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-57]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[["1\\d{7}|(?:2[0-3]|3[1-5]|4[02-47-9]|5[1-3])\\d{6,7}",[8,9]],["9(?:(?:0[1-9]|[12589]\\d)\\d\\d|7(?:[0679]\\d\\d|5(?:[01]\\d|44|55|77|9[5-7])))\\d{4}|98\\d{6}",[8,9]],["80\\d{5,7}",[7,8,9]],["6[01459]\\d{6}|6[01]\\d{4,5}",[6,7,8]],["7[45]\\d{6}",[8]],0,["62\\d{6,7}|72\\d{6}",[8,9]]]],HT:["509","00","(?:[2-489]\\d|55)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["[2-589]"]]],0,0,0,0,0,0,[["2(?:2\\d|5[1-5]|81|9[149])\\d{5}"],["(?:[34]\\d|55)\\d{6}"],["8\\d{7}"],0,0,0,0,0,["9(?:[67][0-4]|8[0-3589]|9\\d)\\d{5}"]]],HU:["36","00","[235-7]\\d{8}|[1-9]\\d{7}",[8,9],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["1"],"(06 $1)"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]"],"(06 $1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-9]"],"06 $1"]],"06",0,0,0,0,0,[["(?:1\\d|[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6[23689]|8[2-57-9]|9[2-69])\\d{6}",[8]],["(?:[257]0|3[01])\\d{7}",[9]],["(?:[48]0\\d|680[29])\\d{5}"],["9[01]\\d{6}",[8]],0,0,["38\\d{7}",[9]],0,["21\\d{7}",[9]]]],ID:["62","00[89]","(?:(?:00[1-9]|8\\d)\\d{4}|[1-36])\\d{6}|00\\d{10}|[1-9]\\d{8,10}|[2-9]\\d{7}",[7,8,9,10,11,12,13],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["15"]],["(\\d{2})(\\d{5,9})","$1 $2",["2[124]|[36]1"],"(0$1)"],["(\\d{3})(\\d{5,7})","$1 $2",["800"],"0$1"],["(\\d{3})(\\d{5,8})","$1 $2",["[2-79]"],"(0$1)"],["(\\d{3})(\\d{3,4})(\\d{3})","$1-$2-$3",["8[1-35-9]"],"0$1"],["(\\d{3})(\\d{6,8})","$1 $2",["1"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["804"],"0$1"],["(\\d{3})(\\d)(\\d{3})(\\d{3})","$1 $2 $3 $4",["80"],"0$1"],["(\\d{3})(\\d{4})(\\d{4,5})","$1-$2-$3",["8"],"0$1"]],"0",0,0,0,0,0,[["2[124]\\d{7,8}|619\\d{8}|2(?:1(?:14|500)|2\\d{3})\\d{3}|61\\d{5,8}|(?:2(?:[35][1-4]|6[0-8]|7[1-6]|8\\d|9[1-8])|3(?:1|[25][1-8]|3[1-68]|4[1-3]|6[1-3568]|7[0-469]|8\\d)|4(?:0[1-589]|1[01347-9]|2[0-36-8]|3[0-24-68]|43|5[1-378]|6[1-5]|7[134]|8[1245])|5(?:1[1-35-9]|2[25-8]|3[124-9]|4[1-3589]|5[1-46]|6[1-8])|6(?:[25]\\d|3[1-69]|4[1-6])|7(?:02|[125][1-9]|[36]\\d|4[1-8]|7[0-36-9])|9(?:0[12]|1[013-8]|2[0-479]|5[125-8]|6[23679]|7[159]|8[01346]))\\d{5,8}",[7,8,9,10,11]],["8[1-35-9]\\d{7,10}",[9,10,11,12]],["00[17]803\\d{7}|(?:177\\d|800)\\d{5,7}|001803\\d{6}",[8,9,10,11,12,13]],["809\\d{7}",[10]],0,0,["(?:1500|8071\\d{3})\\d{3}",[7,10]],0,0,["804\\d{7}",[10]]]],IE:["353","00","(?:1\\d|[2569])\\d{6,8}|4\\d{6,9}|7\\d{8}|8\\d{8,9}",[7,8,9,10],[["(\\d{2})(\\d{5})","$1 $2",["2[24-9]|47|58|6[237-9]|9[35-9]"],"(0$1)"],["(\\d{3})(\\d{5})","$1 $2",["[45]0"],"(0$1)"],["(\\d)(\\d{3,4})(\\d{4})","$1 $2 $3",["1"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2569]|4[1-69]|7[14]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["70"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["81"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[78]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["4"],"(0$1)"],["(\\d{2})(\\d)(\\d{3})(\\d{4})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[["(?:1\\d|21)\\d{6,7}|(?:2[24-9]|4(?:0[24]|5\\d|7)|5(?:0[45]|1\\d|8)|6(?:1\\d|[237-9])|9(?:1\\d|[35-9]))\\d{5}|(?:23|4(?:[1-469]|8\\d)|5[23679]|6[4-6]|7[14]|9[04])\\d{7}"],["8(?:22|[35-9]\\d)\\d{6}",[9]],["1800\\d{6}",[10]],["15(?:1[2-8]|[2-8]0|9[089])\\d{6}",[10]],["700\\d{6}",[9]],0,["818\\d{6}",[9]],0,["76\\d{7}",[9]],["18[59]0\\d{6}",[10]]]],IL:["972","0(?:0|1[2-9])","1\\d{6}(?:\\d{3,5})?|[57]\\d{8}|[1-489]\\d{7}",[7,8,9,10,11,12],[["(\\d{4})(\\d{3})","$1-$2",["125"]],["(\\d{4})(\\d{2})(\\d{2})","$1-$2-$3",["121"]],["(\\d)(\\d{3})(\\d{4})","$1-$2-$3",["[2-489]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["[57]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1-$2-$3",["12"]],["(\\d{4})(\\d{6})","$1-$2",["159"]],["(\\d)(\\d{3})(\\d{3})(\\d{3})","$1-$2-$3-$4",["1[7-9]"]],["(\\d{3})(\\d{1,2})(\\d{3})(\\d{4})","$1-$2 $3-$4",["15"]]],"0",0,0,0,0,0,[["153\\d{8,9}|29[1-9]\\d{5}|(?:2[0-8]|[3489]\\d)\\d{6}",[8,11,12]],["55410\\d{4}|5(?:(?:[02][02-9]|[149][2-9]|[36]\\d|8[3-7])\\d|5(?:01|2\\d|3[0-3]|4[34]|5[0-25689]|6[6-8]|7[0-267]|8[7-9]|9[1-9]))\\d{5}",[9]],["1(?:255|80[019]\\d{3})\\d{3}",[7,10]],["1212\\d{4}|1(?:200|9(?:0[0-2]|19))\\d{6}",[8,10]],0,0,["1599\\d{6}",[10]],0,["7(?:38(?:0\\d|5[0-29]|88)|8(?:33|55|77|81)\\d)\\d{4}|7(?:18|2[23]|3[237]|47|6[258]|7\\d|82|9[2-9])\\d{6}",[9]],["1700\\d{6}",[10]]]],IM:["44","00","1624\\d{6}|(?:[3578]\\d|90)\\d{8}",[10],0,"0",0,"([25-8]\\d{5})$|0","1624$1",0,"74576|(?:16|7[56])24",[["1624(?:230|[5-8]\\d\\d)\\d{3}"],["76245[06]\\d{4}|7(?:4576|[59]24\\d|624[0-4689])\\d{5}"],["808162\\d{4}"],["8(?:440[49]06|72299\\d)\\d{3}|(?:8(?:45|70)|90[0167])624\\d{4}"],["70\\d{8}"],0,["3440[49]06\\d{3}|(?:3(?:08162|3\\d{4}|45624|7(?:0624|2299))|55\\d{4})\\d{4}"],0,["56\\d{8}"]]],IN:["91","00","(?:000800|[2-9]\\d\\d)\\d{7}|1\\d{7,12}",[8,9,10,11,12,13],[["(\\d{8})","$1",["5(?:0|2[23]|3[03]|[67]1|88)","5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)","5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)"],0,1],["(\\d{4})(\\d{4,5})","$1 $2",["180","1800"],0,1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["140"],0,1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["11|2[02]|33|4[04]|79[1-7]|80[2-46]","11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])","11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]","1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]","1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]"],"0$1",1],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807","1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]","1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\d|7(?:1(?:[013-8]\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\d|5[0-367])|70[13-7]))[2-7]"],"0$1",1],["(\\d{5})(\\d{5})","$1 $2",["[6-9]"],"0$1",1],["(\\d{4})(\\d{2,4})(\\d{4})","$1 $2 $3",["1(?:6|8[06])","1(?:6|8[06]0)"],0,1],["(\\d{4})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["18"],0,1]],"0",0,0,0,0,0,[["2717(?:[2-7]\\d|95)\\d{4}|(?:271[0-689]|782[0-6])[2-7]\\d{5}|(?:170[24]|2(?:(?:[02][2-79]|90)\\d|80[13468])|(?:3(?:23|80)|683|79[1-7])\\d|4(?:20[24]|72[2-8])|552[1-7])\\d{6}|(?:11|33|4[04]|80)[2-7]\\d{7}|(?:342|674|788)(?:[0189][2-7]|[2-7]\\d)\\d{5}|(?:1(?:2[0-249]|3[0-25]|4[145]|[59][14]|6[014]|7[1257]|8[01346])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568]|9[14])|3(?:26|4[13]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[014-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|2[14]|3[134]|4[47]|5[15]|[67]1)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91))[2-7]\\d{6}|(?:1(?:2[35-8]|3[346-9]|4[236-9]|[59][0235-9]|6[235-9]|7[34689]|8[257-9])|2(?:1[134689]|3[24-8]|4[2-8]|5[25689]|6[2-4679]|7[3-79]|8[2-479]|9[235-9])|3(?:01|1[79]|2[1245]|4[5-8]|5[125689]|6[235-7]|7[157-9]|8[2-46-8])|4(?:1[14578]|2[5689]|3[2-467]|5[4-7]|6[35]|73|8[2689]|9[2389])|5(?:[16][146-9]|2[14-8]|3[1346]|4[14-69]|5[46]|7[2-4]|8[2-8]|9[246])|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])|7(?:1[013-9]|2[0235-9]|3[2679]|4[1-35689]|5[2-46-9]|[67][02-9]|8[013-7]|9[089])|8(?:1[1357-9]|2[235-8]|3[03-57-9]|4[0-24-9]|5\\d|6[2457-9]|7[1-6]|8[1256]|9[2-4]))\\d[2-7]\\d{5}",[10]],["(?:61279|7(?:887[02-9]|9(?:313|79[07-9]))|8(?:079[04-9]|(?:84|91)7[02-8]))\\d{5}|(?:6(?:12|[2-47]1|5[17]|6[13]|80)[0189]|7(?:1(?:2[0189]|9[0-5])|2(?:[14][017-9]|8[0-59])|3(?:2[5-8]|[34][017-9]|9[016-9])|4(?:1[015-9]|[29][89]|39|8[389])|5(?:[15][017-9]|2[04-9]|9[7-9])|6(?:0[0-47]|1[0-257-9]|2[0-4]|3[19]|5[4589])|70[0289]|88[089]|97[02-8])|8(?:0(?:6[67]|7[02-8])|70[017-9]|84[01489]|91[0-289]))\\d{6}|(?:7(?:31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[0189]\\d|7[02-8])\\d{5}|(?:6(?:[09]\\d|1[04679]|2[03689]|3[05-9]|4[0489]|50|6[069]|7[07]|8[7-9])|7(?:0\\d|2[0235-79]|3[05-8]|40|5[0346-8]|6[6-9]|7[1-9]|8[0-79]|9[089])|8(?:0[01589]|1[0-57-9]|2[235-9]|3[03-57-9]|[45]\\d|6[02457-9]|7[1-69]|8[0-25-9]|9[02-9])|9\\d\\d)\\d{7}|(?:6(?:(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|8[124-6])\\d|7(?:[235689]\\d|4[0189]))|7(?:1(?:[013-8]\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-5])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\d|5[0-367])|70[13-7]|881))[0189]\\d{5}",[10]],["000800\\d{7}|1(?:600\\d{6}|80(?:0\\d{4,9}|3\\d{9}))"],["186[12]\\d{9}",[13]],0,0,["140\\d{7}",[10]],0,0,["1860\\d{7}",[11]]]],IO:["246","00","3\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["3"]]],0,0,0,0,0,0,[["37\\d{5}"],["38\\d{5}"]]],IQ:["964","00","(?:1|7\\d\\d)\\d{7}|[2-6]\\d{7,8}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-6]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[["1\\d{7}|(?:2[13-5]|3[02367]|4[023]|5[03]|6[026])\\d{6,7}",[8,9]],["7[3-9]\\d{8}",[10]]]],IR:["98","00","[1-9]\\d{9}|(?:[1-8]\\d\\d|9)\\d{3,4}",[4,5,6,7,10],[["(\\d{4,5})","$1",["96"],"0$1"],["(\\d{2})(\\d{4,5})","$1 $2",["(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["9"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["[1-8]"],"0$1"]],"0",0,0,0,0,0,[["(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])(?:[03-57]\\d{7}|[16]\\d{3}(?:\\d{4})?|[289]\\d{3}(?:\\d(?:\\d{3})?)?)|94(?:000[09]|(?:12\\d|30[0-2])\\d|2(?:121|[2689]0\\d)|4(?:111|40\\d))\\d{4}",[6,7,10]],["9(?:(?:0(?:[0-35]\\d|4[4-6])|(?:[13]\\d|2[0-3])\\d)\\d|9(?:[0-46]\\d\\d|5[15]0|8(?:[12]\\d|88)|9(?:0[0-3]|[19]\\d|21|69|77|8[7-9])))\\d{5}",[10]],0,0,0,0,["96(?:0[12]|2[16-8]|3(?:08|[14]5|[23]|66)|4(?:0|80)|5[01]|6[89]|86|9[19])",[4,5]]]],IS:["354","00|1(?:0(?:01|[12]0)|100)","(?:38\\d|[4-9])\\d{6}",[7,9],[["(\\d{3})(\\d{4})","$1 $2",["[4-9]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["3"]]],0,0,0,0,0,0,[["(?:4(?:1[0-24-69]|2[0-7]|[37][0-8]|4[0-24589]|5[0-68]|6\\d|8[0-36-8])|5(?:05|[156]\\d|2[02578]|3[0-579]|4[03-7]|7[0-2578]|8[0-35-9]|9[013-689])|872)\\d{4}",[7]],["(?:38[589]\\d\\d|6(?:1[1-8]|2[0-6]|3[026-9]|4[014679]|5[0159]|6[0-69]|70|8[06-8]|9\\d)|7(?:5[057]|[6-9]\\d)|8(?:2[0-59]|[3-69]\\d|8[238]))\\d{4}"],["80[0-8]\\d{4}",[7]],["90(?:0\\d|1[5-79]|2[015-79]|3[135-79]|4[125-7]|5[25-79]|7[1-37]|8[0-35-7])\\d{3}",[7]],0,0,["809\\d{4}",[7]],0,["49[0-24-79]\\d{4}",[7]]],"00"],IT:["39","00","0\\d{5,10}|1\\d{8,10}|3(?:[0-8]\\d{7,10}|9\\d{7,8})|(?:43|55|70)\\d{8}|8\\d{5}(?:\\d{2,4})?",[6,7,8,9,10,11],[["(\\d{2})(\\d{4,6})","$1 $2",["0[26]"]],["(\\d{3})(\\d{3,6})","$1 $2",["0[13-57-9][0159]|8(?:03|4[17]|9[2-5])","0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))"]],["(\\d{4})(\\d{2,6})","$1 $2",["0(?:[13-579][2-46-8]|8[236-8])"]],["(\\d{4})(\\d{4})","$1 $2",["894"]],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["0[26]|5"]],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["1(?:44|[679])|[378]|43"]],["(\\d{3})(\\d{3,4})(\\d{4})","$1 $2 $3",["0[13-57-9][0159]|14"]],["(\\d{2})(\\d{4})(\\d{5})","$1 $2 $3",["0[26]"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["0"]],["(\\d{3})(\\d{4})(\\d{4,5})","$1 $2 $3",["3"]]],0,0,0,0,0,0,[["0669[0-79]\\d{1,6}|0(?:1(?:[0159]\\d|[27][1-5]|31|4[1-4]|6[1356]|8[2-57])|2\\d\\d|3(?:[0159]\\d|2[1-4]|3[12]|[48][1-6]|6[2-59]|7[1-7])|4(?:[0159]\\d|[23][1-9]|4[245]|6[1-5]|7[1-4]|81)|5(?:[0159]\\d|2[1-5]|3[2-6]|4[1-79]|6[4-6]|7[1-578]|8[3-8])|6(?:[0-57-9]\\d|6[0-8])|7(?:[0159]\\d|2[12]|3[1-7]|4[2-46]|6[13569]|7[13-6]|8[1-59])|8(?:[0159]\\d|2[3-578]|3[1-356]|[6-8][1-5])|9(?:[0159]\\d|[238][1-5]|4[12]|6[1-8]|7[1-6]))\\d{2,7}"],["3[2-9]\\d{7,8}|(?:31|43)\\d{8}",[9,10]],["80(?:0\\d{3}|3)\\d{3}",[6,9]],["(?:0878\\d{3}|89(?:2\\d|3[04]|4(?:[0-4]|[5-9]\\d\\d)|5[0-4]))\\d\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\d{6}",[6,8,9,10]],["1(?:78\\d|99)\\d{6}",[9,10]],0,0,0,["55\\d{8}",[10]],["84(?:[08]\\d{3}|[17])\\d{3}",[6,9]]]],JE:["44","00","1534\\d{6}|(?:[3578]\\d|90)\\d{8}",[10],0,"0",0,"([0-24-8]\\d{5})$|0","1534$1",0,0,[["1534[0-24-8]\\d{5}"],["7(?:(?:(?:50|82)9|937)\\d|7(?:00[378]|97\\d))\\d{5}"],["80(?:07(?:35|81)|8901)\\d{4}"],["(?:8(?:4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|90(?:066[59]|1810|71(?:07|55)))\\d{4}"],["701511\\d{4}"],0,["(?:3(?:0(?:07(?:35|81)|8901)|3\\d{4}|4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|55\\d{4})\\d{4}"],["76(?:464|652)\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\d{6}"],["56\\d{8}"]]],JM:["1","011","(?:[58]\\d\\d|658|900)\\d{7}",[10],0,"1",0,0,0,0,"658|876",[["8766060\\d{3}|(?:658(?:2(?:[0-8]\\d|9[0-46-9])|[3-9]\\d\\d)|876(?:52[35]|6(?:0[1-3579]|1[0235-9]|[23]\\d|40|5[06]|6[2-589]|7[0-25-9]|8[04]|9[4-9])|7(?:0[2-689]|[1-6]\\d|8[056]|9[45])|9(?:0[1-8]|1[02378]|[2-8]\\d|9[2-468])))\\d{4}"],["(?:658295|876(?:2(?:0[1-9]|[13-9]\\d|2[013-9])|[348]\\d\\d|5(?:0[1-9]|[1-9]\\d)|6(?:4[89]|6[67])|7(?:0[07]|7\\d|8[1-47-9]|9[0-36-9])|9(?:[01]9|9[0579])))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],JO:["962","00","(?:(?:[2689]|7\\d)\\d|32|53)\\d{6}",[8,9],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2356]|87"],"(0$1)"],["(\\d{3})(\\d{5,6})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["70"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[["87(?:000|90[01])\\d{3}|(?:2(?:6(?:2[0-35-9]|3[0-578]|4[24-7]|5[0-24-8]|[6-8][023]|9[0-3])|7(?:0[1-79]|10|2[014-7]|3[0-689]|4[019]|5[0-3578]))|32(?:0[1-69]|1[1-35-7]|2[024-7]|3\\d|4[0-3]|[5-7][023])|53(?:0[0-3]|[13][023]|2[0-59]|49|5[0-35-9]|6[15]|7[45]|8[1-6]|9[0-36-9])|6(?:2(?:[05]0|22)|3(?:00|33)|4(?:0[0-25]|1[2-7]|2[0569]|[38][07-9]|4[025689]|6[0-589]|7\\d|9[0-2])|5(?:[01][056]|2[034]|3[0-57-9]|4[178]|5[0-69]|6[0-35-9]|7[1-379]|8[0-68]|9[0239]))|87(?:20|7[078]|99))\\d{4}",[8]],["7(?:[78][0-25-9]|9\\d)\\d{6}",[9]],["80\\d{6}",[8]],["9\\d{7}",[8]],["70\\d{7}",[9]],0,["8(?:10|8\\d)\\d{5}",[8]],["74(?:66|77)\\d{5}",[9]],0,["85\\d{6}",[8]]]],JP:["81","010","00[1-9]\\d{6,14}|[257-9]\\d{9}|(?:00|[1-9]\\d\\d)\\d{6}",[8,9,10,11,12,13,14,15,16,17],[["(\\d{3})(\\d{3})(\\d{3})","$1-$2-$3",["(?:12|57|99)0"],"0$1"],["(\\d{4})(\\d)(\\d{4})","$1-$2-$3",["1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])","1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]","1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["60"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1-$2-$3",["[36]|4(?:2[09]|7[01])","[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])","1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]","1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]"],"0$1"],["(\\d{3})(\\d{2})(\\d{4})","$1-$2-$3",["[14]|[289][2-9]|5[3-9]|7[2-4679]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["800"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2-$3",["[257-9]"],"0$1"]],"0",0,"(000[259]\\d{6})$|(?:(?:003768)0?)|0","$1",0,0,[["(?:1(?:1[235-8]|2[3-6]|3[3-9]|4[2-6]|[58][2-8]|6[2-7]|7[2-9]|9[1-9])|(?:2[2-9]|[36][1-9])\\d|4(?:[2-578]\\d|6[02-8]|9[2-59])|5(?:[2-589]\\d|6[1-9]|7[2-8])|7(?:[25-9]\\d|3[4-9]|4[02-9])|8(?:[2679]\\d|3[2-9]|4[5-9]|5[1-9]|8[03-9])|9(?:[2-58]\\d|[679][1-9]))\\d{6}",[9]],["[7-9]0[1-9]\\d{7}",[10]],["00777(?:[01]|5\\d)\\d\\d|(?:00(?:7778|882[1245])|(?:120|800\\d)\\d\\d)\\d{4}|00(?:37|66|78)\\d{6,13}"],["990\\d{6}",[9]],["60\\d{7}",[9]],0,["570\\d{6}",[9]],["20\\d{8}",[10]],["50[1-9]\\d{7}",[10]]]],KE:["254","000","(?:[17]\\d\\d|900)\\d{6}|(?:2|80)0\\d{6,7}|[4-6]\\d{6,8}",[7,8,9,10],[["(\\d{2})(\\d{5,7})","$1 $2",["[24-6]"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["[17]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[["(?:4[245]|5[1-79]|6[01457-9])\\d{5,7}|(?:4[136]|5[08]|62)\\d{7}|(?:[24]0|66)\\d{6,7}",[7,8,9]],["(?:1(?:0[0-8]|1[0-5]|2[014]|30)|7\\d\\d)\\d{6}",[9]],["800[02-8]\\d{5,6}",[9,10]],["900[02-9]\\d{5}",[9]]]],KG:["996","00","8\\d{9}|[235-9]\\d{8}",[9,10],[["(\\d{4})(\\d{5})","$1 $2",["3(?:1[346]|[24-79])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[235-79]|88"],"0$1"],["(\\d{3})(\\d{3})(\\d)(\\d{2,3})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[["312(?:5[0-79]\\d|9(?:[0-689]\\d|7[0-24-9]))\\d{3}|(?:3(?:1(?:2[0-46-8]|3[1-9]|47|[56]\\d)|2(?:22|3[0-479]|6[0-7])|4(?:22|5[6-9]|6\\d)|5(?:22|3[4-7]|59|6\\d)|6(?:22|5[35-7]|6\\d)|7(?:22|3[468]|4[1-9]|59|[67]\\d)|9(?:22|4[1-8]|6\\d))|6(?:09|12|2[2-4])\\d)\\d{5}",[9]],["312(?:58\\d|973)\\d{3}|(?:2(?:0[0-35]|2\\d)|5[0-24-7]\\d|600|7(?:[07]\\d|55)|88[08]|9(?:12|9[05-9]))\\d{6}",[9]],["800\\d{6,7}"]]],KH:["855","00[14-9]","1\\d{9}|[1-9]\\d{7,8}",[8,9,10],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-9]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[["23(?:4(?:[2-4]|[56]\\d)|[568]\\d\\d)\\d{4}|23[236-9]\\d{5}|(?:2[4-6]|3[2-6]|4[2-4]|[5-7][2-5])(?:(?:[237-9]|4[56]|5\\d)\\d{5}|6\\d{5,6})",[8,9]],["(?:(?:1[28]|3[18]|9[67])\\d|6[016-9]|7(?:[07-9]|[16]\\d)|8(?:[013-79]|8\\d))\\d{6}|(?:1\\d|9[0-57-9])\\d{6}|(?:2[3-6]|3[2-6]|4[2-4]|[5-7][2-5])48\\d{5}",[8,9]],["1800(?:1\\d|2[019])\\d{4}",[10]],["1900(?:1\\d|2[09])\\d{4}",[10]]]],KI:["686","00","(?:[37]\\d|6[0-79])\\d{6}|(?:[2-48]\\d|50)\\d{3}",[5,8],0,"0",0,0,0,0,0,[["(?:[24]\\d|3[1-9]|50|65(?:02[12]|12[56]|22[89]|[3-5]00)|7(?:27\\d\\d|3100|5(?:02[12]|12[56]|22[89]|[34](?:00|81)|500))|8[0-5])\\d{3}"],["(?:6200[01]|7(?:310[1-9]|5(?:02[03-9]|12[0-47-9]|22[0-7]|[34](?:0[1-9]|8[02-9])|50[1-9])))\\d{3}|(?:63\\d\\d|7(?:(?:[0146-9]\\d|2[0-689])\\d|3(?:[02-9]\\d|1[1-9])|5(?:[0-2][013-9]|[34][1-79]|5[1-9]|[6-9]\\d)))\\d{4}",[8]],0,0,0,0,0,0,["30(?:0[01]\\d\\d|12(?:11|20))\\d\\d",[8]]]],KM:["269","00","[3478]\\d{6}",[7],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[3478]"]]],0,0,0,0,0,0,[["7[4-7]\\d{5}"],["[34]\\d{6}"],0,["8\\d{6}"]]],KN:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","869$1",0,"869",[["869(?:2(?:29|36)|302|4(?:6[015-9]|70)|56[5-7])\\d{4}"],["869(?:48[89]|55[6-8]|66\\d|76[02-7])\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],KP:["850","00|99","85\\d{6}|(?:19\\d|[2-7])\\d{7}",[8,10],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2-7]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"]],"0",0,0,0,0,0,[["(?:(?:195|2)\\d|3[19]|4[159]|5[37]|6[17]|7[39]|85)\\d{6}"],["19[1-3]\\d{7}",[10]]]],KR:["82","00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))","00[1-9]\\d{8,11}|(?:[12]|5\\d{3})\\d{7}|[13-6]\\d{9}|(?:[1-6]\\d|80)\\d{7}|[3-6]\\d{4,5}|(?:00|7)0\\d{8}",[5,6,8,9,10,11,12,13,14],[["(\\d{2})(\\d{3,4})","$1-$2",["(?:3[1-3]|[46][1-4]|5[1-5])1"],"0$1"],["(\\d{4})(\\d{4})","$1-$2",["1"]],["(\\d)(\\d{3,4})(\\d{4})","$1-$2-$3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["60|8"],"0$1"],["(\\d{2})(\\d{3,4})(\\d{4})","$1-$2-$3",["[1346]|5[1-5]"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2-$3",["[57]"],"0$1"],["(\\d{2})(\\d{5})(\\d{4})","$1-$2-$3",["5"],"0$1"]],"0",0,"0(8(?:[1-46-8]|5\\d\\d))?",0,0,0,[["(?:2|3[1-3]|[46][1-4]|5[1-5])[1-9]\\d{6,7}|(?:3[1-3]|[46][1-4]|5[1-5])1\\d{2,3}",[5,6,8,9,10]],["1(?:05(?:[0-8]\\d|9[0-6])|22[13]\\d)\\d{4,5}|1(?:0[0-46-9]|[16-9]\\d|2[013-9])\\d{6,7}",[9,10]],["00(?:308\\d{6,7}|798\\d{7,9})|(?:00368|80)\\d{7}",[9,11,12,13,14]],["60[2-9]\\d{6}",[9]],["50\\d{8,9}",[10,11]],0,["1(?:5(?:22|33|44|66|77|88|99)|6(?:[07]0|44|6[0168]|88)|8(?:00|33|55|77|99))\\d{4}",[8]],["15\\d{7,8}",[9,10]],["70\\d{8}",[10]]]],KW:["965","00","18\\d{5}|(?:[2569]\\d|41)\\d{6}",[7,8],[["(\\d{4})(\\d{3,4})","$1 $2",["[169]|2(?:[235]|4[1-35-9])|52"]],["(\\d{3})(\\d{5})","$1 $2",["[245]"]]],0,0,0,0,0,0,[["2(?:[23]\\d\\d|4(?:[1-35-9]\\d|44)|5(?:0[034]|[2-46]\\d|5[1-3]|7[1-7]))\\d{4}",[8]],["(?:41\\d\\d|5(?:(?:[05]\\d|1[0-7]|6[56])\\d|2(?:22|5[25])|7(?:55|77)|88[58])|6(?:(?:0[034679]|5[015-9]|6\\d)\\d|1(?:00|11|6[16])|2[26]2|3[36]3|4[46]4|7(?:0[013-9]|[67]\\d)|8[68]8|9(?:[069]\\d|3[039]))|9(?:(?:[04679]\\d|8[057-9])\\d|1(?:1[01]|99)|2(?:00|2\\d)|3(?:00|3[03])|5(?:00|5\\d)))\\d{4}",[8]],["18\\d{5}",[7]]]],KY:["1","011","(?:345|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","345$1",0,"345",[["345(?:2(?:22|3[23]|44|66)|333|444|6(?:23|38|40)|7(?:30|4[35-79]|6[6-9]|77)|8(?:00|1[45]|[48]8)|9(?:14|4[035-9]))\\d{4}"],["345(?:32[1-9]|42[0-4]|5(?:1[67]|2[5-79]|4[6-9]|50|76)|649|82[56]|9(?:1[679]|2[2-9]|3[06-9]|90))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["(?:345976|900[2-9]\\d\\d)\\d{4}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,0,["345849\\d{4}"]]],KZ:["7","810","(?:33622|8\\d{8})\\d{5}|[78]\\d{9}",[10,14],0,"8",0,0,0,0,"33|7",[["(?:33622|7(?:1(?:0(?:[23]\\d|4[0-3]|59|63)|1(?:[23]\\d|4[0-79]|59)|2(?:[23]\\d|59)|3(?:2\\d|3[0-79]|4[0-35-9]|59)|4(?:[24]\\d|3[013-9]|5[1-9]|97)|5(?:2\\d|3[1-9]|4[0-7]|59)|6(?:[2-4]\\d|5[19]|61)|72\\d|8(?:[27]\\d|3[1-46-9]|4[0-5]|59))|2(?:1(?:[23]\\d|4[46-9]|5[3469])|2(?:2\\d|3[0679]|46|5[12679])|3(?:[2-4]\\d|5[139])|4(?:2\\d|3[1-35-9]|59)|5(?:[23]\\d|4[0-8]|59|61)|6(?:2\\d|3[1-9]|4[0-4]|59)|7(?:[2379]\\d|40|5[279])|8(?:[23]\\d|4[0-3]|59)|9(?:2\\d|3[124578]|59))))\\d{5}",[10]],["7(?:0[0-25-8]|47|6[0-4]|7[15-8]|85)\\d{7}",[10]],["8(?:00|108\\d{3})\\d{7}"],["809\\d{7}",[10]],["808\\d{7}",[10]],0,0,0,["751\\d{7}",[10]]],"8~10"],LA:["856","00","[23]\\d{9}|3\\d{8}|(?:[235-8]\\d|41)\\d{6}",[8,9,10],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["2[13]|3[14]|[4-8]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["30[013-9]"],"0$1"],["(\\d{2})(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[23]"],"0$1"]],"0",0,0,0,0,0,[["(?:2[13]|[35-7][14]|41|8[1468])\\d{6}",[8]],["(?:20(?:[23579]\\d|88)|30(?:2\\d|4))\\d{6}",[9,10]],0,0,0,0,["30[0135-9]\\d{6}",[9]]]],LB:["961","00","[27-9]\\d{7}|[13-9]\\d{6}",[7,8],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[13-69]|7(?:[2-57]|62|8[0-7]|9[04-9])|8[02-9]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[27-9]"]]],"0",0,0,0,0,0,[["7(?:62|8[0-7]|9[04-9])\\d{4}|(?:[14-69]\\d|2(?:[14-69]\\d|[78][1-9])|7[2-57]|8[02-9])\\d{5}"],["793(?:[01]\\d|2[0-4])\\d{3}|(?:(?:3|81)\\d|7(?:[01]\\d|6[013-9]|8[89]|9[12]))\\d{5}"],0,["9[01]\\d{6}",[8]],0,0,0,0,0,["80\\d{6}",[8]]]],LC:["1","011","(?:[58]\\d\\d|758|900)\\d{7}",[10],0,"1",0,"([2-8]\\d{6})$|1","758$1",0,"758",[["758(?:234|4(?:30|5\\d|6[2-9]|8[0-2])|57[0-2]|(?:63|75)8)\\d{4}"],["758(?:28[4-7]|384|4(?:6[01]|8[4-9])|5(?:1[89]|20|84)|7(?:1[2-9]|2\\d|3[0-3])|812)\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],LI:["423","00","[68]\\d{8}|(?:[2378]\\d|90)\\d{5}",[7,9],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[2379]|8(?:0[09]|7)","[2379]|8(?:0(?:02|9)|7)"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["69"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]]],"0",0,"(1001)|0",0,0,0,[["(?:2(?:01|1[27]|2[02]|3\\d|6[02-578]|96)|3(?:[24]0|33|7[0135-7]|8[048]|9[0269]))\\d{4}",[7]],["(?:6(?:(?:4[5-9]|5[0-469])\\d|6(?:[024-6]\\d|[17]0|3[7-9]))\\d|7(?:[37-9]\\d|42|56))\\d{4}"],["8002[28]\\d\\d|80(?:05\\d|9)\\d{4}"],["90(?:02[258]|1(?:23|3[14])|66[136])\\d\\d",[7]],0,0,["870(?:28|87)\\d\\d",[7]]]],LK:["94","00","[1-9]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[1-689]"],"0$1"]],"0",0,0,0,0,0,[["(?:12[2-9]|602|8[12]\\d|9(?:1\\d|22|9[245]))\\d{6}|(?:11|2[13-7]|3[1-8]|4[157]|5[12457]|6[35-7])[2-57]\\d{6}"],["7(?:[0-25-8]\\d|4[0-4])\\d{6}"],0,0,0,0,["1973\\d{5}"]]],LR:["231","00","(?:[245]\\d|33|77|88)\\d{7}|(?:2\\d|[4-6])\\d{6}",[7,8,9],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["4[67]|[56]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[2-578]"],"0$1"]],"0",0,0,0,0,0,[["2\\d{7}",[8]],["(?:(?:(?:22|33)0|555|(?:77|88)\\d)\\d|4(?:240|[67]))\\d{5}|[56]\\d{6}",[7,9]],0,["332(?:02|[34]\\d)\\d{4}",[9]]]],LS:["266","00","(?:[256]\\d\\d|800)\\d{5}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[2568]"]]],0,0,0,0,0,0,[["2\\d{7}"],["[56]\\d{7}"],["800[1256]\\d{4}"]]],LT:["370","00","(?:[3469]\\d|52|[78]0)\\d{6}",[8],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["52[0-7]"],"(0-$1)",1],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[7-9]"],"0 $1",1],["(\\d{2})(\\d{6})","$1 $2",["37|4(?:[15]|6[1-8])"],"(0-$1)",1],["(\\d{3})(\\d{5})","$1 $2",["[3-6]"],"(0-$1)",1]],"0",0,"[08]",0,0,0,[["(?:3[1478]|4[124-6]|52)\\d{6}"],["6\\d{7}"],["80[02]\\d{5}"],["9(?:0[0239]|10)\\d{5}"],["70[05]\\d{5}"],0,["70[67]\\d{5}"],0,["[89]01\\d{5}"],["808\\d{5}"]]],LU:["352","00","35[013-9]\\d{4,8}|6\\d{8}|35\\d{2,4}|(?:[2457-9]\\d|3[0-46-9])\\d{2,9}",[4,5,6,7,8,9,10,11],[["(\\d{2})(\\d{3})","$1 $2",["2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])"]],["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])"]],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["20[2-689]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{1,2})","$1 $2 $3 $4",["2(?:[0367]|4[3-8])"]],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["80[01]|90[015]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["20"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{1,2})","$1 $2 $3 $4 $5",["2(?:[0367]|4[3-8])"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{1,5})","$1 $2 $3 $4",["[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]"]]],0,0,"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\d)",0,0,0,[["(?:35[013-9]|80[2-9]|90[89])\\d{1,8}|(?:2[2-9]|3[0-46-9]|[457]\\d|8[13-9]|9[2-579])\\d{2,9}"],["6(?:[269][18]|5[1568]|7[189]|81)\\d{6}",[9]],["800\\d{5}",[8]],["90[015]\\d{5}",[8]],0,0,0,0,["20(?:1\\d{5}|[2-689]\\d{1,7})",[4,5,6,7,8,9,10]],["801\\d{5}",[8]]]],LV:["371","00","(?:[268]\\d|90)\\d{6}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[269]|8[01]"]]],0,0,0,0,0,0,[["6\\d{7}"],["23(?:23[0-57-9]|33[0238])\\d{3}|2(?:[0-24-9]\\d\\d|3(?:0[07]|[14-9]\\d|2[024-9]|3[0-24-9]))\\d{4}"],["80\\d{6}"],["90\\d{6}"],0,0,0,0,0,["81\\d{6}"]]],LY:["218","00","[2-9]\\d{8}",[9],[["(\\d{2})(\\d{7})","$1-$2",["[2-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:2(?:0[56]|[1-6]\\d|7[124579]|8[124])|3(?:1\\d|2[2356])|4(?:[17]\\d|2[1-357]|5[2-4]|8[124])|5(?:[1347]\\d|2[1-469]|5[13-5]|8[1-4])|6(?:[1-479]\\d|5[2-57]|8[1-5])|7(?:[13]\\d|2[13-79])|8(?:[124]\\d|5[124]|84))\\d{6}"],["9[1-6]\\d{7}"]]],MA:["212","00","[5-8]\\d{8}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["5[45]"],"0$1"],["(\\d{4})(\\d{5})","$1-$2",["5(?:2[2-46-9]|3[3-9]|9)|8(?:0[89]|92)"],"0$1"],["(\\d{2})(\\d{7})","$1-$2",["8"],"0$1"],["(\\d{3})(\\d{6})","$1-$2",["[5-7]"],"0$1"]],"0",0,0,0,0,0,[["5(?:2(?:[0-25-79]\\d|3[1-578]|4[02-46-8]|8[0235-7])|3(?:[0-47]\\d|5[02-9]|6[02-8]|8[014-9]|9[3-9])|(?:4[067]|5[03])\\d)\\d{5}"],["(?:6(?:[0-79]\\d|8[0-247-9])|7(?:[0167]\\d|2[0-4]|5[01]|8[0-3]))\\d{6}"],["80[0-7]\\d{6}"],["89\\d{7}"],0,0,0,0,["(?:592(?:4[0-2]|93)|80[89]\\d\\d)\\d{4}"]]],MC:["377","00","(?:[3489]|6\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["4"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[389]"]],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["6"],"0$1"]],"0",0,0,0,0,0,[["(?:870|9[2-47-9]\\d)\\d{5}",[8]],["4(?:[469]\\d|5[1-9])\\d{5}|(?:3|6\\d)\\d{7}"],["(?:800|90\\d)\\d{5}",[8]]]],MD:["373","00","(?:[235-7]\\d|[89]0)\\d{6}",[8],[["(\\d{3})(\\d{5})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["22|3"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[25-7]"],"0$1"]],"0",0,0,0,0,0,[["(?:(?:2[1-9]|3[1-79])\\d|5(?:33|5[257]))\\d{5}"],["562\\d{5}|(?:6\\d|7[16-9])\\d{6}"],["800\\d{5}"],["90[056]\\d{5}"],0,0,["803\\d{5}"],0,["3[08]\\d{6}"],["808\\d{5}"]]],ME:["382","00","(?:20|[3-79]\\d)\\d{6}|80\\d{6,7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:20[2-8]|3(?:[0-2][2-7]|3[24-7])|4(?:0[2-467]|1[2467])|5(?:0[2467]|1[24-7]|2[2-467]))\\d{5}",[8]],["6(?:[07-9]\\d|3[024]|6[0-25])\\d{5}",[8]],["80(?:[0-2578]|9\\d)\\d{5}"],["9(?:4[1568]|5[178])\\d{5}",[8]],0,0,["77[1-9]\\d{5}",[8]],0,["78[1-49]\\d{5}",[8]]]],MF:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],0,"0",0,0,0,0,0,[["590(?:0[079]|[14]3|[27][79]|3[03-7]|5[0-268]|87)\\d{4}"],["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"],["80[0-5]\\d{6}"],0,0,0,0,0,["9(?:(?:39[5-7]|76[018])\\d|475[0-5])\\d{4}"]]],MG:["261","00","[23]\\d{8}",[9],[["(\\d{2})(\\d{2})(\\d{3})(\\d{2})","$1 $2 $3 $4",["[23]"],"0$1"]],"0",0,"([24-9]\\d{6})$|0","20$1",0,0,[["2072[29]\\d{4}|20(?:2\\d|4[47]|5[3467]|6[279]|7[35]|8[268]|9[245])\\d{5}"],["3[2-47-9]\\d{7}"],0,0,0,0,0,0,["22\\d{7}"]]],MH:["692","011","329\\d{4}|(?:[256]\\d|45)\\d{5}",[7],[["(\\d{3})(\\d{4})","$1-$2",["[2-6]"]]],"1",0,0,0,0,0,[["(?:247|528|625)\\d{4}"],["(?:(?:23|54)5|329|45[35-8])\\d{4}"],0,0,0,0,0,0,["635\\d{4}"]]],MK:["389","00","[2-578]\\d{7}",[8],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["2|34[47]|4(?:[37]7|5[47]|64)"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[347]"],"0$1"],["(\\d{3})(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["[58]"],"0$1"]],"0",0,0,0,0,0,[["(?:(?:2(?:62|77)0|3444)\\d|4[56]440)\\d{3}|(?:34|4[357])700\\d{3}|(?:2(?:[0-3]\\d|5[0-578]|6[01]|82)|3(?:1[3-68]|[23][2-68]|4[23568])|4(?:[23][2-68]|4[3-68]|5[2568]|6[25-8]|7[24-68]|8[4-68]))\\d{5}"],["7(?:3555|(?:474|9[019]7)7)\\d{3}|7(?:[0-25-8]\\d\\d|3(?:[1-48]\\d|6[01]|7[01578])|4(?:2\\d|60|7[01578])|9(?:[2-4]\\d|5[01]|7[015]))\\d{4}"],["800\\d{5}"],["5\\d{7}"],0,0,0,0,0,["8(?:0[1-9]|[1-9]\\d)\\d{5}"]]],ML:["223","00","[24-9]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24-9]"]]],0,0,0,0,0,0,[["2(?:07[0-8]|12[67])\\d{4}|(?:2(?:02|1[4-689])|4(?:0[0-4]|4[1-39]))\\d{5}"],["2(?:0(?:01|79)|17\\d)\\d{4}|(?:5[01]|[679]\\d|8[2-49])\\d{6}"],["80\\d{6}"]]],MM:["95","00","1\\d{5,7}|95\\d{6}|(?:[4-7]|9[0-46-9])\\d{6,8}|(?:2|8\\d)\\d{5,8}",[6,7,8,9,10],[["(\\d)(\\d{2})(\\d{3})","$1 $2 $3",["16|2"],"0$1"],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["[45]|6(?:0[23]|[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-6]"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[12]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[4-7]|8[1-35]"],"0$1"],["(\\d)(\\d{3})(\\d{4,6})","$1 $2 $3",["9(?:2[0-4]|[35-9]|4[137-9])"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["92"],"0$1"],["(\\d)(\\d{5})(\\d{4})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[["(?:1(?:(?:2\\d|3[56]|[89][0-6])\\d|4(?:2[29]|62|7[0-2]|83)|6)|2(?:2(?:00|8[34])|4(?:0\\d|[26]2|7[0-2]|83)|51\\d\\d)|4(?:2(?:2\\d\\d|48[013])|3(?:20\\d|4(?:70|83)|56)|420\\d|5470)|6(?:0(?:[23]|88\\d)|(?:124|[56]2\\d)\\d|2472|3(?:20\\d|470)|4(?:2[04]\\d|472)|7(?:(?:3\\d|8[01459])\\d|4[67]0)))\\d{4}|5(?:2(?:2\\d{5,6}|47[02]\\d{4})|(?:3472|4(?:2(?:1|86)|470)|522\\d|6(?:20\\d|483)|7(?:20\\d|48[01])|8(?:20\\d|47[02])|9(?:20\\d|470))\\d{4})|7(?:(?:0470|4(?:25\\d|470)|5(?:202|470|96\\d))\\d{4}|1(?:20\\d{4,5}|4(?:70|83)\\d{4}))|8(?:1(?:2\\d{5,6}|4(?:10|7[01]\\d)\\d{3})|2(?:2\\d{5,6}|(?:320|490\\d)\\d{3})|(?:3(?:2\\d\\d|470)|4[24-7]|5(?:(?:2\\d|51)\\d|4(?:[1-35-9]\\d|4[0-57-9]))|6[23])\\d{4})|(?:1[2-6]\\d|4(?:2[24-8]|3[2-7]|[46][2-6]|5[3-5])|5(?:[27][2-8]|3[2-68]|4[24-8]|5[23]|6[2-4]|8[24-7]|9[2-7])|6(?:[19]20|42[03-6]|(?:52|7[45])\\d)|7(?:[04][24-8]|[15][2-7]|22|3[2-4])|8(?:1[2-689]|2[2-8]|[35]2\\d))\\d{4}|25\\d{5,6}|(?:2[2-9]|6(?:1[2356]|[24][2-6]|3[24-6]|5[2-4]|6[2-8]|7[235-7]|8[245]|9[24])|8(?:3[24]|5[245]))\\d{4}",[6,7,8,9]],["(?:17[01]|9(?:2(?:[0-4]|[56]\\d\\d)|(?:3(?:[0-36]|4\\d)|(?:6\\d|8[89]|9[4-8])\\d|7(?:3|40|[5-9]\\d))\\d|4(?:(?:[0245]\\d|[1379])\\d|88)|5[0-6])\\d)\\d{4}|9[69]1\\d{6}|9(?:[68]\\d|9[089])\\d{5}",[7,8,9,10]],["80080(?:0[1-9]|2\\d)\\d{3}",[10]],0,0,0,0,0,["1333\\d{4}|[12]468\\d{4}",[8]]]],MN:["976","001","[12]\\d{7,9}|[5-9]\\d{7}",[8,9,10],[["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["[12]1"],"0$1"],["(\\d{4})(\\d{4})","$1 $2",["[5-9]"]],["(\\d{3})(\\d{5,6})","$1 $2",["[12]2[1-3]"],"0$1"],["(\\d{4})(\\d{5,6})","$1 $2",["[12](?:27|3[2-8]|4[2-68]|5[1-4689])","[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]"],"0$1"],["(\\d{5})(\\d{4,5})","$1 $2",["[12]"],"0$1"]],"0",0,0,0,0,0,[["[12]2[1-3]\\d{5,6}|(?:(?:[12](?:1|27)|5[368])\\d\\d|7(?:0(?:[0-5]\\d|7[078]|80)|128))\\d{4}|[12](?:3[2-8]|4[2-68]|5[1-4689])\\d{6,7}"],["(?:83[01]|92[039])\\d{5}|(?:5[05]|6[069]|8[015689]|9[013-9])\\d{6}",[8]],0,0,0,0,0,0,["712[0-79]\\d{4}|7(?:1[013-9]|[25-9]\\d)\\d{5}",[8]]]],MO:["853","00","0800\\d{3}|(?:28|[68]\\d)\\d{6}",[7,8],[["(\\d{4})(\\d{3})","$1 $2",["0"]],["(\\d{4})(\\d{4})","$1 $2",["[268]"]]],0,0,0,0,0,0,[["(?:28[2-9]|8(?:11|[2-57-9]\\d))\\d{5}",[8]],["6800[0-79]\\d{3}|6(?:[235]\\d\\d|6(?:0[0-5]|[1-9]\\d)|8(?:0[1-9]|[14-8]\\d|2[5-9]|[39][0-4]))\\d{4}",[8]],["0800\\d{3}",[7]]]],MP:["1","011","[58]\\d{9}|(?:67|90)0\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","670$1",0,"670",[["670(?:2(?:3[3-7]|56|8[4-8])|32[1-38]|4(?:33|8[348])|5(?:32|55|88)|6(?:64|70|82)|78[3589]|8[3-9]8|989)\\d{4}"],[""],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],MQ:["596","00","596\\d{6}|(?:69|80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[569]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[["596(?:[03-7]\\d|1[05]|2[7-9]|8[0-39]|9[04-9])\\d{4}"],["69(?:6(?:[0-46-9]\\d|5[0-6])|727)\\d{4}"],["80[0-5]\\d{6}"],0,0,0,0,0,["9(?:397[0-3]|477[0-5]|76(?:6\\d|7[0-367]))\\d{4}"]]],MR:["222","00","(?:[2-4]\\d\\d|800)\\d{5}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-48]"]]],0,0,0,0,0,0,[["(?:25[08]|35\\d|45[1-7])\\d{5}"],["[2-4][0-46-9]\\d{6}"],["800\\d{5}"]]],MS:["1","011","(?:[58]\\d\\d|664|900)\\d{7}",[10],0,"1",0,"([34]\\d{6})$|1","664$1",0,"664",[["6644(?:1[0-3]|91)\\d{4}"],["664(?:3(?:49|9[1-6])|49[2-6])\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],MT:["356","00","3550\\d{4}|(?:[2579]\\d\\d|800)\\d{5}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[2357-9]"]]],0,0,0,0,0,0,[["20(?:3[1-4]|6[059])\\d{4}|2(?:0[19]|[1-357]\\d|60)\\d{5}"],["(?:7(?:210|[79]\\d\\d)|9(?:[29]\\d\\d|69[67]|8(?:1[1-3]|89|97)))\\d{4}"],["800(?:02|[3467]\\d)\\d{3}"],["5(?:0(?:0(?:37|43)|(?:6\\d|70|9[0168])\\d)|[12]\\d0[1-5])\\d{3}"],0,0,["501\\d{5}"],["7117\\d{4}"],["3550\\d{4}"]]],MU:["230","0(?:0|[24-7]0|3[03])","(?:[57]|8\\d\\d)\\d{7}|[2-468]\\d{6}",[7,8,10],[["(\\d{3})(\\d{4})","$1 $2",["[2-46]|8[013]"]],["(\\d{4})(\\d{4})","$1 $2",["[57]"]],["(\\d{5})(\\d{5})","$1 $2",["8"]]],0,0,0,0,0,0,[["(?:2(?:[0346-8]\\d|1[0-7])|4(?:[013568]\\d|2[4-8]|71)|54(?:[3-5]\\d|71)|6\\d\\d|8(?:14|3[129]))\\d{4}",[7,8]],["5(?:4(?:2[1-389]|7[1-9])|87[15-8])\\d{4}|(?:5(?:2[5-9]|4[3-689]|[57]\\d|8[0-689]|9[0-8])|7(?:0[0-3]|3[013]))\\d{5}",[8]],["802\\d{7}|80[0-2]\\d{4}",[7,10]],["30\\d{5}",[7]],0,0,0,0,["3(?:20|9\\d)\\d{4}",[7]]],"020"],MV:["960","0(?:0|19)","(?:800|9[0-57-9]\\d)\\d{7}|[34679]\\d{6}",[7,10],[["(\\d{3})(\\d{4})","$1-$2",["[34679]"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"]]],0,0,0,0,0,0,[["(?:3(?:0[0-3]|3[0-59])|6(?:[58][024689]|6[024-68]|7[02468]))\\d{4}",[7]],["(?:46[46]|[79]\\d\\d)\\d{4}",[7]],["800\\d{7}",[10]],["900\\d{7}",[10]],0,0,["4(?:0[01]|50)\\d{4}",[7]]],"00"],MW:["265","00","(?:[1289]\\d|31|77)\\d{7}|1\\d{6}",[7,9],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["1[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[137-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:1[2-9]|2[12]\\d\\d)\\d{5}"],["111\\d{6}|(?:31|77|[89][89])\\d{7}",[9]]]],MX:["52","0[09]","1(?:(?:22|44|7[27]|87|9[69])[1-9]|65[0-689])\\d{7}|(?:1(?:[01]\\d|2[13-9]|[35][1-9]|4[0-35-9]|6[0-46-9]|7[013-689]|8[1-69]|9[1-578])|[2-9]\\d)\\d{8}",[10,11],[["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["33|5[56]|81"],0,1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[2-9]"],0,1],["(\\d)(\\d{2})(\\d{4})(\\d{4})","$2 $3 $4",["1(?:33|5[56]|81)"],0,1],["(\\d)(\\d{3})(\\d{3})(\\d{4})","$2 $3 $4",["1"],0,1]],"01",0,"0(?:[12]|4[45])|1",0,0,0,[["657[12]\\d{6}|(?:2(?:0[01]|2\\d|3[1-35-8]|4[13-9]|7[1-689]|8[1-578]|9[467])|3(?:1[1-79]|[2458][1-9]|3\\d|7[1-8]|9[1-5])|4(?:1[1-57-9]|[25-7][1-9]|3[1-8]|4\\d|8[1-35-9]|9[2-689])|5(?:[56]\\d|88|9[1-79])|6(?:1[2-68]|[2-4][1-9]|5[1-3689]|6[1-57-9]|7[1-7]|8[67]|9[4-8])|7(?:[1346][1-9]|[27]\\d|5[13-9]|8[1-69]|9[17])|8(?:1\\d|2[13-689]|3[1-6]|4[124-6]|6[1246-9]|7[0-378]|9[12479])|9(?:1[346-9]|2[1-4]|3[2-46-8]|5[1348]|[69]\\d|7[12]|8[1-8]))\\d{7}",[10]],["657[12]\\d{6}|(?:1(?:2(?:2[1-9]|3[1-35-8]|4[13-9]|7[1-689]|8[1-578]|9[467])|3(?:1[1-79]|[2458][1-9]|3\\d|7[1-8]|9[1-5])|4(?:1[1-57-9]|[24-7][1-9]|3[1-8]|8[1-35-9]|9[2-689])|5(?:[56]\\d|88|9[1-79])|6(?:1[2-68]|[2-4][1-9]|5[1-3689]|6[1-57-9]|7[1-7]|8[67]|9[4-8])|7(?:[1-467][1-9]|5[13-9]|8[1-69]|9[17])|8(?:1\\d|2[13-689]|3[1-6]|4[124-6]|6[1246-9]|7[1-378]|9[12479])|9(?:1[346-9]|2[1-4]|3[2-46-8]|5[1348]|[69][1-9]|7[12]|8[1-8]))|2(?:2\\d|3[1-35-8]|4[13-9]|7[1-689]|8[1-578]|9[467])|3(?:1[1-79]|[2458][1-9]|3\\d|7[1-8]|9[1-5])|4(?:1[1-57-9]|[25-7][1-9]|3[1-8]|4\\d|8[1-35-9]|9[2-689])|5(?:[56]\\d|88|9[1-79])|6(?:1[2-68]|[2-4][1-9]|5[1-3689]|6[1-57-9]|7[1-7]|8[67]|9[4-8])|7(?:[1346][1-9]|[27]\\d|5[13-9]|8[1-69]|9[17])|8(?:1\\d|2[13-689]|3[1-6]|4[124-6]|6[1246-9]|7[0-378]|9[12479])|9(?:1[346-9]|2[1-4]|3[2-46-8]|5[1348]|[69]\\d|7[12]|8[1-8]))\\d{7}"],["8(?:00|88)\\d{7}",[10]],["900\\d{7}",[10]],["500\\d{7}",[10]],0,0,0,0,["300\\d{7}",[10]]],"00"],MY:["60","00","1\\d{8,9}|(?:3\\d|[4-9])\\d{7}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1-$2 $3",["[4-79]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1-$2 $3",["1(?:[02469]|[378][1-9]|53)|8","1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1-$2 $3",["3"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{4})","$1-$2-$3-$4",["1(?:[367]|80)"]],["(\\d{3})(\\d{3})(\\d{4})","$1-$2 $3",["15"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2 $3",["1"],"0$1"]],"0",0,0,0,0,0,[["(?:3(?:2[0-36-9]|3[0-368]|4[0-278]|5[0-24-8]|6[0-467]|7[1246-9]|8\\d|9[0-57])\\d|4(?:2[0-689]|[3-79]\\d|8[1-35689])|5(?:2[0-589]|[3468]\\d|5[0-489]|7[1-9]|9[23])|6(?:2[2-9]|3[1357-9]|[46]\\d|5[0-6]|7[0-35-9]|85|9[015-8])|7(?:[2579]\\d|3[03-68]|4[0-8]|6[5-9]|8[0-35-9])|8(?:[24][2-8]|3[2-5]|5[2-7]|6[2-589]|7[2-578]|[89][2-9])|9(?:0[57]|13|[25-7]\\d|[3489][0-8]))\\d{5}",[8,9]],["1(?:1888[689]|4400|8(?:47|8[27])[0-4])\\d{4}|1(?:0(?:[23568]\\d|4[0-6]|7[016-9]|9[0-8])|1(?:[1-5]\\d\\d|6(?:0[5-9]|[1-9]\\d)|7(?:[0-4]\\d|5[0-7]))|(?:[269]\\d|[37][1-9]|4[235-9])\\d|5(?:31|9\\d\\d)|8(?:1[23]|[236]\\d|4[06]|5(?:46|[7-9])|7[016-9]|8[01]|9[0-8]))\\d{5}",[9,10]],["1[378]00\\d{6}",[10]],["1600\\d{6}",[10]],0,0,0,0,["15(?:4(?:6[0-4]\\d|8(?:0[125]|[17]\\d|21|3[01]|4[01589]|5[014]|6[02]))|6(?:32[0-6]|78\\d))\\d{4}",[10]]]],MZ:["258","00","(?:2|8\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["2|8[2-79]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["2(?:[1346]\\d|5[0-2]|[78][12]|93)\\d{5}",[8]],["8[2-79]\\d{7}",[9]],["800\\d{6}",[9]]]],NA:["264","00","[68]\\d{7,8}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["88"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["6"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["87"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[["64426\\d{3}|6(?:1(?:2[2-7]|3[01378]|4[0-4])|254|32[0237]|4(?:27|41|5[25])|52[236-8]|626|7(?:2[2-4]|30))\\d{4,5}|6(?:1(?:(?:0\\d|2[0189]|3[24-69]|4[5-9])\\d|17|69|7[014])|2(?:17|5[0-36-8]|69|70)|3(?:17|2[14-689]|34|6[289]|7[01]|81)|4(?:17|2[0-2]|4[06]|5[0137]|69|7[01])|5(?:17|2[0459]|69|7[01])|6(?:17|25|38|42|69|7[01])|7(?:17|2[569]|3[13]|6[89]|7[01]))\\d{4}"],["(?:60|8[1245])\\d{7}",[9]],["80\\d{7}",[9]],["8701\\d{5}",[9]],0,0,0,0,["8(?:3\\d\\d|86)\\d{5}"]]],NC:["687","00","(?:050|[2-57-9]\\d\\d)\\d{3}",[6],[["(\\d{2})(\\d{2})(\\d{2})","$1.$2.$3",["[02-57-9]"]]],0,0,0,0,0,0,[["(?:2[03-9]|3[0-5]|4[1-7]|88)\\d{4}"],["(?:5[0-4]|[79]\\d|8[0-79])\\d{4}"],["050\\d{3}"],["36\\d{4}"]]],NE:["227","00","[027-9]\\d{7}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["08"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[089]|2[013]|7[047]"]]],0,0,0,0,0,0,[["2(?:0(?:20|3[1-8]|4[13-5]|5[14]|6[14578]|7[1-578])|1(?:4[145]|5[14]|6[14-68]|7[169]|88))\\d{4}"],["(?:23|7[047]|[89]\\d)\\d{6}"],["08\\d{6}"],["09\\d{6}"]]],NF:["672","00","[13]\\d{5}",[6],[["(\\d{2})(\\d{4})","$1 $2",["1[0-3]"]],["(\\d)(\\d{5})","$1 $2",["[13]"]]],0,0,"([0-258]\\d{4})$","3$1",0,0,[["(?:1(?:06|17|28|39)|3[0-2]\\d)\\d{3}"],["(?:14|3[58])\\d{4}"]]],NG:["234","009","2[0-24-9]\\d{8}|[78]\\d{10,13}|[7-9]\\d{9}|[1-9]\\d{7}|[124-7]\\d{6}",[7,8,10,11,12,13,14],[["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["78"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[12]|9(?:0[3-9]|[1-9])"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["[3-6]|7(?:0[0-689]|[1-79])|8[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[7-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["20[129]"],"0$1"],["(\\d{4})(\\d{2})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{4})(\\d{4,5})","$1 $2 $3",["[78]"],"0$1"],["(\\d{3})(\\d{5})(\\d{5,6})","$1 $2 $3",["[78]"],"0$1"]],"0",0,0,0,0,0,[["20(?:[1259]\\d|3[013-9]|4[1-8]|6[024-689]|7[1-79]|8[2-9])\\d{6}|(?:(?:[1-356]\\d|4[02-8]|8[2-9])\\d|9(?:0[3-9]|[1-9]\\d))\\d{5}|7(?:0(?:[013-689]\\d|2[0-24-9])\\d{3,4}|[1-79]\\d{6})|(?:[12]\\d|4[147]|5[14579]|6[1578]|7[1-3578])\\d{5}",[7,8,10]],["(?:702[0-24-9]|819[01])\\d{6}|(?:7(?:0[13-9]|[12]\\d)|8(?:0[1-9]|1[0-8])|9(?:0[1-9]|1[1-6]))\\d{7}",[10]],["800\\d{7,11}",[10,11,12,13,14]],0,0,0,["700\\d{7,11}",[10,11,12,13,14]]]],NI:["505","00","(?:1800|[25-8]\\d{3})\\d{4}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[125-8]"]]],0,0,0,0,0,0,[["2\\d{7}"],["(?:5(?:5[0-7]|[78]\\d)|6(?:20|3[035]|4[045]|5[05]|77|8[1-9]|9[059])|(?:7[5-8]|8\\d)\\d)\\d{5}"],["1800\\d{4}"]]],NL:["31","00","(?:[124-7]\\d\\d|3(?:[02-9]\\d|1[0-8]))\\d{6}|8\\d{6,9}|9\\d{6,10}|1\\d{4,5}",[5,6,7,8,9,10,11],[["(\\d{3})(\\d{4,7})","$1 $2",["[89]0"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["66"],"0$1"],["(\\d)(\\d{8})","$1 $2",["6"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["1[16-8]|2[259]|3[124]|4[17-9]|5[124679]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-578]|91"],"0$1"],["(\\d{3})(\\d{3})(\\d{5})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[["(?:1(?:[035]\\d|1[13-578]|6[124-8]|7[24]|8[0-467])|2(?:[0346]\\d|2[2-46-9]|5[125]|9[479])|3(?:[03568]\\d|1[3-8]|2[01]|4[1-8])|4(?:[0356]\\d|1[1-368]|7[58]|8[15-8]|9[23579])|5(?:[0358]\\d|[19][1-9]|2[1-57-9]|4[13-8]|6[126]|7[0-3578])|7\\d\\d)\\d{6}",[9]],["(?:6[1-58]|970\\d)\\d{7}",[9,11]],["800\\d{4,7}",[7,8,9,10]],["90[069]\\d{4,7}",[7,8,9,10]],0,0,["140(?:1[035]|2[0346]|3[03568]|4[0356]|5[0358]|8[458])|(?:140(?:1[16-8]|2[259]|3[124]|4[17-9]|5[124679]|7)|8[478]\\d{6})\\d",[5,6,9]],["66\\d{7}",[9]],["(?:85|91)\\d{7}",[9]]]],NO:["47","00","(?:0|[2-9]\\d{3})\\d{4}",[5,8],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["8"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-79]"]]],0,0,0,0,0,"[02-689]|7[0-8]",[["(?:2[1-4]|3[1-3578]|5[1-35-7]|6[1-4679]|7[0-8])\\d{6}",[8]],["(?:4[015-8]|9\\d)\\d{6}",[8]],["80[01]\\d{5}",[8]],["82[09]\\d{5}",[8]],["880\\d{5}",[8]],0,["(?:0[2-9]|81(?:0(?:0[7-9]|1\\d)|5\\d\\d))\\d{3}"],0,["85[0-5]\\d{5}",[8]],["810(?:0[0-6]|[2-8]\\d)\\d{3}",[8]]]],NP:["977","00","(?:1\\d|9)\\d{9}|[1-9]\\d{7}",[8,10,11],[["(\\d)(\\d{7})","$1-$2",["1[2-6]"],"0$1"],["(\\d{2})(\\d{6})","$1-$2",["1[01]|[2-8]|9(?:[1-59]|[67][2-6])"],"0$1"],["(\\d{3})(\\d{7})","$1-$2",["9"]]],"0",0,0,0,0,0,[["(?:1[0-6]\\d|99[02-6])\\d{5}|(?:2[13-79]|3[135-8]|4[146-9]|5[135-7]|6[13-9]|7[15-9]|8[1-46-9]|9[1-7])[2-6]\\d{5}",[8]],["9(?:00|6[0-3]|7[024-6]|8[0-24-68])\\d{7}",[10]],["1(?:66001|800\\d\\d)\\d{5}",[11]]]],NR:["674","00","(?:444|(?:55|8\\d)\\d|666)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[4-68]"]]],0,0,0,0,0,0,[["444\\d{4}"],["(?:55[3-9]|666|8\\d\\d)\\d{4}"]]],NU:["683","00","(?:[4-7]|888\\d)\\d{3}",[4,7],[["(\\d{3})(\\d{4})","$1 $2",["8"]]],0,0,0,0,0,0,[["[47]\\d{3}",[4]],["(?:[56]|888[1-9])\\d{3}"]]],NZ:["64","0(?:0|161)","[1289]\\d{9}|50\\d{5}(?:\\d{2,3})?|[27-9]\\d{7,8}|(?:[34]\\d|6[0-35-9])\\d{6}|8\\d{4,6}",[5,6,7,8,9,10],[["(\\d{2})(\\d{3,8})","$1 $2",["8[1-79]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["50[036-8]|8|90","50(?:[0367]|88)|8|90"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["24|[346]|7[2-57-9]|9[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["2(?:10|74)|[589]"],"0$1"],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["1|2[028]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,5})","$1 $2 $3",["2(?:[169]|7[0-35-9])|7"],"0$1"]],"0",0,0,0,0,0,[["24099\\d{3}|(?:3[2-79]|[49][2-9]|6[235-9]|7[2-57-9])\\d{6}",[8]],["2(?:[0-27-9]\\d|6)\\d{6,7}|2(?:1\\d|75)\\d{5}",[8,9,10]],["508\\d{6,7}|80\\d{6,8}",[8,9,10]],["(?:1[13-57-9]\\d{5}|50(?:0[08]|30|66|77|88))\\d{3}|90\\d{6,8}",[7,8,9,10]],["70\\d{7}",[9]],0,["8(?:1[16-9]|22|3\\d|4[045]|5[459]|6[235-9]|7[0-3579]|90)\\d{2,7}"]],"00"],OM:["968","00","(?:1505|[279]\\d{3}|500)\\d{4}|800\\d{5,6}",[7,8,9],[["(\\d{3})(\\d{4,6})","$1 $2",["[58]"]],["(\\d{2})(\\d{6})","$1 $2",["2"]],["(\\d{4})(\\d{4})","$1 $2",["[179]"]]],0,0,0,0,0,0,[["2[1-6]\\d{6}",[8]],["(?:1505|90[1-9]\\d)\\d{4}|(?:7[126-9]|9[1-9])\\d{6}",[8]],["8007\\d{4,5}|(?:500|800[05])\\d{4}"],["900\\d{5}",[8]]]],PA:["507","00","(?:00800|8\\d{3})\\d{6}|[68]\\d{7}|[1-57-9]\\d{6}",[7,8,10,11],[["(\\d{3})(\\d{4})","$1-$2",["[1-57-9]"]],["(\\d{4})(\\d{4})","$1-$2",["[68]"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["(?:1(?:0\\d|1[479]|2[37]|3[0137]|4[17]|5[05]|6[058]|7[0167]|8[2358]|9[1389])|2(?:[0235-79]\\d|1[0-7]|4[013-9]|8[02-9])|3(?:[089]\\d|1[0-7]|2[0-5]|33|4[0-79]|5[0-35]|6[068]|7[0-8])|4(?:00|3[0-579]|4\\d|7[0-57-9])|5(?:[01]\\d|2[0-7]|[56]0|79)|7(?:0[09]|2[0-26-8]|3[03]|4[04]|5[05-9]|6[0156]|7[0-24-9]|8[5-9]|90)|8(?:09|2[89]|3\\d|4[0-24-689]|5[014]|8[02])|9(?:0[5-9]|1[0135-8]|2[036-9]|3[35-79]|40|5[0457-9]|6[05-9]|7[04-9]|8[35-8]|9\\d))\\d{4}",[7]],["(?:1[16]1|21[89]|6\\d{3}|8(?:1[01]|7[23]))\\d{4}",[7,8]],["800\\d{4,5}|(?:00800|800\\d)\\d{6}"],["(?:8(?:22|55|60|7[78]|86)|9(?:00|81))\\d{4}",[7]]]],PE:["51","00|19(?:1[124]|77|90)00","(?:[14-8]|9\\d)\\d{7}",[8,9],[["(\\d{3})(\\d{5})","$1 $2",["80"],"(0$1)"],["(\\d)(\\d{7})","$1 $2",["1"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["[4-8]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"]]],"0",0,0,0,0,0,[["(?:(?:4[34]|5[14])[0-8]\\d|7(?:173|3[0-8]\\d)|8(?:10[05689]|6(?:0[06-9]|1[6-9]|29)|7(?:0[569]|[56]0)))\\d{4}|(?:1[0-8]|4[12]|5[236]|6[1-7]|7[246]|8[2-4])\\d{6}",[8]],["9\\d{8}",[9]],["800\\d{5}",[8]],["805\\d{5}",[8]],["80[24]\\d{5}",[8]],0,0,0,0,["801\\d{5}",[8]]],"00"," Anexo "],PF:["689","00","4\\d{5}(?:\\d{2})?|8\\d{7,8}",[6,8,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["44"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["4|8[7-9]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]]],0,0,0,0,0,0,[["4(?:0[4-689]|9[4-68])\\d{5}",[8]],["8[7-9]\\d{6}",[8]],["80[0-5]\\d{6}",[9]],0,0,0,["44\\d{4}",[6]],0,["499\\d{5}",[8]]]],PG:["675","00|140[1-3]","(?:180|[78]\\d{3})\\d{4}|(?:[2-589]\\d|64)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["18|[2-69]|85"]],["(\\d{4})(\\d{4})","$1 $2",["[78]"]]],0,0,0,0,0,0,[["(?:(?:3[0-2]|4[257]|5[34]|9[78])\\d|64[1-9]|85[02-46-9])\\d{4}",[7]],["(?:7\\d|8[1-38])\\d{6}",[8]],["180\\d{4}",[7]],0,0,0,0,["27[01]\\d{4}",[7]],["2(?:0[0-57]|7[568])\\d{4}",[7]]],"00"],PH:["63","00","(?:[2-7]|9\\d)\\d{8}|2\\d{5}|(?:1800|8)\\d{7,9}",[6,8,9,10,11,12,13],[["(\\d)(\\d{5})","$1 $2",["2"],"(0$1)"],["(\\d{4})(\\d{4,6})","$1 $2",["3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2","3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))"],"(0$1)"],["(\\d{5})(\\d{4})","$1 $2",["346|4(?:27|9[35])|883","3469|4(?:279|9(?:30|56))|8834"],"(0$1)"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[3-7]|8[2-8]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]],["(\\d{4})(\\d{1,2})(\\d{3})(\\d{4})","$1 $2 $3 $4",["1"]]],"0",0,0,0,0,0,[["(?:(?:2[3-8]|3[2-68]|4[2-9]|5[2-6]|6[2-58]|7[24578])\\d{3}|88(?:22\\d\\d|42))\\d{4}|(?:2|8[2-8]\\d\\d)\\d{5}",[6,8,9,10]],["(?:8(?:1[37]|9[5-8])|9(?:0[5-9]|1[0-24-9]|[235-7]\\d|4[2-9]|8[135-9]|9[1-9]))\\d{7}",[10]],["1800\\d{7,9}",[11,12,13]]]],PK:["92","00","122\\d{6}|[24-8]\\d{10,11}|9(?:[013-9]\\d{8,10}|2(?:[01]\\d\\d|2(?:[06-8]\\d|1[01]))\\d{7})|(?:[2-8]\\d{3}|92(?:[0-7]\\d|8[1-9]))\\d{6}|[24-9]\\d{8}|[89]\\d{7}",[8,9,10,11,12],[["(\\d{3})(\\d{3})(\\d{2,7})","$1 $2 $3",["[89]0"],"0$1"],["(\\d{4})(\\d{5})","$1 $2",["1"]],["(\\d{3})(\\d{6,7})","$1 $2",["2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])","9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]"],"(0$1)"],["(\\d{2})(\\d{7,8})","$1 $2",["(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]"],"(0$1)"],["(\\d{5})(\\d{5})","$1 $2",["58"],"(0$1)"],["(\\d{3})(\\d{7})","$1 $2",["3"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[24-9]"],"(0$1)"]],"0",0,0,0,0,0,[["(?:(?:21|42)[2-9]|58[126])\\d{7}|(?:2[25]|4[0146-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]\\d{6,7}|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8]))[2-9]\\d{5,6}",[9,10]],["3(?:[0-247]\\d|3[0-79]|55|64)\\d{7}",[10]],["800\\d{5}(?:\\d{3})?",[8,11]],["900\\d{5}",[8]],["122\\d{6}",[9]],0,["(?:2(?:[125]|3[2358]|4[2-4]|9[2-8])|4(?:[0-246-9]|5[3479])|5(?:[1-35-7]|4[2-467])|6(?:0[468]|[1-8])|7(?:[14]|2[236])|8(?:[16]|2[2-689]|3[23578]|4[3478]|5[2356])|9(?:1|22|3[27-9]|4[2-6]|6[3569]|9[2-7]))111\\d{6}",[11,12]]]],PL:["48","00","(?:6|8\\d\\d)\\d{7}|[1-9]\\d{6}(?:\\d{2})?|[26]\\d{5}",[6,7,8,9,10],[["(\\d{5})","$1",["19"]],["(\\d{3})(\\d{3})","$1 $2",["11|20|64"]],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1","(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19"]],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["64"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)"]],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[2-8]|[2-7]|8[1-79]|9[145]"]],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[["47\\d{7}|(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])(?:[02-9]\\d{6}|1(?:[0-8]\\d{5}|9\\d{3}(?:\\d{2})?))",[7,9]],["21(?:1[013-5]|2\\d)\\d{5}|(?:45|5[0137]|6[069]|7[2389]|88)\\d{7}",[9]],["800\\d{6,7}",[9,10]],["70[01346-8]\\d{6}",[9]],0,0,["804\\d{6}",[9]],["64\\d{4,7}",[6,7,8,9]],["39\\d{7}",[9]],["801\\d{6}",[9]]]],PM:["508","00","[45]\\d{5}|(?:708|80\\d)\\d{6}",[6,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["[45]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["7"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[["(?:4[1-35-7]|5[01])\\d{4}",[6]],["(?:4[02-4]|5[056]|708[45][0-5])\\d{4}"],["80[0-5]\\d{6}",[9]]]],PR:["1","011","(?:[589]\\d\\d|787)\\d{7}",[10],0,"1",0,0,0,0,"787|939",[["(?:787|939)[2-9]\\d{6}"],[""],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],PS:["970","00","[2489]2\\d{6}|(?:1\\d|5)\\d{8}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2489]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["5"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[["(?:22[2-47-9]|42[45]|82[014-68]|92[3569])\\d{5}",[8]],["5[69]\\d{7}",[9]],["1800\\d{6}",[10]],0,0,0,0,0,0,["1700\\d{6}",[10]]]],PT:["351","00","1693\\d{5}|(?:[26-9]\\d|30)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["2[12]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["16|[236-9]"]]],0,0,0,0,0,0,[["2(?:[12]\\d|3[1-689]|4[1-59]|[57][1-9]|6[1-35689]|8[1-69]|9[1256])\\d{6}"],["6(?:[06]92(?:30|9\\d)|[35]92(?:[049]\\d|3[034]))\\d{3}|(?:(?:16|6[0356])93|9(?:[1-36]\\d\\d|480))\\d{5}"],["80[02]\\d{6}"],["(?:6(?:0[178]|4[68])\\d|76(?:0[1-57]|1[2-47]|2[237]))\\d{5}"],["884[0-4689]\\d{5}"],0,["70(?:38[01]|596|(?:7\\d|8[17])\\d)\\d{4}"],["6(?:222\\d|8988)\\d{4}"],["30\\d{7}"],["80(?:8\\d|9[1579])\\d{5}"]]],PW:["680","01[12]","(?:[24-8]\\d\\d|345|900)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[["(?:2(?:55|77)|345|488|5(?:35|44|87)|6(?:22|54|79)|7(?:33|47)|8(?:24|55|76)|900)\\d{4}"],["(?:(?:46|83)[0-5]|6[2-4689]0)\\d{4}|(?:45|77|88)\\d{5}"]]],PY:["595","00","59\\d{4,6}|9\\d{5,10}|(?:[2-46-8]\\d|5[0-8])\\d{4,7}",[6,7,8,9,10,11],[["(\\d{3})(\\d{3,6})","$1 $2",["[2-9]0"],"0$1"],["(\\d{2})(\\d{5})","$1 $2",["[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]"],"(0$1)"],["(\\d{3})(\\d{4,5})","$1 $2",["2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["87"]],["(\\d{3})(\\d{6})","$1 $2",["9(?:[5-79]|8[1-7])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[2-8]"],"0$1"],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["9"]]],"0",0,0,0,0,0,[["(?:[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36])\\d{5,7}|(?:2(?:2[4-68]|[4-68]\\d|7[15]|9[1-5])|3(?:18|3[167]|4[2357]|51|[67]\\d)|4(?:3[12]|5[13]|9[1-47])|5(?:[1-4]\\d|5[02-4])|6(?:3[1-3]|44|7[1-8])|7(?:4[0-4]|5\\d|6[1-578]|75|8[0-8])|858)\\d{5,6}",[7,8,9]],["9(?:51|6[129]|7[1-6]|8[1-7]|9[1-5])\\d{6}",[9]],["9800\\d{5,7}",[9,10,11]],0,0,0,["[2-9]0\\d{4,7}",[6,7,8,9]],0,["8700[0-4]\\d{4}",[9]]]],QA:["974","00","800\\d{4}|(?:2|800)\\d{6}|(?:0080|[3-7])\\d{7}",[7,8,9,11],[["(\\d{3})(\\d{4})","$1 $2",["2[16]|8"]],["(\\d{4})(\\d{4})","$1 $2",["[3-7]"]]],0,0,0,0,0,0,[["4(?:1111|2022)\\d{3}|4(?:[04]\\d\\d|14[0-6]|999)\\d{4}",[8]],["[35-7]\\d{7}",[8]],["800\\d{4}|(?:0080[01]|800)\\d{6}",[7,9,11]],0,0,0,0,["2[16]\\d{5}",[7]]]],RE:["262","00","(?:26|[689]\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2689]"],"0$1"]],"0",0,0,0,0,0,[["26(?:2\\d\\d|3(?:0\\d|1[0-6]))\\d{4}"],["69(?:2\\d\\d|3(?:[06][0-6]|1[013]|2[0-2]|3[0-39]|4\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))\\d{4}"],["80\\d{7}"],["89[1-37-9]\\d{6}"],0,0,0,0,["9(?:399[0-3]|479[0-5]|76(?:2[27]|3[0-37]))\\d{4}"],["8(?:1[019]|2[0156]|84|90)\\d{6}"]]],RO:["40","00","(?:[236-8]\\d|90)\\d{7}|[23]\\d{5}",[6,9],[["(\\d{3})(\\d{3})","$1 $2",["2[3-6]","2[3-6]\\d9"],"0$1"],["(\\d{2})(\\d{4})","$1 $2",["219|31"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[23]1"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[236-9]"],"0$1"]],"0",0,0,0,0,0,[["[23][13-6]\\d{7}|(?:2(?:19\\d|[3-6]\\d9)|31\\d\\d)\\d\\d"],["7020\\d{5}|(?:6(?:[04]0|2\\d)|7(?:0[013-9]|1[0-3]|[2-7]\\d|8[03-8]|9[0-39]))\\d{6}",[9]],["800\\d{6}",[9]],["90[0136]\\d{6}",[9]],0,0,["(?:37\\d|80[578])\\d{6}",[9]],0,0,["801\\d{6}",[9]]],0," int "],RS:["381","00","38[02-9]\\d{6,9}|6\\d{7,9}|90\\d{4,8}|38\\d{5,6}|(?:7\\d\\d|800)\\d{3,9}|(?:[12]\\d|3[0-79])\\d{5,10}",[6,7,8,9,10,11,12],[["(\\d{3})(\\d{3,9})","$1 $2",["(?:2[389]|39)0|[7-9]"],"0$1"],["(\\d{2})(\\d{5,10})","$1 $2",["[1-36]"],"0$1"]],"0",0,0,0,0,0,[["(?:11[1-9]\\d|(?:2[389]|39)(?:0[2-9]|[2-9]\\d))\\d{3,8}|(?:1[02-9]|2[0-24-7]|3[0-8])[2-9]\\d{4,9}",[7,8,9,10,11,12]],["6(?:[0-689]|7\\d)\\d{6,7}",[8,9,10]],["800\\d{3,9}"],["(?:78\\d|90[0169])\\d{3,7}",[6,7,8,9,10]],0,0,["7[06]\\d{4,10}"]]],RU:["7","810","8\\d{13}|[347-9]\\d{9}",[10,14],[["(\\d{4})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["7(?:1[0-8]|2[1-9])","7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))","7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2"],"8 ($1)",1],["(\\d{5})(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["7(?:1[0-68]|2[1-9])","7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))","7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]"],"8 ($1)",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"8 ($1)",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2-$3-$4",["[349]|8(?:[02-7]|1[1-8])"],"8 ($1)",1],["(\\d{4})(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3 $4",["8"],"8 ($1)"]],"8",0,0,0,0,"3[04-689]|[489]",[["(?:3(?:0[12]|4[1-35-79]|5[1-3]|65|8[1-58]|9[0145])|4(?:01|1[1356]|2[13467]|7[1-5]|8[1-7]|9[1-689])|8(?:1[1-8]|2[01]|3[13-6]|4[0-8]|5[15]|6[1-35-79]|7[1-37-9]))\\d{7}",[10]],["9\\d{9}",[10]],["8(?:0[04]|108\\d{3})\\d{7}"],["80[39]\\d{7}",[10]],["808\\d{7}",[10]]],"8~10"],RW:["250","00","(?:06|[27]\\d\\d|[89]00)\\d{6}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["0"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[7-9]"],"0$1"]],"0",0,0,0,0,0,[["(?:06|2[23568]\\d)\\d{6}"],["7[237-9]\\d{7}",[9]],["800\\d{6}",[9]],["900\\d{6}",[9]]]],SA:["966","00","92\\d{7}|(?:[15]|8\\d)\\d{8}",[9,10],[["(\\d{4})(\\d{5})","$1 $2",["9"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["5"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["81"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]]],"0",0,0,0,0,0,[["1(?:1\\d|2[24-8]|3[35-8]|4[3-68]|6[2-5]|7[235-7])\\d{6}",[9]],["579[01]\\d{5}|5(?:[013-689]\\d|7[0-8])\\d{6}",[9]],["800\\d{7}",[10]],["925\\d{6}",[9]],0,0,["811\\d{7}",[10]],0,0,["920\\d{6}",[9]]]],SB:["677","0[01]","[6-9]\\d{6}|[1-6]\\d{4}",[5,7],[["(\\d{2})(\\d{5})","$1 $2",["6[89]|7|8[4-9]|9(?:[1-8]|9[0-8])"]]],0,0,0,0,0,0,[["(?:1[4-79]|[23]\\d|4[0-2]|5[03]|6[0-37])\\d{3}",[5]],["48\\d{3}|(?:(?:6[89]|7[1-9]|8[4-9])\\d|9(?:1[2-9]|2[013-9]|3[0-2]|[46]\\d|5[0-46-9]|7[0-689]|8[0-79]|9[0-8]))\\d{4}"],["1[38]\\d{3}",[5]],0,0,0,0,0,["5[12]\\d{3}",[5]]]],SC:["248","010|0[0-2]","800\\d{4}|(?:[249]\\d|64)\\d{5}",[7],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[246]|9[57]"]]],0,0,0,0,0,0,[["4[2-46]\\d{5}"],["2[125-8]\\d{5}"],["800[08]\\d{3}"],0,0,0,0,0,["971\\d{4}|(?:64|95)\\d{5}"]],"00"],SD:["249","00","[19]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[19]"],"0$1"]],"0",0,0,0,0,0,[["1(?:5\\d|8[35-7])\\d{6}"],["(?:1[0-2]|9[0-3569])\\d{7}"]]],SE:["46","00","(?:[26]\\d\\d|9)\\d{9}|[1-9]\\d{8}|[1-689]\\d{7}|[1-4689]\\d{6}|2\\d{5}",[6,7,8,9,10],[["(\\d{2})(\\d{2,3})(\\d{2})","$1-$2 $3",["20"],"0$1",0,"$1 $2 $3"],["(\\d{3})(\\d{4})","$1-$2",["9(?:00|39|44|9)"],"0$1",0,"$1 $2"],["(\\d{2})(\\d{3})(\\d{2})","$1-$2 $3",["[12][136]|3[356]|4[0246]|6[03]|90[1-9]"],"0$1",0,"$1 $2 $3"],["(\\d)(\\d{2,3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["8"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2,3})(\\d{2})","$1-$2 $3",["1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])"],"0$1",0,"$1 $2 $3"],["(\\d{3})(\\d{2,3})(\\d{3})","$1-$2 $3",["9(?:00|39|44)"],"0$1",0,"$1 $2 $3"],["(\\d{2})(\\d{2,3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]"],"0$1",0,"$1 $2 $3 $4"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["10|7"],"0$1",0,"$1 $2 $3 $4"],["(\\d)(\\d{3})(\\d{3})(\\d{2})","$1-$2 $3 $4",["8"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1-$2 $3 $4",["[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{2})(\\d{3})","$1-$2 $3 $4",["9"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1-$2 $3 $4 $5",["[26]"],"0$1",0,"$1 $2 $3 $4 $5"]],"0",0,0,0,0,0,[["(?:(?:[12][136]|3[356]|4[0246]|6[03]|8\\d)\\d|90[1-9])\\d{4,6}|(?:1(?:2[0-35]|4[0-4]|5[0-25-9]|7[13-6]|[89]\\d)|2(?:2[0-7]|4[0136-8]|5[0138]|7[018]|8[01]|9[0-57])|3(?:0[0-4]|1\\d|2[0-25]|4[056]|7[0-2]|8[0-3]|9[023])|4(?:1[013-8]|3[0135]|5[14-79]|7[0-246-9]|8[0156]|9[0-689])|5(?:0[0-6]|[15][0-5]|2[0-68]|3[0-4]|4\\d|6[03-5]|7[013]|8[0-79]|9[01])|6(?:1[1-3]|2[0-4]|4[02-57]|5[0-37]|6[0-3]|7[0-2]|8[0247]|9[0-356])|9(?:1[0-68]|2\\d|3[02-5]|4[0-3]|5[0-4]|[68][01]|7[0135-8]))\\d{5,6}",[7,8,9]],["7[02369]\\d{7}",[9]],["20\\d{4,7}",[6,7,8,9]],["649\\d{6}|99[1-59]\\d{4}(?:\\d{3})?|9(?:00|39|44)[1-8]\\d{3,6}",[7,8,9,10]],["75[1-8]\\d{6}",[9]],0,["10[1-8]\\d{6}",[9]],["74[02-9]\\d{6}",[9]],0,["77[0-7]\\d{6}",[9]]]],SG:["65","0[0-3]\\d","(?:(?:1\\d|8)\\d\\d|7000)\\d{7}|[3689]\\d{7}",[8,10,11],[["(\\d{4})(\\d{4})","$1 $2",["[369]|8(?:0[1-9]|[1-9])"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]],["(\\d{4})(\\d{4})(\\d{3})","$1 $2 $3",["7"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[["662[0-24-9]\\d{4}|6(?:[0-578]\\d|6[013-57-9]|9[0-35-9])\\d{5}",[8]],["8(?:09[0-6]|95[0-2])\\d{4}|(?:8(?:0[1-8]|[1-8]\\d|9[0-4])|9[0-8]\\d)\\d{5}",[8]],["(?:18|8)00\\d{7}",[10,11]],["1900\\d{7}",[11]],0,0,["7000\\d{7}",[11]],0,["(?:3[12]\\d|666)\\d{5}",[8]]]],SH:["290","00","(?:[256]\\d|8)\\d{3}",[4,5],0,0,0,0,0,0,"[256]",[["2(?:[0-57-9]\\d|6[4-9])\\d\\d"],["[56]\\d{4}",[5]],0,0,0,0,0,0,["262\\d\\d",[5]]]],SI:["386","00|10(?:22|66|88|99)","[1-7]\\d{7}|8\\d{4,7}|90\\d{4,6}",[5,6,7,8],[["(\\d{2})(\\d{3,6})","$1 $2",["8[09]|9"],"0$1"],["(\\d{3})(\\d{5})","$1 $2",["59|8"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[37][01]|4[0139]|51|6"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[1-57]"],"(0$1)"]],"0",0,0,0,0,0,[["(?:[1-357][2-8]|4[24-8])\\d{6}",[8]],["65(?:[178]\\d|5[56]|6[01])\\d{4}|(?:[37][01]|4[0139]|51|6[489])\\d{6}",[8]],["80\\d{4,6}",[6,7,8]],["89[1-3]\\d{2,5}|90\\d{4,6}"],0,0,0,0,["(?:59\\d\\d|8(?:1(?:[67]\\d|8[0-589])|2(?:0\\d|2[0-37-9]|8[0-2489])|3[389]\\d))\\d{4}",[8]]],"00"],SJ:["47","00","0\\d{4}|(?:[489]\\d|79)\\d{6}",[5,8],0,0,0,0,0,0,"79",[["79\\d{6}",[8]],["(?:4[015-8]|9\\d)\\d{6}",[8]],["80[01]\\d{5}",[8]],["82[09]\\d{5}",[8]],["880\\d{5}",[8]],0,["(?:0[2-9]|81(?:0(?:0[7-9]|1\\d)|5\\d\\d))\\d{3}"],0,["85[0-5]\\d{5}",[8]],["810(?:0[0-6]|[2-8]\\d)\\d{3}",[8]]]],SK:["421","00","[2-689]\\d{8}|[2-59]\\d{6}|[2-5]\\d{5}",[6,7,9],[["(\\d)(\\d{2})(\\d{3,4})","$1 $2 $3",["21"],"0$1"],["(\\d{2})(\\d{2})(\\d{2,3})","$1 $2 $3",["[3-5][1-8]1","[3-5][1-8]1[67]"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{2})","$1/$2 $3 $4",["2"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[689]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1/$2 $3 $4",["[3-5]"],"0$1"]],"0",0,0,0,0,0,[["(?:2(?:16|[2-9]\\d{3})|(?:(?:[3-5][1-8]\\d|819)\\d|601[1-5])\\d)\\d{4}|(?:2|[3-5][1-8])1[67]\\d{3}|[3-5][1-8]16\\d\\d"],["909[1-9]\\d{5}|9(?:0[1-8]|1[0-24-9]|4[03-57-9]|5\\d)\\d{6}",[9]],["800\\d{6}",[9]],["9(?:00|[78]\\d)\\d{6}",[9]],0,0,["96\\d{7}",[9]],["9090\\d{3}",[7]],["6(?:02|5[0-4]|9[0-6])\\d{6}",[9]],["8[5-9]\\d{7}",[9]]]],SL:["232","00","(?:[237-9]\\d|66)\\d{6}",[8],[["(\\d{2})(\\d{6})","$1 $2",["[236-9]"],"(0$1)"]],"0",0,0,0,0,0,[["22[2-4][2-9]\\d{4}"],["(?:25|3[0-5]|66|7[2-9]|8[08]|9[09])\\d{6}"]]],SM:["378","00","(?:0549|[5-7]\\d)\\d{6}",[8,10],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-7]"]],["(\\d{4})(\\d{6})","$1 $2",["0"]]],0,0,"([89]\\d{5})$","0549$1",0,0,[["0549(?:8[0157-9]|9\\d)\\d{4}",[10]],["6[16]\\d{6}",[8]],0,["7[178]\\d{6}",[8]],0,0,0,0,["5[158]\\d{6}",[8]]]],SN:["221","00","(?:[378]\\d|93)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[379]"]]],0,0,0,0,0,0,[["3(?:0(?:1[0-2]|80)|282|3(?:8[1-9]|9[3-9])|611)\\d{5}"],["7(?:(?:[06-8]\\d|21|90)\\d|5(?:01|[19]0|25|[38]3|[4-7]\\d))\\d{5}"],["800\\d{6}"],["88[4689]\\d{6}"],0,0,0,0,["(?:3(?:392|9[01]\\d)\\d|93(?:3[13]0|929))\\d{4}"],["81[02468]\\d{6}"]]],SO:["252","00","[346-9]\\d{8}|[12679]\\d{7}|[1-5]\\d{6}|[1348]\\d{5}",[6,7,8,9],[["(\\d{2})(\\d{4})","$1 $2",["8[125]"]],["(\\d{6})","$1",["[134]"]],["(\\d)(\\d{6})","$1 $2",["[15]|2[0-79]|3[0-46-8]|4[0-7]"]],["(\\d)(\\d{7})","$1 $2",["(?:2|90)4|[67]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[348]|64|79|90"]],["(\\d{2})(\\d{5,7})","$1 $2",["1|28|6[0-35-9]|77|9[2-9]"]]],"0",0,0,0,0,0,[["(?:1\\d|2[0-79]|3[0-46-8]|4[0-7]|5[57-9])\\d{5}|(?:[134]\\d|8[125])\\d{4}",[6,7]],["(?:(?:15|(?:3[59]|4[89]|6\\d|7[79]|8[08])\\d|9(?:0\\d|[2-9]))\\d|2(?:4\\d|8))\\d{5}|(?:[67]\\d\\d|904)\\d{5}",[7,8,9]]]],SR:["597","00","(?:[2-5]|68|[78]\\d)\\d{5}",[6,7],[["(\\d{2})(\\d{2})(\\d{2})","$1-$2-$3",["56"]],["(\\d{3})(\\d{3})","$1-$2",["[2-5]"]],["(\\d{3})(\\d{4})","$1-$2",["[6-8]"]]],0,0,0,0,0,0,[["(?:2[1-3]|3[0-7]|(?:4|68)\\d|5[2-58])\\d{4}"],["(?:7[124-7]|8[124-9])\\d{5}",[7]],0,0,0,0,0,0,["56\\d{4}",[6]]]],SS:["211","00","[19]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[19]"],"0$1"]],"0",0,0,0,0,0,[["1[89]\\d{7}"],["(?:12|9[1257-9])\\d{7}"]]],ST:["239","00","(?:22|9\\d)\\d{5}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[29]"]]],0,0,0,0,0,0,[["22\\d{5}"],["900[5-9]\\d{3}|9(?:0[1-9]|[89]\\d)\\d{4}"]]],SV:["503","00","[267]\\d{7}|(?:80\\d|900)\\d{4}(?:\\d{4})?",[7,8,11],[["(\\d{3})(\\d{4})","$1 $2",["[89]"]],["(\\d{4})(\\d{4})","$1 $2",["[267]"]],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["[89]"]]],0,0,0,0,0,0,[["2(?:79(?:0[0347-9]|[1-9]\\d)|89(?:0[024589]|[1-9]\\d))\\d{3}|2(?:[1-69]\\d|[78][0-8])\\d{5}",[8]],["[67]\\d{7}",[8]],["800\\d{8}|80[01]\\d{4}",[7,11]],["900\\d{4}(?:\\d{4})?",[7,11]]]],SX:["1","011","7215\\d{6}|(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"(5\\d{6})$|1","721$1",0,"721",[["7215(?:4[2-8]|8[239]|9[056])\\d{4}"],["7215(?:1[02]|2\\d|5[034679]|8[014-8])\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],SY:["963","00","[1-39]\\d{8}|[1-5]\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-5]"],"0$1",1],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1",1]],"0",0,0,0,0,0,[["21\\d{6,7}|(?:1(?:[14]\\d|[2356])|2[235]|3(?:[13]\\d|4)|4[134]|5[1-3])\\d{6}"],["9[1-689]\\d{7}",[9]]]],SZ:["268","00","0800\\d{4}|(?:[237]\\d|900)\\d{6}",[8,9],[["(\\d{4})(\\d{4})","$1 $2",["[0237]"]],["(\\d{5})(\\d{4})","$1 $2",["9"]]],0,0,0,0,0,0,[["[23][2-5]\\d{6}",[8]],["7[6-9]\\d{6}",[8]],["0800\\d{4}",[8]],["900\\d{6}",[9]],0,0,0,0,["70\\d{6}",[8]]]],TA:["290","00","8\\d{3}",[4],0,0,0,0,0,0,"8",[["8\\d{3}"]]],TC:["1","011","(?:[58]\\d\\d|649|900)\\d{7}",[10],0,"1",0,"([2-479]\\d{6})$|1","649$1",0,"649",[["649(?:266|712|9(?:4\\d|50))\\d{4}"],["649(?:2(?:3[129]|4[1-79])|3\\d\\d|4[34][1-3])\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,0,0,["649(?:71[01]|966)\\d{4}"]]],TD:["235","00|16","(?:22|[69]\\d|77)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2679]"]]],0,0,0,0,0,0,[["22(?:[37-9]0|5[0-5]|6[89])\\d{4}"],["(?:6[0-689]|77|9\\d)\\d{6}"]],"00"],TG:["228","00","[279]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[279]"]]],0,0,0,0,0,0,[["2(?:2[2-7]|3[23]|4[45]|55|6[67]|77)\\d{5}"],["(?:7[019]|9[0-36-9])\\d{6}"]]],TH:["66","00[1-9]","(?:001800|[2-57]|[689]\\d)\\d{7}|1\\d{7,9}",[8,9,10,13],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[13-9]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[["(?:1[0689]|2\\d|3[2-9]|4[2-5]|5[2-6]|7[3-7])\\d{6}",[8]],["67(?:1[0-8]|2[4-7])\\d{5}|(?:14|6[1-6]|[89]\\d)\\d{7}",[9]],["(?:001800\\d|1800)\\d{6}",[10,13]],["1900\\d{6}",[10]],0,0,0,0,["6[08]\\d{7}",[9]]]],TJ:["992","810","[0-57-9]\\d{8}",[9],[["(\\d{6})(\\d)(\\d{2})","$1 $2 $3",["331","3317"]],["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["44[02-479]|[34]7"]],["(\\d{4})(\\d)(\\d{4})","$1 $2 $3",["3[1-5]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[0-57-9]"]]],0,0,0,0,0,0,[["(?:3(?:1[3-5]|2[245]|3[12]|4[24-7]|5[25]|72)|4(?:46|74|87))\\d{6}"],["(?:4(?:1[18]|4[02-479])|81[1-9])\\d{6}|(?:0[0-57-9]|1[017]|2[02]|[34]0|5[05]|7[01578]|8[078]|9\\d)\\d{7}"]],"8~10"],TK:["690","00","[2-47]\\d{3,6}",[4,5,6,7],0,0,0,0,0,0,0,[["(?:2[2-4]|[34]\\d)\\d{2,5}"],["7[2-4]\\d{2,5}"]]],TL:["670","00","7\\d{7}|(?:[2-47]\\d|[89]0)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["[2-489]|70"]],["(\\d{4})(\\d{4})","$1 $2",["7"]]],0,0,0,0,0,0,[["(?:2[1-5]|3[1-9]|4[1-4])\\d{5}",[7]],["7[2-8]\\d{6}",[8]],["80\\d{5}",[7]],["90\\d{5}",[7]],["70\\d{5}",[7]]]],TM:["993","810","(?:[1-6]\\d|71)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2-$3-$4",["12"],"(8 $1)"],["(\\d{3})(\\d)(\\d{2})(\\d{2})","$1 $2-$3-$4",["[1-5]"],"(8 $1)"],["(\\d{2})(\\d{6})","$1 $2",["[67]"],"8 $1"]],"8",0,0,0,0,0,[["(?:1(?:2\\d|3[1-9])|2(?:22|4[0-35-8])|3(?:22|4[03-9])|4(?:22|3[128]|4\\d|6[15])|5(?:22|5[7-9]|6[014-689]))\\d{5}"],["(?:6\\d|71)\\d{6}"]],"8~10"],TN:["216","00","[2-57-9]\\d{7}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2-57-9]"]]],0,0,0,0,0,0,[["81200\\d{3}|(?:3[0-2]|7\\d)\\d{6}"],["3(?:001|[12]40)\\d{4}|(?:(?:[259]\\d|4[0-8])\\d|3(?:1[1-35]|6[0-4]|91))\\d{5}"],["8010\\d{4}"],["88\\d{6}"],0,0,0,0,0,["8[12]10\\d{4}"]]],TO:["676","00","(?:0800|(?:[5-8]\\d\\d|999)\\d)\\d{3}|[2-8]\\d{4}",[5,7],[["(\\d{2})(\\d{3})","$1-$2",["[2-4]|50|6[09]|7[0-24-69]|8[05]"]],["(\\d{4})(\\d{3})","$1 $2",["0"]],["(\\d{3})(\\d{4})","$1 $2",["[5-9]"]]],0,0,0,0,0,0,[["(?:2\\d|3[0-8]|4[0-4]|50|6[09]|7[0-24-69]|8[05])\\d{3}",[5]],["(?:5(?:4[0-5]|5[4-6])|6(?:[09]\\d|3[02]|8[15-9])|(?:7\\d|8[46-9])\\d|999)\\d{4}",[7]],["0800\\d{3}",[7]],0,0,0,0,0,["55[0-37-9]\\d{4}",[7]]]],TR:["90","00","4\\d{6}|8\\d{11,12}|(?:[2-58]\\d\\d|900)\\d{7}",[7,10,12,13],[["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["512|8[01589]|90"],"0$1",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["5(?:[0-59]|61)","5(?:[0-59]|61[06])","5(?:[0-59]|61[06]1)"],"0$1",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24][1-8]|3[1-9]"],"(0$1)",1],["(\\d{3})(\\d{3})(\\d{6,7})","$1 $2 $3",["80"],"0$1",1]],"0",0,0,0,0,0,[["(?:2(?:[13][26]|[28][2468]|[45][268]|[67][246])|3(?:[13][28]|[24-6][2468]|[78][02468]|92)|4(?:[16][246]|[23578][2468]|4[26]))\\d{7}",[10]],["561(?:011|61\\d)\\d{4}|5(?:0[15-7]|1[06]|24|[34]\\d|5[1-59]|9[46])\\d{7}",[10]],["8(?:00\\d{7}(?:\\d{2,3})?|11\\d{7})",[10,12,13]],["(?:8[89]8|900)\\d{7}",[10]],["592(?:21[12]|461)\\d{4}",[10]],0,["444\\d{4}",[7]],["512\\d{7}",[10]],["850\\d{7}",[10]]]],TT:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-46-8]\\d{6})$|1","868$1",0,"868",[["868(?:2(?:01|1[5-9]|[23]\\d|4[0-2])|6(?:0[7-9]|1[02-8]|2[1-9]|[3-69]\\d|7[0-79])|82[124])\\d{4}"],["868(?:(?:2[5-9]|3\\d)\\d|4(?:3[0-6]|[6-9]\\d)|6(?:20|78|8\\d)|7(?:0[1-9]|1[02-9]|[2-9]\\d))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],TV:["688","00","(?:2|7\\d\\d|90)\\d{4}",[5,6,7],[["(\\d{2})(\\d{3})","$1 $2",["2"]],["(\\d{2})(\\d{4})","$1 $2",["90"]],["(\\d{2})(\\d{5})","$1 $2",["7"]]],0,0,0,0,0,0,[["2[02-9]\\d{3}",[5]],["(?:7[01]\\d|90)\\d{4}",[6,7]]]],TW:["886","0(?:0[25-79]|19)","[2-689]\\d{8}|7\\d{9,10}|[2-8]\\d{7}|2\\d{6}",[7,8,9,10,11],[["(\\d{2})(\\d)(\\d{4})","$1 $2 $3",["202"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[258]0"],"0$1"],["(\\d)(\\d{3,4})(\\d{4})","$1 $2 $3",["[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]","[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[49]"],"0$1"],["(\\d{2})(\\d{4})(\\d{4,5})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[["(?:2[2-8]\\d|370|55[01]|7[1-9])\\d{6}|4(?:(?:0(?:0[1-9]|[2-48]\\d)|1[023]\\d)\\d{4,5}|(?:[239]\\d\\d|4(?:0[56]|12|49))\\d{5})|6(?:[01]\\d{7}|4(?:0[56]|12|24|4[09])\\d{4,5})|8(?:(?:2(?:3\\d|4[0-269]|[578]0|66)|36[24-9]|90\\d\\d)\\d{4}|4(?:0[56]|12|24|4[09])\\d{4,5})|(?:2(?:2(?:0\\d\\d|4(?:0[68]|[249]0|3[0-467]|5[0-25-9]|6[0235689]))|(?:3(?:[09]\\d|1[0-4])|(?:4\\d|5[0-49]|6[0-29]|7[0-5])\\d)\\d)|(?:(?:3[2-9]|5[2-8]|6[0-35-79]|8[7-9])\\d\\d|4(?:2(?:[089]\\d|7[1-9])|(?:3[0-4]|[78]\\d|9[01])\\d))\\d)\\d{3}",[8,9]],["(?:40001[0-2]|9[0-8]\\d{4})\\d{3}",[9]],["80[0-79]\\d{6}|800\\d{5}",[8,9]],["20(?:[013-9]\\d\\d|2)\\d{4}",[7,9]],["99\\d{7}",[9]],0,["50[0-46-9]\\d{6}",[9]],0,["7010(?:[0-2679]\\d|3[0-7]|8[0-5])\\d{5}|70\\d{8}",[10,11]]],0,"#"],TZ:["255","00[056]","(?:[25-8]\\d|41|90)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[24]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["5"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[67]"],"0$1"]],"0",0,0,0,0,0,[["2[2-8]\\d{7}"],["(?:6[125-9]|7[13-9])\\d{7}"],["80[08]\\d{6}"],["90\\d{7}"],0,0,0,0,["41\\d{7}"],["8(?:40|6[01])\\d{6}"]]],UA:["380","00","[89]\\d{9}|[3-9]\\d{8}",[9,10],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]","6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]"],"0$1"],["(\\d{4})(\\d{5})","$1 $2",["3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])","3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[3-7]|89|9[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[["(?:3[1-8]|4[13-8]|5[1-7]|6[12459])\\d{7}",[9]],["(?:39|50|6[36-8]|7[1-357]|9[1-9])\\d{7}",[9]],["800[1-8]\\d{5,6}"],["900[239]\\d{5,6}"],0,0,0,0,["89[1-579]\\d{6}",[9]]],"0~0"],UG:["256","00[057]","800\\d{6}|(?:[29]0|[347]\\d)\\d{7}",[9],[["(\\d{4})(\\d{5})","$1 $2",["202","2024"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["[27-9]|4(?:6[45]|[7-9])"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["[34]"],"0$1"]],"0",0,0,0,0,0,[["20(?:(?:240|30[67])\\d|6(?:00[0-2]|30[0-4]))\\d{3}|(?:20(?:[017]\\d|2[5-9]|3[1-4]|5[0-4]|6[15-9])|[34]\\d{3})\\d{5}"],["72(?:[48]0|6[01])\\d{5}|7(?:[015-8]\\d|20|36|4[0-5]|9[89])\\d{6}"],["800[1-3]\\d{5}"],["90[1-3]\\d{6}"]]],US:["1","011","[2-9]\\d{9}|3\\d{6}",[10],[["(\\d{3})(\\d{4})","$1-$2",["310"],0,1],["(\\d{3})(\\d{3})(\\d{4})","($1) $2-$3",["[2-9]"],0,1,"$1-$2-$3"]],"1",0,0,0,0,0,[["(?:5056(?:[0-35-9]\\d|4[468])|7302[0-4]\\d)\\d{4}|(?:472[24]|505[2-57-9]|7306|983[2-47-9])\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[1459]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-57-9]|1[02-9]|2[013569]|3[0-24679]|4[167]|5[0-2]|6[01349]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|3[016]|4[0156]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-8]|3[1247]|4[037]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[068]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-389]|8[04-69]))[2-9]\\d{6}"],[""],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],UY:["598","0(?:0|1[3-9]\\d)","0004\\d{2,9}|[1249]\\d{7}|(?:[49]\\d|80)\\d{5}",[6,7,8,9,10,11,12,13],[["(\\d{3})(\\d{3,4})","$1 $2",["0"]],["(\\d{3})(\\d{4})","$1 $2",["[49]0|8"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1"],["(\\d{4})(\\d{4})","$1 $2",["[124]"]],["(\\d{3})(\\d{3})(\\d{2,4})","$1 $2 $3",["0"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{2,4})","$1 $2 $3 $4",["0"]]],"0",0,0,0,0,0,[["(?:1(?:770|9(?:20|87))|(?:2\\d|4[2-7])\\d\\d)\\d{4}",[8]],["9[1-9]\\d{6}",[8]],["0004\\d{2,9}|(?:405|80[05])\\d{4}"],["90[0-8]\\d{4}",[7]]],"00"," int. "],UZ:["998","810","(?:20|33|[5-79]\\d|88)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[235-9]"],"8 $1"]],"8",0,0,0,0,0,[["(?:55\\d\\d|6(?:1(?:22|3[124]|4[1-4]|5[1-3578]|64)|2(?:22|3[0-57-9]|41)|5(?:22|3[3-7]|5[024-8])|6\\d\\d|7(?:[23]\\d|7[69])|9(?:22|4[1-8]|6[135]))|7(?:0(?:5[4-9]|6[0146]|7[124-6]|9[135-8])|(?:1[12]|[68]\\d)\\d|2(?:22|3[13-57-9]|4[1-3579]|5[14])|3(?:2\\d|3[1578]|4[1-35-7]|5[1-57]|61)|4(?:2\\d|3[1-579]|7[1-79])|5(?:22|5[1-9]|6[1457])|9(?:22|5[1-9])))\\d{5}"],["(?:(?:[25]0|33|88|9[0-57-9])\\d{3}|6(?:1(?:2(?:2[01]|98)|35[0-4]|50\\d|61[23]|7(?:[01][017]|4\\d|55|9[5-9]))|2(?:(?:11|7\\d)\\d|2(?:[12]1|9[01379])|5(?:[126]\\d|3[0-4]))|5(?:19[01]|2(?:27|9[26])|(?:30|59|7\\d)\\d)|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|(?:3[79]|9[0-3])\\d|4(?:56|83)|7(?:[07]\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79]))|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\d|[39][07])|9(?:0\\d|7[079]))|9(?:2(?:1[1267]|3[01]|5\\d|7[0-4])|(?:5[67]|7\\d)\\d|6(?:2[0-26]|8\\d)))|7(?:[07]\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\d|7(?:0\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|(?:33|9[4-6])\\d|5(?:0[0-4]|5[579]|9\\d)|7(?:[0-3579]\\d|4[0467]|6[67]|8[078]))|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\d|9[5-9])|7(?:0\\d|1[024589]|2[0-27]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\d))|5(?:112|2(?:0\\d|2[29]|[49]4)|3[1568]\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\d|8[78]|9[079]))|9(?:22[128]|3(?:2[0-4]|7\\d)|57[02569]|7(?:2[05-9]|3[37]|4\\d|60|7[2579]|87|9[07]))))\\d{4}"]],"8~10"],VA:["39","00","0\\d{5,10}|3[0-8]\\d{7,10}|55\\d{8}|8\\d{5}(?:\\d{2,4})?|(?:1\\d|39)\\d{7,8}",[6,7,8,9,10,11],0,0,0,0,0,0,"06698",[["06698\\d{1,6}"],["3[1-9]\\d{8}|3[2-9]\\d{7}",[9,10]],["80(?:0\\d{3}|3)\\d{3}",[6,9]],["(?:0878\\d{3}|89(?:2\\d|3[04]|4(?:[0-4]|[5-9]\\d\\d)|5[0-4]))\\d\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\d{6}",[6,8,9,10]],["1(?:78\\d|99)\\d{6}",[9,10]],0,0,0,["55\\d{8}",[10]],["84(?:[08]\\d{3}|[17])\\d{3}",[6,9]]]],VC:["1","011","(?:[58]\\d\\d|784|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","784$1",0,"784",[["784(?:266|3(?:6[6-9]|7\\d|8[0-6])|4(?:38|5[0-36-8]|8[0-8])|5(?:55|7[0-2]|93)|638|784)\\d{4}"],["784(?:4(?:3[0-5]|5[45]|89|9[0-8])|5(?:2[6-9]|3[0-4])|720)\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"],0,0,0,["78451[0-2]\\d{4}"]]],VE:["58","00","[68]00\\d{7}|(?:[24]\\d|[59]0)\\d{8}",[10],[["(\\d{3})(\\d{7})","$1-$2",["[24-689]"],"0$1"]],"0",0,0,0,0,0,[["(?:2(?:12|3[457-9]|[467]\\d|[58][1-9]|9[1-6])|[4-6]00)\\d{7}"],["4(?:1[24-8]|2[46])\\d{7}"],["800\\d{7}"],["90[01]\\d{7}"],0,0,["501\\d{7}"]]],VG:["1","011","(?:284|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-578]\\d{6})$|1","284$1",0,"284",[["284(?:229|4(?:22|9[45])|774|8(?:52|6[459]))\\d{4}"],["284(?:245|3(?:0[0-3]|4[0-7]|68|9[34])|4(?:4[0-6]|68|9[69])|5(?:4[0-7]|68|9[69]))\\d{4}"],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],VI:["1","011","[58]\\d{9}|(?:34|90)0\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","340$1",0,"340",[["340(?:2(?:0\\d|2[06-8]|4[49]|77)|3(?:32|44)|4(?:2[23]|44|7[34]|89)|5(?:1[34]|55)|6(?:2[56]|4[23]|77|9[023])|7(?:1[2-57-9]|2[57]|7\\d)|884|998)\\d{4}"],[""],["8(?:00|33|44|55|66|77|88)[2-9]\\d{6}"],["900[2-9]\\d{6}"],["52(?:3(?:[2-46-9][02-9]\\d|5(?:[02-46-9]\\d|5[0-46-9]))|4(?:[2-478][02-9]\\d|5(?:[034]\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\d)|9(?:[05-9]\\d|2[0-5]|49)))\\d{4}|52[34][2-9]1[02-9]\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\d{6}"]]],VN:["84","00","[12]\\d{9}|[135-9]\\d{8}|[16]\\d{7}|[16-8]\\d{6}",[7,8,9,10],[["(\\d{2})(\\d{5})","$1 $2",["80"],"0$1",1],["(\\d{4})(\\d{4,6})","$1 $2",["1"],0,1],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["6"],"0$1",1],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[357-9]"],"0$1",1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["2[48]"],"0$1",1],["(\\d{3})(\\d{4})(\\d{3})","$1 $2 $3",["2"],"0$1",1]],"0",0,0,0,0,0,[["2(?:0[3-9]|1[0-689]|2[0-25-9]|[38][2-9]|4[2-8]|5[124-9]|6[0-39]|7[0-7]|9[0-4679])\\d{7}",[10]],["(?:5(?:2[238]|59)|89[6-9]|99[013-9])\\d{6}|(?:3\\d|5[1689]|7[06-9]|8[1-8]|9[0-8])\\d{7}",[9]],["1800\\d{4,6}|12(?:0[13]|28)\\d{4}",[8,9,10]],["1900\\d{4,6}",[8,9,10]],0,0,["(?:[17]99|80\\d)\\d{4}|69\\d{5,6}",[7,8]],0,["672\\d{6}",[9]]]],VU:["678","00","[57-9]\\d{6}|(?:[238]\\d|48)\\d{3}",[5,7],[["(\\d{3})(\\d{4})","$1 $2",["[57-9]"]]],0,0,0,0,0,0,[["(?:38[0-8]|48[4-9])\\d\\d|(?:2[02-9]|3[4-7]|88)\\d{3}",[5]],["(?:[58]\\d|7[013-7])\\d{5}",[7]],["81[18]\\d\\d",[5]],0,0,0,["(?:3[03]|900\\d)\\d{3}"],0,["9(?:0[1-9]|1[01])\\d{4}",[7]]]],WF:["681","00","(?:40|72)\\d{4}|8\\d{5}(?:\\d{3})?",[6,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["[478]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]]],0,0,0,0,0,0,[["72\\d{4}",[6]],["(?:72|8[23])\\d{4}",[6]],["80[0-5]\\d{6}",[9]]]],WS:["685","0","(?:[2-6]|8\\d{5})\\d{4}|[78]\\d{6}|[68]\\d{5}",[5,6,7,10],[["(\\d{5})","$1",["[2-5]|6[1-9]"]],["(\\d{3})(\\d{3,7})","$1 $2",["[68]"]],["(\\d{2})(\\d{5})","$1 $2",["7"]]],0,0,0,0,0,0,[["6[1-9]\\d{3}|(?:[2-5]|60)\\d{4}",[5,6]],["(?:7[1-35-7]|8(?:[3-7]|9\\d{3}))\\d{5}",[7,10]],["800\\d{3}",[6]]]],XK:["383","00","2\\d{7,8}|3\\d{7,11}|(?:4\\d\\d|[89]00)\\d{5}",[8,9,10,11,12],[["(\\d{3})(\\d{5})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2-4]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2|39"],"0$1"],["(\\d{2})(\\d{7,10})","$1 $2",["3"],"0$1"]],"0",0,0,0,0,0,[["38\\d{6,10}|(?:2[89]|39)(?:0\\d{5,6}|[1-9]\\d{5})"],["4[3-9]\\d{6}",[8]],["800\\d{5}",[8]],["900\\d{5}",[8]]]],YE:["967","00","(?:1|7\\d)\\d{7}|[1-7]\\d{6}",[7,8,9],[["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-6]|7(?:[24-6]|8[0-7])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[["78[0-7]\\d{4}|17\\d{6}|(?:[12][2-68]|3[2358]|4[2-58]|5[2-6]|6[3-58]|7[24-6])\\d{5}",[7,8]],["7[01378]\\d{7}",[9]]]],YT:["262","00","(?:80|9\\d)\\d{7}|(?:26|63)9\\d{6}",[9],0,"0",0,0,0,0,0,[["269(?:0[0-467]|15|5[0-4]|6\\d|[78]0)\\d{4}"],["639(?:0[0-79]|1[019]|[267]\\d|3[09]|40|5[05-9]|9[04-79])\\d{4}"],["80\\d{7}"],0,0,0,0,0,["9(?:(?:39|47)8[01]|769\\d)\\d{4}"]]],ZA:["27","00","[1-79]\\d{8}|8\\d{4,9}",[5,6,7,8,9,10],[["(\\d{2})(\\d{3,4})","$1 $2",["8[1-4]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["8[1-4]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["860"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[["(?:2(?:0330|4302)|52087)0\\d{3}|(?:1[0-8]|2[1-378]|3[1-69]|4\\d|5[1346-8])\\d{7}",[9]],["(?:1(?:3492[0-25]|4495[0235]|549(?:20|5[01]))|4[34]492[01])\\d{3}|8[1-4]\\d{3,7}|(?:2[27]|47|54)4950\\d{3}|(?:1(?:049[2-4]|9[12]\\d\\d)|(?:6\\d|7[0-46-9])\\d{3}|8(?:5\\d{3}|7(?:08[67]|158|28[5-9]|310)))\\d{4}|(?:1[6-8]|28|3[2-69]|4[025689]|5[36-8])4920\\d{3}|(?:12|[2-5]1)492\\d{4}",[5,6,7,8,9]],["80\\d{7}",[9]],["(?:86[2-9]|9[0-2]\\d)\\d{6}",[9]],0,0,["861\\d{6,7}",[9,10]],0,["87(?:08[0-589]|15[0-79]|28[0-4]|31[1-9])\\d{4}|87(?:[02][0-79]|1[0-46-9]|3[02-9]|[4-9]\\d)\\d{5}",[9]],["860\\d{6}",[9]]]],ZM:["260","00","800\\d{6}|(?:21|63|[79]\\d)\\d{7}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[28]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["[79]"],"0$1"]],"0",0,0,0,0,0,[["21[1-8]\\d{6}"],["(?:7[5-79]|9[5-8])\\d{7}"],["800\\d{6}"],0,0,0,0,0,["63\\d{7}"]]],ZW:["263","00","2(?:[0-57-9]\\d{6,8}|6[0-24-9]\\d{6,7})|[38]\\d{9}|[35-8]\\d{8}|[3-6]\\d{7}|[1-689]\\d{6}|[1-3569]\\d{5}|[1356]\\d{4}",[5,6,7,8,9,10],[["(\\d{3})(\\d{3,5})","$1 $2",["2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]"],"0$1"],["(\\d)(\\d{3})(\\d{2,4})","$1 $2 $3",["[49]"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["80"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2","2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)","2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)"],"0$1"],["(\\d{4})(\\d{6})","$1 $2",["8"],"0$1"],["(\\d{2})(\\d{3,5})","$1 $2",["1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["29[013-9]|39|54"],"0$1"],["(\\d{4})(\\d{3,5})","$1 $2",["(?:25|54)8","258|5483"],"0$1"]],"0",0,0,0,0,0,[["(?:1(?:(?:3\\d|9)\\d|[4-8])|2(?:(?:(?:0(?:2[014]|5)|(?:2[0157]|31|84|9)\\d\\d|[56](?:[14]\\d\\d|20)|7(?:[089]|2[03]|[35]\\d\\d))\\d|4(?:2\\d\\d|8))\\d|1(?:2|[39]\\d{4}))|3(?:(?:123|(?:29\\d|92)\\d)\\d\\d|7(?:[19]|[56]\\d))|5(?:0|1[2-478]|26|[37]2|4(?:2\\d{3}|83)|5(?:25\\d\\d|[78])|[689]\\d)|6(?:(?:[16-8]21|28|52[013])\\d\\d|[39])|8(?:[1349]28|523)\\d\\d)\\d{3}|(?:4\\d\\d|9[2-9])\\d{4,5}|(?:(?:2(?:(?:(?:0|8[146])\\d|7[1-7])\\d|2(?:[278]\\d|92)|58(?:2\\d|3))|3(?:[26]|9\\d{3})|5(?:4\\d|5)\\d\\d)\\d|6(?:(?:(?:[0-246]|[78]\\d)\\d|37)\\d|5[2-8]))\\d\\d|(?:2(?:[569]\\d|8[2-57-9])|3(?:[013-59]\\d|8[37])|6[89]8)\\d{3}"],["7(?:[1278]\\d|3[1-9])\\d{6}",[9]],["80(?:[01]\\d|20|8[0-8])\\d{3}",[7]],0,0,0,0,0,["86(?:1[12]|22|30|44|55|77|8[368])\\d{6}",[10]]]]},nonGeographic:{800:["800",0,"(?:00|[1-9]\\d)\\d{6}",[8],[["(\\d{4})(\\d{4})","$1 $2",["\\d"]]],0,0,0,0,0,0,[0,0,["(?:00|[1-9]\\d)\\d{6}"]]],808:["808",0,"[1-9]\\d{7}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[1-9]"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,0,["[1-9]\\d{7}"]]],870:["870",0,"7\\d{11}|[35-7]\\d{8}",[9,12],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[35-7]"]]],0,0,0,0,0,0,[0,["(?:[356]|774[45])\\d{8}|7[6-8]\\d{7}"]]],878:["878",0,"10\\d{10}",[12],[["(\\d{2})(\\d{5})(\\d{5})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,["10\\d{10}"]]],881:["881",0,"6\\d{9}|[0-36-9]\\d{8}",[9,10],[["(\\d)(\\d{3})(\\d{5})","$1 $2 $3",["[0-37-9]"]],["(\\d)(\\d{3})(\\d{5,6})","$1 $2 $3",["6"]]],0,0,0,0,0,0,[0,["6\\d{9}|[0-36-9]\\d{8}"]]],882:["882",0,"[13]\\d{6}(?:\\d{2,5})?|[19]\\d{7}|(?:[25]\\d\\d|4)\\d{7}(?:\\d{2})?",[7,8,9,10,11,12],[["(\\d{2})(\\d{5})","$1 $2",["16|342"]],["(\\d{2})(\\d{6})","$1 $2",["49"]],["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["1[36]|9"]],["(\\d{2})(\\d{4})(\\d{3})","$1 $2 $3",["3[23]"]],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["16"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["10|23|3(?:[15]|4[57])|4|51"]],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["34"]],["(\\d{2})(\\d{4,5})(\\d{5})","$1 $2 $3",["[1-35]"]]],0,0,0,0,0,0,[0,["342\\d{4}|(?:337|49)\\d{6}|(?:3(?:2|47|7\\d{3})|50\\d{3})\\d{7}",[7,8,9,10,12]],0,0,0,0,0,0,["1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\d{4}|6\\d{5,10})|(?:345\\d|9[89])\\d{6}|(?:10|2(?:3|85\\d)|3(?:[15]|[69]\\d\\d)|4[15-8]|51)\\d{8}"]]],883:["883",0,"(?:[1-4]\\d|51)\\d{6,10}",[8,9,10,11,12],[["(\\d{3})(\\d{3})(\\d{2,8})","$1 $2 $3",["[14]|2[24-689]|3[02-689]|51[24-9]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["510"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["21"]],["(\\d{4})(\\d{4})(\\d{4})","$1 $2 $3",["51[13]"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[235]"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,["(?:2(?:00\\d\\d|10)|(?:370[1-9]|51\\d0)\\d)\\d{7}|51(?:00\\d{5}|[24-9]0\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\d{5,9}"]]],888:["888",0,"\\d{11}",[11],[["(\\d{3})(\\d{3})(\\d{5})","$1 $2 $3"]],0,0,0,0,0,0,[0,0,0,0,0,0,["\\d{11}"]]],979:["979",0,"[1359]\\d{8}",[9],[["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["[1359]"]]],0,0,0,0,0,0,[0,0,0,["[1359]\\d{8}"]]]}};function Bn(t,e){var d=Array.prototype.slice.call(e);return d.push(wn),t.apply(this,d)}function Fn(t){return Fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(t)}function Zn(t,e){for(var d=0;d<e.length;d++){var r=e[d];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Gn(t,e){if(e&&("object"===Fn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Un(t)}function Un(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function jn(t){var e="function"==typeof Map?new Map:void 0;return jn=function(t){if(null===t||(d=t,-1===Function.toString.call(d).indexOf("[native code]")))return t;var d;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return Hn(t,arguments,kn(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Kn(r,t)},jn(t)}function Hn(t,e,d){return Hn=Vn()?Reflect.construct:function(t,e,d){var r=[null];r.push.apply(r,e);var n=new(Function.bind.apply(t,r));return d&&Kn(n,d.prototype),n},Hn.apply(null,arguments)}function Vn(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function Kn(t,e){return Kn=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},Kn(t,e)}function kn(t){return kn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},kn(t)}var Wn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Kn(t,e)}(i,t);var e,d,r,n,a,o=(e=i,d=Vn(),function(){var t,r=kn(e);if(d){var n=kn(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return Gn(this,t)});function i(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),e=o.call(this,t),Object.setPrototypeOf(Un(e),i.prototype),e.name=e.constructor.name,e}return r=i,n&&Zn(r.prototype,n),a&&Zn(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r}(jn(Error)),Yn="0-9０-９٠-٩۰-۹",Xn="".concat("-‐-―−ー－").concat("／/").concat("．.").concat("  ­​⁠　").concat("()（）［］\\[\\]").concat("~⁓∼～");function zn(t,e){t=t.split("-"),e=e.split("-");for(var d=t[0].split("."),r=e[0].split("."),n=0;n<3;n++){var a=Number(d[n]),o=Number(r[n]);if(a>o)return 1;if(o>a)return-1;if(!isNaN(a)&&isNaN(o))return 1;if(isNaN(a)&&!isNaN(o))return-1}return t[1]&&e[1]?t[1]>e[1]?1:t[1]<e[1]?-1:0:!t[1]&&e[1]?1:t[1]&&!e[1]?-1:0}var Jn={}.constructor;function qn(t){return null!=t&&t.constructor===Jn}function Qn(t){return Qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(t)}function ta(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ea(t,e){for(var d=0;d<e.length;d++){var r=e[d];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function da(t,e,d){return e&&ea(t.prototype,e),d&&ea(t,d),Object.defineProperty(t,"prototype",{writable:!1}),t}var ra=" ext. ",na=/^\d+$/,aa=function(){function t(e){ta(this,t),function(t){if(!t)throw new Error("[libphonenumber-js] `metadata` argument not passed. Check your arguments.");if(!qn(t)||!qn(t.countries))throw new Error("[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got ".concat(qn(t)?"an object of shape: { "+Object.keys(t).join(", ")+" }":"a "+ca(t)+": "+t,"."))}(e),this.metadata=e,$a.call(this,e)}return da(t,[{key:"getCountries",value:function(){return Object.keys(this.metadata.countries).filter((function(t){return"001"!==t}))}},{key:"getCountryMetadata",value:function(t){return this.metadata.countries[t]}},{key:"nonGeographic",value:function(){if(!(this.v1||this.v2||this.v3))return this.metadata.nonGeographic||this.metadata.nonGeographical}},{key:"hasCountry",value:function(t){return void 0!==this.getCountryMetadata(t)}},{key:"hasCallingCode",value:function(t){if(this.getCountryCodesForCallingCode(t))return!0;if(this.nonGeographic()){if(this.nonGeographic()[t])return!0}else{var e=this.countryCallingCodes()[t];if(e&&1===e.length&&"001"===e[0])return!0}}},{key:"isNonGeographicCallingCode",value:function(t){return this.nonGeographic()?!!this.nonGeographic()[t]:!this.getCountryCodesForCallingCode(t)}},{key:"country",value:function(t){return this.selectNumberingPlan(t)}},{key:"selectNumberingPlan",value:function(t,e){if(t&&na.test(t)&&(e=t,t=null),t&&"001"!==t){if(!this.hasCountry(t))throw new Error("Unknown country: ".concat(t));this.numberingPlan=new oa(this.getCountryMetadata(t),this)}else if(e){if(!this.hasCallingCode(e))throw new Error("Unknown calling code: ".concat(e));this.numberingPlan=new oa(this.getNumberingPlanMetadata(e),this)}else this.numberingPlan=void 0;return this}},{key:"getCountryCodesForCallingCode",value:function(t){var e=this.countryCallingCodes()[t];if(e){if(1===e.length&&3===e[0].length)return;return e}}},{key:"getCountryCodeForCallingCode",value:function(t){var e=this.getCountryCodesForCallingCode(t);if(e)return e[0]}},{key:"getNumberingPlanMetadata",value:function(t){var e=this.getCountryCodeForCallingCode(t);if(e)return this.getCountryMetadata(e);if(this.nonGeographic()){var d=this.nonGeographic()[t];if(d)return d}else{var r=this.countryCallingCodes()[t];if(r&&1===r.length&&"001"===r[0])return this.metadata.countries["001"]}}},{key:"countryCallingCode",value:function(){return this.numberingPlan.callingCode()}},{key:"IDDPrefix",value:function(){return this.numberingPlan.IDDPrefix()}},{key:"defaultIDDPrefix",value:function(){return this.numberingPlan.defaultIDDPrefix()}},{key:"nationalNumberPattern",value:function(){return this.numberingPlan.nationalNumberPattern()}},{key:"possibleLengths",value:function(){return this.numberingPlan.possibleLengths()}},{key:"formats",value:function(){return this.numberingPlan.formats()}},{key:"nationalPrefixForParsing",value:function(){return this.numberingPlan.nationalPrefixForParsing()}},{key:"nationalPrefixTransformRule",value:function(){return this.numberingPlan.nationalPrefixTransformRule()}},{key:"leadingDigits",value:function(){return this.numberingPlan.leadingDigits()}},{key:"hasTypes",value:function(){return this.numberingPlan.hasTypes()}},{key:"type",value:function(t){return this.numberingPlan.type(t)}},{key:"ext",value:function(){return this.numberingPlan.ext()}},{key:"countryCallingCodes",value:function(){return this.v1?this.metadata.country_phone_code_to_countries:this.metadata.country_calling_codes}},{key:"chooseCountryByCountryCallingCode",value:function(t){return this.selectNumberingPlan(t)}},{key:"hasSelectedNumberingPlan",value:function(){return void 0!==this.numberingPlan}}]),t}(),oa=function(){function t(e,d){ta(this,t),this.globalMetadataObject=d,this.metadata=e,$a.call(this,d.metadata)}return da(t,[{key:"callingCode",value:function(){return this.metadata[0]}},{key:"getDefaultCountryMetadataForRegion",value:function(){return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode())}},{key:"IDDPrefix",value:function(){if(!this.v1&&!this.v2)return this.metadata[1]}},{key:"defaultIDDPrefix",value:function(){if(!this.v1&&!this.v2)return this.metadata[12]}},{key:"nationalNumberPattern",value:function(){return this.v1||this.v2?this.metadata[1]:this.metadata[2]}},{key:"possibleLengths",value:function(){if(!this.v1)return this.metadata[this.v2?2:3]}},{key:"_getFormats",value:function(t){return t[this.v1?2:this.v2?3:4]}},{key:"formats",value:function(){var t=this,e=this._getFormats(this.metadata)||this._getFormats(this.getDefaultCountryMetadataForRegion())||[];return e.map((function(e){return new ia(e,t)}))}},{key:"nationalPrefix",value:function(){return this.metadata[this.v1?3:this.v2?4:5]}},{key:"_getNationalPrefixFormattingRule",value:function(t){return t[this.v1?4:this.v2?5:6]}},{key:"nationalPrefixFormattingRule",value:function(){return this._getNationalPrefixFormattingRule(this.metadata)||this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion())}},{key:"_nationalPrefixForParsing",value:function(){return this.metadata[this.v1?5:this.v2?6:7]}},{key:"nationalPrefixForParsing",value:function(){return this._nationalPrefixForParsing()||this.nationalPrefix()}},{key:"nationalPrefixTransformRule",value:function(){return this.metadata[this.v1?6:this.v2?7:8]}},{key:"_getNationalPrefixIsOptionalWhenFormatting",value:function(){return!!this.metadata[this.v1?7:this.v2?8:9]}},{key:"nationalPrefixIsOptionalWhenFormattingInNationalFormat",value:function(){return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata)||this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion())}},{key:"leadingDigits",value:function(){return this.metadata[this.v1?8:this.v2?9:10]}},{key:"types",value:function(){return this.metadata[this.v1?9:this.v2?10:11]}},{key:"hasTypes",value:function(){return(!this.types()||0!==this.types().length)&&!!this.types()}},{key:"type",value:function(t){if(this.hasTypes()&&la(this.types(),t))return new sa(la(this.types(),t),this)}},{key:"ext",value:function(){return this.v1||this.v2?ra:this.metadata[13]||ra}}]),t}(),ia=function(){function t(e,d){ta(this,t),this._format=e,this.metadata=d}return da(t,[{key:"pattern",value:function(){return this._format[0]}},{key:"format",value:function(){return this._format[1]}},{key:"leadingDigitsPatterns",value:function(){return this._format[2]||[]}},{key:"nationalPrefixFormattingRule",value:function(){return this._format[3]||this.metadata.nationalPrefixFormattingRule()}},{key:"nationalPrefixIsOptionalWhenFormattingInNationalFormat",value:function(){return!!this._format[4]||this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat()}},{key:"nationalPrefixIsMandatoryWhenFormattingInNationalFormat",value:function(){return this.usesNationalPrefix()&&!this.nationalPrefixIsOptionalWhenFormattingInNationalFormat()}},{key:"usesNationalPrefix",value:function(){return!(!this.nationalPrefixFormattingRule()||ua.test(this.nationalPrefixFormattingRule()))}},{key:"internationalFormat",value:function(){return this._format[5]||this.format()}}]),t}(),ua=/^\(?\$1\)?$/,sa=function(){function t(e,d){ta(this,t),this.type=e,this.metadata=d}return da(t,[{key:"pattern",value:function(){return this.metadata.v1?this.type:this.type[0]}},{key:"possibleLengths",value:function(){if(!this.metadata.v1)return this.type[1]||this.metadata.possibleLengths()}}]),t}();function la(t,e){switch(e){case"FIXED_LINE":return t[0];case"MOBILE":return t[1];case"TOLL_FREE":return t[2];case"PREMIUM_RATE":return t[3];case"PERSONAL_NUMBER":return t[4];case"VOICEMAIL":return t[5];case"UAN":return t[6];case"PAGER":return t[7];case"VOIP":return t[8];case"SHARED_COST":return t[9]}}var ca=function(t){return Qn(t)};function fa(t,e){if((e=new aa(e)).hasCountry(t))return e.country(t).countryCallingCode();throw new Error("Unknown country: ".concat(t))}function $a(t){var e=t.version;"number"==typeof e?(this.v1=1===e,this.v2=2===e,this.v3=3===e,this.v4=4===e):e?-1===zn(e,"1.2.0")?this.v2=!0:-1===zn(e,"1.7.35")?this.v3=!0:this.v4=!0:this.v1=!0}var pa=function(t){return"([".concat(Yn,"]{1,").concat(t,"})")};function va(t){var e="#?";return";ext="+pa("20")+"|"+("[  \\t,]*(?:e?xt(?:ensi(?:ó?|ó))?n?|ｅ?ｘｔｎ?|доб|anexo)[:\\.．]?[  \\t,-]*"+pa("20")+e)+"|"+("[  \\t,]*(?:[xｘ#＃~～]|int|ｉｎｔ)[:\\.．]?[  \\t,-]*"+pa("9")+e)+"|"+("[- ]+"+pa("6")+"#")+"|"+("[  \\t]*(?:,{2}|;)[:\\.．]?[  \\t,-]*"+pa("15")+e)+"|"+("[  \\t]*(?:,)+[:\\.．]?[  \\t,-]*"+pa("9")+e)}var ya="[+＋]{0,1}(?:["+Xn+"]*["+"0-9０-９٠-٩۰-۹]){3,}["+Xn+"0-9０-９٠-٩۰-۹]*",ma=new RegExp("^[+＋]{0,1}(?:["+Xn+"]*["+"0-9０-９٠-٩۰-۹]){1,2}$","i"),ga=ya+"(?:"+va()+")?",ha=new RegExp("^[0-9０-９٠-٩۰-۹]{2}$|^"+ga+"$","i");var Aa=new RegExp("(?:"+va()+")$","i");var ba={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"};function Ia(t,e){var d="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(d)return(d=d.call(t)).next.bind(d);if(Array.isArray(t)||(d=function(t,e){if(!t)return;if("string"==typeof t)return Ma(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return Ma(t,e)}(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ma(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}function Sa(t){for(var e,d="",r=Ia(t.split(""));!(e=r()).done;){d+=_a(e.value,d)||""}return d}function _a(t,e,d){return"+"===t?e?void("function"==typeof d&&d("end")):"+":function(t){return ba[t]}(t)}function Oa(t,e){var d="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(d)return(d=d.call(t)).next.bind(d);if(Array.isArray(t)||(d=function(t,e){if(!t)return;if("string"==typeof t)return xa(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return xa(t,e)}(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function xa(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}function Ea(t,e){return Ca(t,void 0,e)}function Ca(t,e,d){var r=d.type(e),n=r&&r.possibleLengths()||d.possibleLengths();if(!n)return"IS_POSSIBLE";if("FIXED_LINE_OR_MOBILE"===e){if(!d.type("FIXED_LINE"))return Ca(t,"MOBILE",d);var a=d.type("MOBILE");a&&(n=function(t,e){for(var d,r=t.slice(),n=Oa(e);!(d=n()).done;){var a=d.value;t.indexOf(a)<0&&r.push(a)}return r.sort((function(t,e){return t-e}))}(n,a.possibleLengths()))}else if(e&&!r)return"INVALID_LENGTH";var o=t.length,i=n[0];return i===o?"IS_POSSIBLE":i>o?"TOO_SHORT":n[n.length-1]<o?"TOO_LONG":n.indexOf(o,1)>=0?"IS_POSSIBLE":"INVALID_LENGTH"}function Na(t,e){return"IS_POSSIBLE"===Ea(t,e)}function Pa(t,e){return t=t||"",new RegExp("^(?:"+e+")$").test(t)}function La(t,e){var d="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(d)return(d=d.call(t)).next.bind(d);if(Array.isArray(t)||(d=function(t,e){if(!t)return;if("string"==typeof t)return Ra(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return Ra(t,e)}(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ra(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}var Ta=["MOBILE","PREMIUM_RATE","TOLL_FREE","SHARED_COST","VOIP","PERSONAL_NUMBER","PAGER","UAN","VOICEMAIL"];function Da(t,e,d){if(e=e||{},t.country||t.countryCallingCode){(d=new aa(d)).selectNumberingPlan(t.country,t.countryCallingCode);var r=e.v2?t.nationalNumber:t.phone;if(Pa(r,d.nationalNumberPattern())){if(wa(r,"FIXED_LINE",d))return d.type("MOBILE")&&""===d.type("MOBILE").pattern()?"FIXED_LINE_OR_MOBILE":d.type("MOBILE")?wa(r,"MOBILE",d)?"FIXED_LINE_OR_MOBILE":"FIXED_LINE":"FIXED_LINE_OR_MOBILE";for(var n,a=La(Ta);!(n=a()).done;){var o=n.value;if(wa(r,o,d))return o}}}}function wa(t,e,d){return!(!(e=d.type(e))||!e.pattern())&&(!(e.possibleLengths()&&e.possibleLengths().indexOf(t.length)<0)&&Pa(t,e.pattern()))}function Ba(t,e,d){var r=new aa(d).getCountryCodesForCallingCode(t);return r?r.filter((function(t){return function(t,e,d){var r=new aa(d);if(r.selectNumberingPlan(e),r.numberingPlan.possibleLengths().indexOf(t.length)>=0)return!0;return!1}(e,t,d)})):[]}var Fa=/(\$\d)/;function Za(t,e,d){var r=d.useInternationalFormat,n=d.withNationalPrefix;d.carrierCode,d.metadata;var a=t.replace(new RegExp(e.pattern()),r?e.internationalFormat():n&&e.nationalPrefixFormattingRule()?e.format().replace(Fa,e.nationalPrefixFormattingRule()):e.format());return r?function(t){return t.replace(new RegExp("[".concat(Xn,"]+"),"g")," ").trim()}(a):a}var Ga=/^[\d]+(?:[~\u2053\u223C\uFF5E][\d]+)?$/;function Ua(t,e){var d="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(d)return(d=d.call(t)).next.bind(d);if(Array.isArray(t)||(d=function(t,e){if(!t)return;if("string"==typeof t)return ja(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return ja(t,e)}(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ja(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}function Ha(t,e){var d=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),d.push.apply(d,r)}return d}function Va(t){for(var e=1;e<arguments.length;e++){var d=null!=arguments[e]?arguments[e]:{};e%2?Ha(Object(d),!0).forEach((function(e){Ka(t,e,d[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(d)):Ha(Object(d)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(d,e))}))}return t}function Ka(t,e,d){return e in t?Object.defineProperty(t,e,{value:d,enumerable:!0,configurable:!0,writable:!0}):t[e]=d,t}var ka={formatExtension:function(t,e,d){return"".concat(t).concat(d.ext()).concat(e)}};function Wa(t,e,d,r){if(d=d?Va(Va({},ka),d):ka,r=new aa(r),t.country&&"001"!==t.country){if(!r.hasCountry(t.country))throw new Error("Unknown country: ".concat(t.country));r.country(t.country)}else{if(!t.countryCallingCode)return t.phone||"";r.selectNumberingPlan(t.countryCallingCode)}var n,a=r.countryCallingCode(),o=d.v2?t.nationalNumber:t.phone;switch(e){case"NATIONAL":return o?Xa(n=Ya(o,t.carrierCode,"NATIONAL",r,d),t.ext,r,d.formatExtension):"";case"INTERNATIONAL":return o?(n=Ya(o,null,"INTERNATIONAL",r,d),Xa(n="+".concat(a," ").concat(n),t.ext,r,d.formatExtension)):"+".concat(a);case"E.164":return"+".concat(a).concat(o);case"RFC3966":return function(t){var e=t.number,d=t.ext;if(!e)return"";if("+"!==e[0])throw new Error('"formatRFC3966()" expects "number" to be in E.164 format.');return"tel:".concat(e).concat(d?";ext="+d:"")}({number:"+".concat(a).concat(o),ext:t.ext});case"IDD":if(!d.fromCountry)return;var i=function(t,e,d,r,n){if(fa(r,n.metadata)===d){var a=Ya(t,e,"NATIONAL",n);return"1"===d?d+" "+a:a}var o=function(t,e,d){var r=new aa(d);return r.selectNumberingPlan(t,e),r.defaultIDDPrefix()?r.defaultIDDPrefix():Ga.test(r.IDDPrefix())?r.IDDPrefix():void 0}(r,void 0,n.metadata);if(o)return"".concat(o," ").concat(d," ").concat(Ya(t,null,"INTERNATIONAL",n))}(o,t.carrierCode,a,d.fromCountry,r);return Xa(i,t.ext,r,d.formatExtension);default:throw new Error('Unknown "format" argument passed to "formatNumber()": "'.concat(e,'"'))}}function Ya(t,e,d,r,n){var a=function(t,e){for(var d,r=Ua(t);!(d=r()).done;){var n=d.value;if(n.leadingDigitsPatterns().length>0){var a=n.leadingDigitsPatterns()[n.leadingDigitsPatterns().length-1];if(0!==e.search(a))continue}if(Pa(e,n.pattern()))return n}}(r.formats(),t);return a?Za(t,a,{useInternationalFormat:"INTERNATIONAL"===d,withNationalPrefix:!a.nationalPrefixIsOptionalWhenFormattingInNationalFormat()||!n||!1!==n.nationalPrefix,carrierCode:e,metadata:r}):t}function Xa(t,e,d,r){return e?r(t,e,d):t}function za(t,e){var d=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),d.push.apply(d,r)}return d}function Ja(t){for(var e=1;e<arguments.length;e++){var d=null!=arguments[e]?arguments[e]:{};e%2?za(Object(d),!0).forEach((function(e){qa(t,e,d[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(d)):za(Object(d)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(d,e))}))}return t}function qa(t,e,d){return e in t?Object.defineProperty(t,e,{value:d,enumerable:!0,configurable:!0,writable:!0}):t[e]=d,t}function Qa(t,e){for(var d=0;d<e.length;d++){var r=e[d];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var to=function(){function t(e,d,r){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e)throw new TypeError("`country` or `countryCallingCode` not passed");if(!d)throw new TypeError("`nationalNumber` not passed");if(!r)throw new TypeError("`metadata` not passed");var n=function(t,e){var d,r,n=new aa(e);a=t,/^[A-Z]{2}$/.test(a)?(d=t,n.selectNumberingPlan(d),r=n.countryCallingCode()):r=t;var a;return{country:d,countryCallingCode:r}}(e,r),a=n.country,o=n.countryCallingCode;this.country=a,this.countryCallingCode=o,this.nationalNumber=d,this.number="+"+this.countryCallingCode+this.nationalNumber,this.getMetadata=function(){return r}}var e,d,r;return e=t,d=[{key:"setExt",value:function(t){this.ext=t}},{key:"getPossibleCountries",value:function(){return this.country?[this.country]:Ba(this.countryCallingCode,this.nationalNumber,this.getMetadata())}},{key:"isPossible",value:function(){return function(t,e,d){if(void 0===e&&(e={}),d=new aa(d),e.v2){if(!t.countryCallingCode)throw new Error("Invalid phone number object passed");d.selectNumberingPlan(t.countryCallingCode)}else{if(!t.phone)return!1;if(t.country){if(!d.hasCountry(t.country))throw new Error("Unknown country: ".concat(t.country));d.country(t.country)}else{if(!t.countryCallingCode)throw new Error("Invalid phone number object passed");d.selectNumberingPlan(t.countryCallingCode)}}if(d.possibleLengths())return Na(t.phone||t.nationalNumber,d);if(t.countryCallingCode&&d.isNonGeographicCallingCode(t.countryCallingCode))return!0;throw new Error('Missing "possibleLengths" in metadata. Perhaps the metadata has been generated before v1.0.18.')}(this,{v2:!0},this.getMetadata())}},{key:"isValid",value:function(){return function(t,e,d){return e=e||{},(d=new aa(d)).selectNumberingPlan(t.country,t.countryCallingCode),d.hasTypes()?void 0!==Da(t,e,d.metadata):Pa(e.v2?t.nationalNumber:t.phone,d.nationalNumberPattern())}(this,{v2:!0},this.getMetadata())}},{key:"isNonGeographic",value:function(){return new aa(this.getMetadata()).isNonGeographicCallingCode(this.countryCallingCode)}},{key:"isEqual",value:function(t){return this.number===t.number&&this.ext===t.ext}},{key:"getType",value:function(){return Da(this,{v2:!0},this.getMetadata())}},{key:"format",value:function(t,e){return Wa(this,t,e?Ja(Ja({},e),{},{v2:!0}):{v2:!0},this.getMetadata())}},{key:"formatNational",value:function(t){return this.format("NATIONAL",t)}},{key:"formatInternational",value:function(t){return this.format("INTERNATIONAL",t)}},{key:"getURI",value:function(t){return this.format("RFC3966",t)}}],d&&Qa(e.prototype,d),r&&Qa(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();var eo=new RegExp("([0-9０-９٠-٩۰-۹])");function ro(t,e){var d=function(t,e){if(t&&e.numberingPlan.nationalPrefixForParsing()){var d=new RegExp("^(?:"+e.numberingPlan.nationalPrefixForParsing()+")"),r=d.exec(t);if(r){var n,a,o,i=r.length-1,u=i>0&&r[i];if(e.nationalPrefixTransformRule()&&u)n=t.replace(d,e.nationalPrefixTransformRule()),i>1&&(a=r[1]);else{var s=r[0];n=t.slice(s.length),u&&(a=r[1])}if(u){var l=t.indexOf(r[1]);t.slice(0,l)===e.numberingPlan.nationalPrefix()&&(o=e.numberingPlan.nationalPrefix())}else o=r[0];return{nationalNumber:n,nationalPrefix:o,carrierCode:a}}}return{nationalNumber:t}}(t,e),r=d.carrierCode,n=d.nationalNumber;if(n!==t){if(!function(t,e,d){if(Pa(t,d.nationalNumberPattern())&&!Pa(e,d.nationalNumberPattern()))return!1;return!0}(t,n,e))return{nationalNumber:t};if(e.possibleLengths()&&!function(t,e){switch(Ea(t,e)){case"TOO_SHORT":case"INVALID_LENGTH":return!1;default:return!0}}(n,e))return{nationalNumber:t}}return{nationalNumber:n,carrierCode:r}}function no(t,e,d,r){if(!t)return{};var n;if("+"!==t[0]){var a=function(t,e,d,r){if(e){var n=new aa(r);n.selectNumberingPlan(e,d);var a=new RegExp(n.IDDPrefix());if(0===t.search(a)){var o=(t=t.slice(t.match(a)[0].length)).match(eo);if(!(o&&null!=o[1]&&o[1].length>0&&"0"===o[1]))return t}}}(t,e,d,r);if(!a||a===t){if(e||d){var o=function(t,e,d,r){var n=e?fa(e,r):d;if(0===t.indexOf(n)){(r=new aa(r)).selectNumberingPlan(e,d);var a=t.slice(n.length),o=ro(a,r).nationalNumber,i=ro(t,r).nationalNumber;if(!Pa(i,r.nationalNumberPattern())&&Pa(o,r.nationalNumberPattern())||"TOO_LONG"===Ea(i,r))return{countryCallingCode:n,number:a}}return{number:t}}(t,e,d,r),i=o.countryCallingCode,u=o.number;if(i)return{countryCallingCodeSource:"FROM_NUMBER_WITHOUT_PLUS_SIGN",countryCallingCode:i,number:u}}return{number:t}}n=!0,t="+"+a}if("0"===t[1])return{};r=new aa(r);for(var s=2;s-1<=3&&s<=t.length;){var l=t.slice(1,s);if(r.hasCallingCode(l))return r.selectNumberingPlan(l),{countryCallingCodeSource:n?"FROM_NUMBER_WITH_IDD":"FROM_NUMBER_WITH_PLUS_SIGN",countryCallingCode:l,number:t.slice(s)};s++}return{}}function ao(t,e){var d="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(d)return(d=d.call(t)).next.bind(d);if(Array.isArray(t)||(d=function(t,e){if(!t)return;if("string"==typeof t)return oo(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return oo(t,e)}(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function oo(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}function io(t,e){var d=e.nationalNumber,r=e.defaultCountry,n=e.metadata;var a=n.getCountryCodesForCallingCode(t);if(a)return 1===a.length?a[0]:function(t,e){var d=e.countries,r=e.defaultCountry,n=e.metadata;n=new aa(n);for(var a,o=[],i=ao(d);!(a=i()).done;){var u=a.value;if(n.country(u),n.leadingDigits()){if(t&&0===t.search(n.leadingDigits()))return u}else if(Da({phone:t,country:u},void 0,n.metadata)){if(!r)return u;if(u===r)return u;o.push(u)}}if(o.length>0)return o[0]}(d,{countries:a,defaultCountry:r,metadata:n.metadata})}var uo=new RegExp("^\\+([0-9０-９٠-٩۰-۹]|[\\-\\.\\(\\)]?)*[0-9０-９٠-٩۰-۹]([0-9０-９٠-٩۰-۹]|[\\-\\.\\(\\)]?)*$","g"),so=new RegExp("^([0-9０-９٠-٩۰-۹]+((\\-)*[0-9０-９٠-٩۰-۹])*\\.)*[a-zA-Z]+((\\-)*[0-9０-９٠-٩۰-۹])*\\.?$","g"),lo="tel:",co=";phone-context=";function fo(t,e){var d,r=e.extractFormattedPhoneNumber,n=function(t){var e=t.indexOf(co);if(e<0)return null;var d=e+co.length;if(d>=t.length)return"";var r=t.indexOf(";",d);return r>=0?t.substring(d,r):t.substring(d)}(t);if(!function(t){return null===t||0!==t.length&&(uo.test(t)||so.test(t))}(n))throw new Wn("NOT_A_NUMBER");if(null===n)d=r(t)||"";else{d="","+"===n.charAt(0)&&(d+=n);var a,o=t.indexOf(lo);a=o>=0?o+lo.length:0;var i=t.indexOf(co);d+=t.substring(a,i)}var u=d.indexOf(";isub=");if(u>0&&(d=d.substring(0,u)),""!==d)return d}var $o=new RegExp("[+＋0-9０-９٠-٩۰-۹]"),po=new RegExp("[^0-9０-９٠-٩۰-۹#]+$");function vo(t,e,d){if(e=e||{},d=new aa(d),e.defaultCountry&&!d.hasCountry(e.defaultCountry)){if(e.v2)throw new Wn("INVALID_COUNTRY");throw new Error("Unknown country: ".concat(e.defaultCountry))}var r=function(t,e,d){var r=fo(t,{extractFormattedPhoneNumber:function(t){return function(t,e,d){if(!t)return;if(t.length>250){if(d)throw new Wn("TOO_LONG");return}if(!1===e)return t;var r=t.search($o);if(r<0)return;return t.slice(r).replace(po,"")}(t,d,e)}});if(!r)return{};if(!function(t){return t.length>=2&&ha.test(t)}(r))return function(t){return ma.test(t)}(r)?{error:"TOO_SHORT"}:{};var n=function(t){var e=t.search(Aa);if(e<0)return{};for(var d=t.slice(0,e),r=t.match(Aa),n=1;n<r.length;){if(r[n])return{number:d,ext:r[n]};n++}}(r);if(n.ext)return n;return{number:r}}(t,e.v2,e.extract),n=r.number,a=r.ext,o=r.error;if(!n){if(e.v2){if("TOO_SHORT"===o)throw new Wn("TOO_SHORT");throw new Wn("NOT_A_NUMBER")}return{}}var i=function(t,e,d,r){var n,a=no(Sa(t),e,d,r.metadata),o=a.countryCallingCodeSource,i=a.countryCallingCode,u=a.number;if(i)r.selectNumberingPlan(i);else{if(!u||!e&&!d)return{};r.selectNumberingPlan(e,d),e&&(n=e),i=d||fa(e,r.metadata)}if(!u)return{countryCallingCodeSource:o,countryCallingCode:i};var s=ro(Sa(u),r),l=s.nationalNumber,c=s.carrierCode,f=io(i,{nationalNumber:l,defaultCountry:e,metadata:r});f&&(n=f,"001"===f||r.country(n));return{country:n,countryCallingCode:i,countryCallingCodeSource:o,nationalNumber:l,carrierCode:c}}(n,e.defaultCountry,e.defaultCallingCode,d),u=i.country,s=i.nationalNumber,l=i.countryCallingCode,c=i.countryCallingCodeSource,f=i.carrierCode;if(!d.hasSelectedNumberingPlan()){if(e.v2)throw new Wn("INVALID_COUNTRY");return{}}if(!s||s.length<2){if(e.v2)throw new Wn("TOO_SHORT");return{}}if(s.length>17){if(e.v2)throw new Wn("TOO_LONG");return{}}if(e.v2){var $=new to(l,s,d.metadata);return u&&($.country=u),f&&($.carrierCode=f),a&&($.ext=a),$.__countryCallingCodeSource=c,$}var p=!!(e.extended?d.hasSelectedNumberingPlan():u)&&Pa(s,d.nationalNumberPattern());return e.extended?{country:u,countryCallingCode:l,carrierCode:f,valid:p,possible:!!p||!(!0!==e.extended||!d.possibleLengths()||!Na(s,d)),phone:s,ext:a}:p?function(t,e,d){var r={country:t,phone:e};d&&(r.ext=d);return r}(u,s,a):{}}function yo(t,e){var d=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),d.push.apply(d,r)}return d}function mo(t){for(var e=1;e<arguments.length;e++){var d=null!=arguments[e]?arguments[e]:{};e%2?yo(Object(d),!0).forEach((function(e){go(t,e,d[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(d)):yo(Object(d)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(d,e))}))}return t}function go(t,e,d){return e in t?Object.defineProperty(t,e,{value:d,enumerable:!0,configurable:!0,writable:!0}):t[e]=d,t}function ho(t,e,d){return vo(t,mo(mo({},e),{},{v2:!0}),d)}function Ao(t,e){var d=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),d.push.apply(d,r)}return d}function bo(t,e,d){return e in t?Object.defineProperty(t,e,{value:d,enumerable:!0,configurable:!0,writable:!0}):t[e]=d,t}function Io(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var d=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==d)return;var r,n,a=[],o=!0,i=!1;try{for(d=d.call(t);!(o=(r=d.next()).done)&&(a.push(r.value),!e||a.length!==e);o=!0);}catch(t){i=!0,n=t}finally{try{o||null==d.return||d.return()}finally{if(i)throw n}}return a}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Mo(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return Mo(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mo(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}function So(t){var e,d,r,n=Io(Array.prototype.slice.call(t),4),a=n[0],o=n[1],i=n[2],u=n[3];if("string"!=typeof a)throw new TypeError("A text for parsing must be a string.");if(e=a,o&&"string"!=typeof o){if(!qn(o))throw new Error("Invalid second argument: ".concat(o));i?(d=o,r=i):r=o}else u?(d=i,r=u):(d=void 0,r=i),o&&(d=function(t){for(var e=1;e<arguments.length;e++){var d=null!=arguments[e]?arguments[e]:{};e%2?Ao(Object(d),!0).forEach((function(e){bo(t,e,d[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(d)):Ao(Object(d)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(d,e))}))}return t}({defaultCountry:o},d));return{text:e,options:d,metadata:r}}function _o(){var t=So(arguments),e=t.text,d=t.options,r=t.metadata;return ho(e,d,r)}var Oo="isPhoneNumber";function xo(t,e){if("string"!=typeof t||t.trim()!==t)return!1;try{var d=function(){return Bn(_o,arguments)}(t,e);return(!e||d.country===e)&&d.isValid()}catch(t){return!1}}var Eo="isMilitaryTime";function Co(t){return"string"==typeof t&&Rn(t,/^([01]\d|2[0-3]):?([0-5]\d)$/)}var No={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),new RegExp("^[a-fA-F0-9]{".concat(r[e],"}$")).test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};t.exports=e.default,t.exports.default=e.default}(No,No.exports);var Po=No.exports,Lo=N(Po),Ro="isHash";function To(t,e){return"string"==typeof t&&Lo(t,e)}var Do={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,d.default)(t);var n=r;if(n=e.require_hyphen?n.replace("?",""):n,!(n=e.case_sensitive?new RegExp(n):new RegExp(n,"i")).test(t))return!1;for(var a=t.replace("-","").toUpperCase(),o=0,i=0;i<a.length;i++){var u=a[i];o+=("X"===u?10:+u)*(8-i)}return o%11==0};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r="^\\d{4}-?\\d{3}[\\dX]$";t.exports=e.default,t.exports.default=e.default}(Do,Do.exports);var wo=Do.exports,Bo=N(wo),Fo="isISSN";function Zo(t,e){return"string"==typeof t&&Bo(t,e)}var Go="isDateString";function Uo(t,e){return ur(t,e)}var jo={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r;if((0,d.default)(t),e.loose)return a.includes(t.toLowerCase());return n.includes(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r={loose:!1},n=["true","false","1","0"],a=[].concat(n,["yes","no"]);t.exports=e.default,t.exports.default=e.default}(jo,jo.exports);var Ho=jo.exports,Vo=N(Ho),Ko="isBooleanString";function ko(t){return"string"==typeof t&&Vo(t)}var Wo={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e&&e.no_symbols)return n.test(t);return new RegExp("^[+-]?([0-9]*[".concat((e||{}).locale?r.decimal[e.locale]:".","])?[0-9]+$")).test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R),r=at;var n=/^[0-9]+$/;t.exports=e.default,t.exports.default=e.default}(Wo,Wo.exports);var Yo=Wo.exports,Xo=N(Yo),zo="isNumberString";function Jo(t,e){return"string"==typeof t&&Xo(t,e)}var qo={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),(e=(0,r.default)(e,i)).crockford)return o.test(t);if(t.length%8==0&&a.test(t))return!0;return!1};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}var a=/^[A-Z2-7]+=*$/,o=/^[A-HJKMNP-TV-Z0-9]+$/,i={crockford:!1};t.exports=e.default,t.exports.default=e.default}(qo,qo.exports);var Qo=qo.exports,ti=N(Qo),ei="isBase32";function di(t){return"string"==typeof t&&ti(t)}var ri={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);var e=t.slice(4,6).toUpperCase();if(!r.CountryCodes.has(e)&&"XK"!==e)return!1;return n.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R),r=Pr;var n=/^[A-Za-z]{6}[A-Za-z0-9]{2}([A-Za-z0-9]{3})?$/;t.exports=e.default,t.exports.default=e.default}(ri,ri.exports);var ni=ri.exports,ai=N(ni),oi="isBIC";function ii(t){return"string"==typeof t&&ai(t)}var ui={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)||n.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(bc1)[a-z0-9]{25,39}$/,n=/^(1|3)[A-HJ-NP-Za-km-z1-9]{25,39}$/;t.exports=e.default,t.exports.default=e.default}(ui,ui.exports);var si=ui.exports,li=N(si),ci="isBtcAddress";function fi(t){return"string"==typeof t&&li(t)}var $i={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);var e=t.split(",");if(e.length<2)return!1;var o=e.shift().trim().split(";"),i=o.shift();if("data:"!==i.slice(0,5))return!1;var u=i.slice(5);if(""!==u&&!r.test(u))return!1;for(var s=0;s<o.length;s++)if((s!==o.length-1||"base64"!==o[s].toLowerCase())&&!n.test(o[s]))return!1;for(var l=0;l<e.length;l++)if(!a.test(e[l]))return!1;return!0};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[a-z]+\/[a-z0-9\-\+\._]+$/i,n=/^[a-z\-]+=[a-z0-9\-]+$/i,a=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;t.exports=e.default,t.exports.default=e.default}($i,$i.exports);var pi=$i.exports,vi=N(pi),yi="isDataURI";function mi(t){return"string"==typeof t&&vi(t)}var gi={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);var e=Number(t.slice(-1));return r.test(t)&&e===(n=t,a=10-n.slice(0,-1).split("").map((function(t,e){return Number(t)*function(t,e){return 8===t||14===t?e%2==0?3:1:e%2==0?1:3}(n.length,e)})).reduce((function(t,e){return t+e}),0)%10,a<10?a:0);var n,a};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(\d{8}|\d{13}|\d{14})$/;t.exports=e.default,t.exports.default=e.default}(gi,gi.exports);var hi=gi.exports,Ai=N(hi),bi="isEAN";function Ii(t){return"string"==typeof t&&Ai(t)}var Mi={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(0x)[0-9a-f]{40}$/i;t.exports=e.default,t.exports.default=e.default}(Mi,Mi.exports);var Si=Mi.exports,_i=N(Si),Oi="isEthereumAddress";function xi(t){return"string"==typeof t&&_i(t)}var Ei={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){(0,d.default)(t);var e=t.replace(/\s+/g," ").replace(/\s?(hsla?\(|\)|,)\s?/gi,"$1");if(-1!==e.indexOf(","))return r.test(e);return n.test(e)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(,(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}(,((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?))?\)$/i,n=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(\s(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s?(\/\s((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s?)?\)$/i;t.exports=e.default,t.exports.default=e.default}(Ei,Ei.exports);var Ci=Ei.exports,Ni=N(Ci),Pi="isHSL";function Li(t){return"string"==typeof t&&Ni(t)}var Ri={};Object.defineProperty(Ri,"__esModule",{value:!0});var Ti=Ri.default=function(t){return(0,Di.default)(t),function(t){var e=t.replace(/[\s\-]+/gi,"").toUpperCase(),d=e.slice(0,2).toUpperCase();return d in wi&&wi[d].test(e)}(t)&&function(t){var e=t.replace(/[^A-Z0-9]+/gi,"").toUpperCase();return 1===(e.slice(4)+e.slice(0,4)).replace(/[A-Z]/g,(function(t){return t.charCodeAt(0)-55})).match(/\d{1,7}/g).reduce((function(t,e){return Number(t+e)%97}),"")}(t)};Ri.locales=void 0;var Di=function(t){return t&&t.__esModule?t:{default:t}}(R);var wi={AD:/^(AD[0-9]{2})\d{8}[A-Z0-9]{12}$/,AE:/^(AE[0-9]{2})\d{3}\d{16}$/,AL:/^(AL[0-9]{2})\d{8}[A-Z0-9]{16}$/,AT:/^(AT[0-9]{2})\d{16}$/,AZ:/^(AZ[0-9]{2})[A-Z0-9]{4}\d{20}$/,BA:/^(BA[0-9]{2})\d{16}$/,BE:/^(BE[0-9]{2})\d{12}$/,BG:/^(BG[0-9]{2})[A-Z]{4}\d{6}[A-Z0-9]{8}$/,BH:/^(BH[0-9]{2})[A-Z]{4}[A-Z0-9]{14}$/,BR:/^(BR[0-9]{2})\d{23}[A-Z]{1}[A-Z0-9]{1}$/,BY:/^(BY[0-9]{2})[A-Z0-9]{4}\d{20}$/,CH:/^(CH[0-9]{2})\d{5}[A-Z0-9]{12}$/,CR:/^(CR[0-9]{2})\d{18}$/,CY:/^(CY[0-9]{2})\d{8}[A-Z0-9]{16}$/,CZ:/^(CZ[0-9]{2})\d{20}$/,DE:/^(DE[0-9]{2})\d{18}$/,DK:/^(DK[0-9]{2})\d{14}$/,DO:/^(DO[0-9]{2})[A-Z]{4}\d{20}$/,EE:/^(EE[0-9]{2})\d{16}$/,EG:/^(EG[0-9]{2})\d{25}$/,ES:/^(ES[0-9]{2})\d{20}$/,FI:/^(FI[0-9]{2})\d{14}$/,FO:/^(FO[0-9]{2})\d{14}$/,FR:/^(FR[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,GB:/^(GB[0-9]{2})[A-Z]{4}\d{14}$/,GE:/^(GE[0-9]{2})[A-Z0-9]{2}\d{16}$/,GI:/^(GI[0-9]{2})[A-Z]{4}[A-Z0-9]{15}$/,GL:/^(GL[0-9]{2})\d{14}$/,GR:/^(GR[0-9]{2})\d{7}[A-Z0-9]{16}$/,GT:/^(GT[0-9]{2})[A-Z0-9]{4}[A-Z0-9]{20}$/,HR:/^(HR[0-9]{2})\d{17}$/,HU:/^(HU[0-9]{2})\d{24}$/,IE:/^(IE[0-9]{2})[A-Z0-9]{4}\d{14}$/,IL:/^(IL[0-9]{2})\d{19}$/,IQ:/^(IQ[0-9]{2})[A-Z]{4}\d{15}$/,IR:/^(IR[0-9]{2})0\d{2}0\d{18}$/,IS:/^(IS[0-9]{2})\d{22}$/,IT:/^(IT[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,JO:/^(JO[0-9]{2})[A-Z]{4}\d{22}$/,KW:/^(KW[0-9]{2})[A-Z]{4}[A-Z0-9]{22}$/,KZ:/^(KZ[0-9]{2})\d{3}[A-Z0-9]{13}$/,LB:/^(LB[0-9]{2})\d{4}[A-Z0-9]{20}$/,LC:/^(LC[0-9]{2})[A-Z]{4}[A-Z0-9]{24}$/,LI:/^(LI[0-9]{2})\d{5}[A-Z0-9]{12}$/,LT:/^(LT[0-9]{2})\d{16}$/,LU:/^(LU[0-9]{2})\d{3}[A-Z0-9]{13}$/,LV:/^(LV[0-9]{2})[A-Z]{4}[A-Z0-9]{13}$/,MC:/^(MC[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,MD:/^(MD[0-9]{2})[A-Z0-9]{20}$/,ME:/^(ME[0-9]{2})\d{18}$/,MK:/^(MK[0-9]{2})\d{3}[A-Z0-9]{10}\d{2}$/,MR:/^(MR[0-9]{2})\d{23}$/,MT:/^(MT[0-9]{2})[A-Z]{4}\d{5}[A-Z0-9]{18}$/,MU:/^(MU[0-9]{2})[A-Z]{4}\d{19}[A-Z]{3}$/,MZ:/^(MZ[0-9]{2})\d{21}$/,NL:/^(NL[0-9]{2})[A-Z]{4}\d{10}$/,NO:/^(NO[0-9]{2})\d{11}$/,PK:/^(PK[0-9]{2})[A-Z0-9]{4}\d{16}$/,PL:/^(PL[0-9]{2})\d{24}$/,PS:/^(PS[0-9]{2})[A-Z0-9]{4}\d{21}$/,PT:/^(PT[0-9]{2})\d{21}$/,QA:/^(QA[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,RO:/^(RO[0-9]{2})[A-Z]{4}[A-Z0-9]{16}$/,RS:/^(RS[0-9]{2})\d{18}$/,SA:/^(SA[0-9]{2})\d{2}[A-Z0-9]{18}$/,SC:/^(SC[0-9]{2})[A-Z]{4}\d{20}[A-Z]{3}$/,SE:/^(SE[0-9]{2})\d{20}$/,SI:/^(SI[0-9]{2})\d{15}$/,SK:/^(SK[0-9]{2})\d{20}$/,SM:/^(SM[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,SV:/^(SV[0-9]{2})[A-Z0-9]{4}\d{20}$/,TL:/^(TL[0-9]{2})\d{19}$/,TN:/^(TN[0-9]{2})\d{20}$/,TR:/^(TR[0-9]{2})\d{5}[A-Z0-9]{17}$/,UA:/^(UA[0-9]{2})\d{6}[A-Z0-9]{19}$/,VA:/^(VA[0-9]{2})\d{18}$/,VG:/^(VG[0-9]{2})[A-Z0-9]{4}\d{16}$/,XK:/^(XK[0-9]{2})\d{16}$/};var Bi=Object.keys(wi);Ri.locales=Bi;var Fi="isIBAN";function Zi(t){return"string"==typeof t&&Ti(t)}var Gi={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e in a)return a[e](t);if("any"===e){for(var r in a){if(a.hasOwnProperty(r))if((0,a[r])(t))return!0}return!1}throw new Error("Invalid locale '".concat(e,"'"))};var d=n(R),r=n(Hd);function n(t){return t&&t.__esModule?t:{default:t}}var a={PL:function(t){(0,d.default)(t);var e={1:1,2:3,3:7,4:9,5:1,6:3,7:7,8:9,9:1,10:3,11:0};if(null!=t&&11===t.length&&(0,r.default)(t,{allow_leading_zeroes:!0})){var n=t.split("").slice(0,-1).reduce((function(t,d,r){return t+Number(d)*e[r+1]}),0)%10,a=Number(t.charAt(t.length-1));if(0===n&&0===a||a===10-n)return!0}return!1},ES:function(t){(0,d.default)(t);var e={X:0,Y:1,Z:2},r=t.trim().toUpperCase();if(!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(r))return!1;var n=r.slice(0,-1).replace(/[X,Y,Z]/g,(function(t){return e[t]}));return r.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][n%23])},FI:function(t){if((0,d.default)(t),11!==t.length)return!1;if(!t.match(/^\d{6}[\-A\+]\d{3}[0-9ABCDEFHJKLMNPRSTUVWXY]{1}$/))return!1;return"0123456789ABCDEFHJKLMNPRSTUVWXY"[(1e3*parseInt(t.slice(0,6),10)+parseInt(t.slice(7,10),10))%31]===t.slice(10,11)},IN:function(t){var e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],d=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=t.trim();if(!/^[1-9]\d{3}\s?\d{4}\s?\d{4}$/.test(r))return!1;var n=0;return r.replace(/\s/g,"").split("").map(Number).reverse().forEach((function(t,r){n=e[n][d[r%8][t]]})),0===n},IR:function(t){if(!t.match(/^\d{10}$/))return!1;if(t="0000".concat(t).slice(t.length-6),0===parseInt(t.slice(3,9),10))return!1;for(var e=parseInt(t.slice(9,10),10),d=0,r=0;r<9;r++)d+=parseInt(t.slice(r,r+1),10)*(10-r);return(d%=11)<2&&e===d||d>=2&&e===11-d},IT:function(t){return 9===t.length&&("CA00000AA"!==t&&t.search(/C[A-Z][0-9]{5}[A-Z]{2}/i)>-1)},NO:function(t){var e=t.trim();if(isNaN(Number(e)))return!1;if(11!==e.length)return!1;if("00000000000"===e)return!1;var d=e.split("").map(Number),r=(11-(3*d[0]+7*d[1]+6*d[2]+1*d[3]+8*d[4]+9*d[5]+4*d[6]+5*d[7]+2*d[8])%11)%11,n=(11-(5*d[0]+4*d[1]+3*d[2]+2*d[3]+7*d[4]+6*d[5]+5*d[6]+4*d[7]+3*d[8]+2*r)%11)%11;return r===d[9]&&n===d[10]},TH:function(t){if(!t.match(/^[1-8]\d{12}$/))return!1;for(var e=0,d=0;d<12;d++)e+=parseInt(t[d],10)*(13-d);return t[12]===((11-e%11)%10).toString()},LK:function(t){return!(10!==t.length||!/^[1-9]\d{8}[vx]$/i.test(t))||!(12!==t.length||!/^[1-9]\d{11}$/i.test(t))},"he-IL":function(t){var e=t.trim();if(!/^\d{9}$/.test(e))return!1;for(var d,r=e,n=0,a=0;a<r.length;a++)n+=(d=Number(r[a])*(a%2+1))>9?d-9:d;return n%10==0},"ar-LY":function(t){var e=t.trim();return!!/^(1|2)\d{11}$/.test(e)},"ar-TN":function(t){var e=t.trim();return!!/^\d{8}$/.test(e)},"zh-CN":function(t){var e,d=["11","12","13","14","15","21","22","23","31","32","33","34","35","36","37","41","42","43","44","45","46","50","51","52","53","54","61","62","63","64","65","71","81","82","91"],r=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"],n=["1","0","X","9","8","7","6","5","4","3","2"],a=function(t){return d.includes(t)},o=function(t){var e=parseInt(t.substring(0,4),10),d=parseInt(t.substring(4,6),10),r=parseInt(t.substring(6),10),n=new Date(e,d-1,r);return!(n>new Date)&&(n.getFullYear()===e&&n.getMonth()===d-1&&n.getDate()===r)},i=function(t){return function(t){for(var e=t.substring(0,17),d=0,a=0;a<17;a++)d+=parseInt(e.charAt(a),10)*parseInt(r[a],10);return n[d%11]}(t)===t.charAt(17).toUpperCase()};return!!/^\d{15}|(\d{17}(\d|x|X))$/.test(e=t)&&(15===e.length?function(t){var e=/^[1-9]\d{7}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}$/.test(t);if(!e)return!1;var d=t.substring(0,2);if(!(e=a(d)))return!1;var r="19".concat(t.substring(6,12));return!!(e=o(r))}(e):function(t){var e=/^[1-9]\d{5}[1-9]\d{3}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}(\d|x|X)$/.test(t);if(!e)return!1;var d=t.substring(0,2);if(!(e=a(d)))return!1;var r=t.substring(6,14);return!!(e=o(r))&&i(t)}(e))},"zh-HK":function(t){var e=/^[0-9]$/;if(t=(t=t.trim()).toUpperCase(),!/^[A-Z]{1,2}[0-9]{6}((\([0-9A]\))|(\[[0-9A]\])|([0-9A]))$/.test(t))return!1;8===(t=t.replace(/\[|\]|\(|\)/g,"")).length&&(t="3".concat(t));for(var d=0,r=0;r<=7;r++){d+=(e.test(t[r])?t[r]:(t[r].charCodeAt(0)-55)%11)*(9-r)}return(0===(d%=11)?"0":1===d?"A":String(11-d))===t[t.length-1]},"zh-TW":function(t){var e={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},d=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(d)&&Array.from(d).reduce((function(t,d,r){if(0===r){var n=e[d];return n%10*9+Math.floor(n/10)}return 9===r?(10-t%10-Number(d))%10==0:t+Number(d)*(9-r)}),0)}};t.exports=e.default,t.exports.default=e.default}(Gi,Gi.exports);var Ui=Gi.exports,ji=N(Ui),Hi="isIdentityCard";function Vi(t,e){return"string"==typeof t&&ji(t,e)}var Ki={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;t.exports=e.default,t.exports.default=e.default}(Ki,Ki.exports);var ki=Ki.exports,Wi=N(ki),Yi="isISRC";function Xi(t){return"string"==typeof t&&Wi(t)}var zi={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if((0,d.default)(t),"en_US_POSIX"===t||"ca_ES_VALENCIA"===t)return!0;return r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[A-Za-z]{2,4}([_-]([A-Za-z]{4}|[\d]{3}))?([_-]([A-Za-z]{2}|[\d]{3}))?$/;t.exports=e.default,t.exports.default=e.default}(zi,zi.exports);var Ji=zi.exports,qi=N(Ji),Qi="isLocale";function tu(t){return"string"==typeof t&&qi(t)}var eu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if((0,d.default)(t),0!==t.indexOf("magnet:?"))return!1;return r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/(?:^magnet:\?|[^?&]&)xt(?:\.1)?=urn:(?:(?:aich|bitprint|btih|ed2k|ed2khash|kzhash|md5|sha1|tree:tiger):[a-z0-9]{32}(?:[a-z0-9]{8})?|btmh:1220[a-z0-9]{64})(?:$|&)/i;t.exports=e.default,t.exports.default=e.default}(eu,eu.exports);var du=eu.exports,ru=N(du),nu="isMagnetURI";function au(t){return"string"==typeof t&&ru(t)}var ou={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)||n.test(t)||a.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+_]{1,100}$/i,n=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,a=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;t.exports=e.default,t.exports.default=e.default}(ou,ou.exports);var iu=ou.exports,uu=N(iu),su="isMimeType";function lu(t){return"string"==typeof t&&uu(t)}var cu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^(0o)?[0-7]+$/i;t.exports=e.default,t.exports.default=e.default}(cu,cu.exports);var fu=cu.exports,$u=N(fu),pu="isOctal";function vu(t){return"string"==typeof t&&$u(t)}var yu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var n=t.replace(/\s/g,"").toUpperCase();return e.toUpperCase()in r&&r[e].test(n)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r={AM:/^[A-Z]{2}\d{7}$/,AR:/^[A-Z]{3}\d{6}$/,AT:/^[A-Z]\d{7}$/,AU:/^[A-Z]\d{7}$/,AZ:/^[A-Z]{2,3}\d{7,8}$/,BE:/^[A-Z]{2}\d{6}$/,BG:/^\d{9}$/,BR:/^[A-Z]{2}\d{6}$/,BY:/^[A-Z]{2}\d{7}$/,CA:/^[A-Z]{2}\d{6}$/,CH:/^[A-Z]\d{7}$/,CN:/^G\d{8}$|^E(?![IO])[A-Z0-9]\d{7}$/,CY:/^[A-Z](\d{6}|\d{8})$/,CZ:/^\d{8}$/,DE:/^[CFGHJKLMNPRTVWXYZ0-9]{9}$/,DK:/^\d{9}$/,DZ:/^\d{9}$/,EE:/^([A-Z]\d{7}|[A-Z]{2}\d{7})$/,ES:/^[A-Z0-9]{2}([A-Z0-9]?)\d{6}$/,FI:/^[A-Z]{2}\d{7}$/,FR:/^\d{2}[A-Z]{2}\d{5}$/,GB:/^\d{9}$/,GR:/^[A-Z]{2}\d{7}$/,HR:/^\d{9}$/,HU:/^[A-Z]{2}(\d{6}|\d{7})$/,IE:/^[A-Z0-9]{2}\d{7}$/,IN:/^[A-Z]{1}-?\d{7}$/,ID:/^[A-C]\d{7}$/,IR:/^[A-Z]\d{8}$/,IS:/^(A)\d{7}$/,IT:/^[A-Z0-9]{2}\d{7}$/,JM:/^[Aa]\d{7}$/,JP:/^[A-Z]{2}\d{7}$/,KR:/^[MS]\d{8}$/,KZ:/^[a-zA-Z]\d{7}$/,LI:/^[a-zA-Z]\d{5}$/,LT:/^[A-Z0-9]{8}$/,LU:/^[A-Z0-9]{8}$/,LV:/^[A-Z0-9]{2}\d{7}$/,LY:/^[A-Z0-9]{8}$/,MT:/^\d{7}$/,MZ:/^([A-Z]{2}\d{7})|(\d{2}[A-Z]{2}\d{5})$/,MY:/^[AHK]\d{8}$/,MX:/^\d{10,11}$/,NL:/^[A-Z]{2}[A-Z0-9]{6}\d$/,NZ:/^([Ll]([Aa]|[Dd]|[Ff]|[Hh])|[Ee]([Aa]|[Pp])|[Nn])\d{6}$/,PH:/^([A-Z](\d{6}|\d{7}[A-Z]))|([A-Z]{2}(\d{6}|\d{7}))$/,PK:/^[A-Z]{2}\d{7}$/,PL:/^[A-Z]{2}\d{7}$/,PT:/^[A-Z]\d{6}$/,RO:/^\d{8,9}$/,RU:/^\d{9}$/,SE:/^\d{8}$/,SL:/^(P)[A-Z]\d{7}$/,SK:/^[0-9A-Z]\d{7}$/,TH:/^[A-Z]{1,2}\d{6,7}$/,TR:/^[A-Z]\d{8}$/,UA:/^[A-Z]{2}\d{6}$/,US:/^\d{9}$/};t.exports=e.default,t.exports.default=e.default}(yu,yu.exports);var mu=yu.exports,gu=N(mu),hu="isPassportNumber";function Au(t,e){return"string"==typeof t&&gu(t,e)}var bu={};Object.defineProperty(bu,"__esModule",{value:!0});var Iu=bu.default=function(t,e){if((0,Mu.default)(t),e in Eu)return Eu[e].test(t);if("any"===e){for(var d in Eu){if(Eu.hasOwnProperty(d))if(Eu[d].test(t))return!0}return!1}throw new Error("Invalid locale '".concat(e,"'"))};bu.locales=void 0;var Mu=function(t){return t&&t.__esModule?t:{default:t}}(R);var Su=/^\d{3}$/,_u=/^\d{4}$/,Ou=/^\d{5}$/,xu=/^\d{6}$/,Eu={AD:/^AD\d{3}$/,AT:_u,AU:_u,AZ:/^AZ\d{4}$/,BA:/^([7-8]\d{4}$)/,BE:_u,BG:_u,BR:/^\d{5}-\d{3}$/,BY:/^2[1-4]\d{4}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:_u,CN:/^(0[1-7]|1[012356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[1-5]|8[1345]|9[09])\d{4}$/,CZ:/^\d{3}\s?\d{2}$/,DE:Ou,DK:_u,DO:Ou,DZ:Ou,EE:Ou,ES:/^(5[0-2]{1}|[0-4]{1}\d{1})\d{3}$/,FI:Ou,FR:/^\d{2}\s?\d{3}$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HT:/^HT\d{4}$/,HU:_u,ID:Ou,IE:/^(?!.*(?:o))[A-Za-z]\d[\dw]\s\w{4}$/i,IL:/^(\d{5}|\d{7})$/,IN:/^((?!10|29|35|54|55|65|66|86|87|88|89)[1-9][0-9]{5})$/,IR:/^(?!(\d)\1{3})[13-9]{4}[1346-9][013-9]{5}$/,IS:Su,IT:Ou,JP:/^\d{3}\-\d{4}$/,KE:Ou,KR:/^(\d{5}|\d{6})$/,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:_u,LV:/^LV\-\d{4}$/,LK:Ou,MG:Su,MX:Ou,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,MY:Ou,NL:/^\d{4}\s?[a-z]{2}$/i,NO:_u,NP:/^(10|21|22|32|33|34|44|45|56|57)\d{3}$|^(977)$/i,NZ:_u,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:xu,RU:xu,SA:Ou,SE:/^[1-9]\d{2}\s?\d{2}$/,SG:xu,SI:_u,SK:/^\d{3}\s?\d{2}$/,TH:Ou,TN:_u,TW:/^\d{3}(\d{2})?$/,UA:Ou,US:/^\d{5}(-\d{4})?$/,ZA:_u,ZM:Ou},Cu=Object.keys(Eu);bu.locales=Cu;var Nu="isPostalCode";function Pu(t,e){return"string"==typeof t&&Iu(t,e)}var Lu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),l.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/([01][0-9]|2[0-3])/,n=/[0-5][0-9]/,a=new RegExp("[-+]".concat(r.source,":").concat(n.source)),o=new RegExp("([zZ]|".concat(a.source,")")),i=new RegExp("".concat(r.source,":").concat(n.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),u=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),s=new RegExp("".concat(i.source).concat(o.source)),l=new RegExp("^".concat(u.source,"[ tT]").concat(s.source,"$"));t.exports=e.default,t.exports.default=e.default}(Lu,Lu.exports);var Ru=Lu.exports,Tu=N(Ru),Du="isRFC3339";function wu(t){return"string"==typeof t&&Tu(t)}var Bu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if((0,d.default)(t),!e)return r.test(t)||n.test(t);return r.test(t)||n.test(t)||a.test(t)||o.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^rgb\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\)$/,n=/^rgba\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/,a=/^rgb\((([0-9]%|[1-9][0-9]%|100%),){2}([0-9]%|[1-9][0-9]%|100%)\)$/,o=/^rgba\((([0-9]%|[1-9][0-9]%|100%),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/;t.exports=e.default,t.exports.default=e.default}(Bu,Bu.exports);var Fu=Bu.exports,Zu=N(Fu),Gu="isRgbColor";function Uu(t,e){return"string"==typeof t&&Zu(t,e)}var ju={exports:{}},Hu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var d=t.join("");return new RegExp(d,e)},t.exports=e.default,t.exports.default=e.default}(Hu,Hu.exports);var Vu=Hu.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),n.test(t)};var d=r(R);function r(t){return t&&t.__esModule?t:{default:t}}var n=(0,r(Vu).default)(["^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)","(?:-((?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*))*))","?(?:\\+([0-9a-z-]+(?:\\.[0-9a-z-]+)*))?$"],"i");t.exports=e.default,t.exports.default=e.default}(ju,ju.exports);var Ku=ju.exports,ku=N(Ku),Wu="isSemVer";function Yu(t){return"string"==typeof t&&ku(t)}var Xu={exports:{}},zu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),t=Date.parse(t),isNaN(t)?null:new Date(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(zu,zu.exports);var Ju=zu.exports,qu={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),parseInt(t,e||10)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(qu,qu.exports);var Qu=qu.exports,ts={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e)return"1"===t||/^true$/i.test(t);return"0"!==t&&!/^false$/i.test(t)&&""!==t};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(ts,ts.exports);var es=ts.exports,ds={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),t===e};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(ds,ds.exports);var rs=ds.exports,ns={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";(0,d.default)(t);var n=t.split("/");if(2!==n.length)return!1;if(!a.test(n[1]))return!1;if(n[1].length>1&&n[1].startsWith("0"))return!1;var i=(0,r.default)(n[0],e);if(!i)return!1;var u=null;switch(String(e)){case"4":u=32;break;case"6":u=o;break;default:u=(0,r.default)(n[0],"6")?o:32}return n[1]<=u&&n[1]>=0};var d=n(R),r=n(qe);function n(t){return t&&t.__esModule?t:{default:t}}var a=/^\d{1,3}$/,o=128;t.exports=e.default,t.exports.default=e.default}(ns,ns.exports);var as=ns.exports,os={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){e="string"==typeof e?(0,d.default)({format:e},o):(0,d.default)(e,o);if("string"==typeof t&&(v=e.format,/(^(y{4}|y{2})[.\/-](m{1,2})[.\/-](d{1,2})$)|(^(m{1,2})[.\/-](d{1,2})[.\/-]((y{4}|y{2})$))|(^(d{1,2})[.\/-](m{1,2})[.\/-]((y{4}|y{2})$))/gi.test(v))){var a,i=e.delimiters.find((function(t){return-1!==e.format.indexOf(t)})),u=e.strictMode?i:e.delimiters.find((function(e){return-1!==t.indexOf(e)})),s=function(t,e){for(var d=[],r=Math.min(t.length,e.length),n=0;n<r;n++)d.push([t[n],e[n]]);return d}(t.split(u),e.format.toLowerCase().split(i)),l={},c=function(t,e){var d;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(d=n(t))||e&&t&&"number"==typeof t.length){d&&(t=d);var r=0,a=function(){};return{s:a,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){d=t[Symbol.iterator]()},n:function(){var t=d.next();return i=t.done,t},e:function(t){u=!0,o=t},f:function(){try{i||null==d.return||d.return()}finally{if(u)throw o}}}}(s);try{for(c.s();!(a=c.n()).done;){var f=r(a.value,2),$=f[0],p=f[1];if($.length!==p.length)return!1;l[p.charAt(0)]=$}}catch(t){c.e(t)}finally{c.f()}return new Date("".concat(l.m,"/").concat(l.d,"/").concat(l.y)).getDate()===+l.d}var v;if(!e.strictMode)return"[object Date]"===Object.prototype.toString.call(t)&&isFinite(t);return!1};var d=function(t){return t&&t.__esModule?t:{default:t}}(D);function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var d=[],r=!0,n=!1,a=void 0;try{for(var o,i=t[Symbol.iterator]();!(r=(o=i.next()).done)&&(d.push(o.value),!e||d.length!==e);r=!0);}catch(t){n=!0,a=t}finally{try{r||null==i.return||i.return()}finally{if(n)throw a}}return d}(t,e)||n(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,e){if(t){if("string"==typeof t)return a(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);return"Object"===d&&t.constructor&&(d=t.constructor.name),"Map"===d||"Set"===d?Array.from(t):"Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d)?a(t,e):void 0}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}var o={format:"YYYY/MM/DD",delimiters:["/","-"],strictMode:!1};t.exports=e.default,t.exports.default=e.default}(os,os.exports);var is=os.exports,us={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return e=(0,d.default)(e,r),"string"==typeof t&&n[e.hourFormat][e.mode].test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(D);var r={hourFormat:"hour24",mode:"default"},n={hour24:{default:/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,withSeconds:/^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/},hour12:{default:/^(0?[1-9]|1[0-2]):([0-5][0-9]) (A|P)M$/,withSeconds:/^(0?[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (A|P)M$/}};t.exports=e.default,t.exports.default=e.default}(us,us.exports);var ss=us.exports,ls={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var a=r;(e=e||{}).allow_hyphens&&(a=n);if(!a.test(t))return!1;t=t.replace(/-/g,"");for(var o=0,i=2,u=0;u<14;u++){var s=t.substring(14-u-1,14-u),l=parseInt(s,10)*i;o+=l>=10?l%10+1:l,1===i?i+=1:i-=1}if((10-o%10)%10!==parseInt(t.substring(14,15),10))return!1;return!0};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[0-9]{15}$/,n=/^\d{2}-\d{6}-\d{6}-\d{1}$/;t.exports=e.default,t.exports.default=e.default}(ls,ls.exports);var cs=ls.exports,fs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[a-f0-9]{32}$/;t.exports=e.default,t.exports.default=e.default}(fs,fs.exports);var $s=fs.exports,ps={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),0===((e=(0,r.default)(e,a)).ignore_whitespace?t.trim().length:t.length)};var d=n(R),r=n(D);function n(t){return t&&t.__esModule?t:{default:t}}var a={ignore_whitespace:!1};t.exports=e.default,t.exports.default=e.default}(ps,ps.exports);var vs=ps.exports,ys={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var r=(null==e?void 0:e.comparisonDate)||e||Date().toString(),n=(0,d.default)(r),a=(0,d.default)(t);return!!(a&&n&&a>n)};var d=function(t){return t&&t.__esModule?t:{default:t}}(Ju);t.exports=e.default,t.exports.default=e.default}(ys,ys.exports);var ms=ys.exports,gs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:String(new Date);(0,d.default)(t);var n=(0,r.default)(e),a=(0,r.default)(t);return!!(a&&n&&a<n)};var d=n(R),r=n(Ju);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(gs,gs.exports);var hs=gs.exports,As={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var n;if((0,d.default)(t),"[object Array]"===Object.prototype.toString.call(e)){var o=[];for(n in e)({}).hasOwnProperty.call(e,n)&&(o[n]=(0,r.default)(e[n]));return o.indexOf(t)>=0}if("object"===a(e))return e.hasOwnProperty(t);if(e&&"function"==typeof e.indexOf)return e.indexOf(t)>=0;return!1};var d=n(R),r=n(kt);function n(t){return t&&t.__esModule?t:{default:t}}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}t.exports=e.default,t.exports.default=e.default}(As,As.exports);var bs=As.exports,Is={exports:{}},Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.iso7064Check=function(t){for(var e=10,d=0;d<t.length-1;d++)e=(parseInt(t[d],10)+e)%10==0?9:(parseInt(t[d],10)+e)%10*2%11;return(e=1===e?0:11-e)===parseInt(t[10],10)},Ms.luhnCheck=function(t){for(var e=0,d=!1,r=t.length-1;r>=0;r--){if(d){var n=2*parseInt(t[r],10);e+=n>9?n.toString().split("").map((function(t){return parseInt(t,10)})).reduce((function(t,e){return t+e}),0):n}else e+=parseInt(t[r],10);d=!d}return e%10==0},Ms.reverseMultiplyAndSum=function(t,e){for(var d=0,r=0;r<t.length;r++)d+=t[r]*(e-r);return d},Ms.verhoeffCheck=function(t){for(var e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],d=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=t.split("").reverse().join(""),n=0,a=0;a<r.length;a++)n=e[n][d[a%8][parseInt(r[a],10)]];return 0===n},function(t,e){function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";(0,r.default)(t);var d=t.slice(0);if(e in f)return e in v&&(d=d.replace(v[e],"")),!!f[e].test(d)&&(!(e in $)||$[e](d));throw new Error("Invalid locale '".concat(e,"'"))};var r=i(R),n=function(t){if(t&&t.__esModule)return t;if(null===t||"object"!==d(t)&&"function"!=typeof t)return{default:t};var e=o();if(e&&e.has(t))return e.get(t);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var i=n?Object.getOwnPropertyDescriptor(t,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=t[a]}r.default=t,e&&e.set(t,r);return r}(Ms),a=i(is);function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function(){return t},t}function i(t){return t&&t.__esModule?t:{default:t}}function u(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return s(t,e);var d=Object.prototype.toString.call(t).slice(8,-1);"Object"===d&&t.constructor&&(d=t.constructor.name);if("Map"===d||"Set"===d)return Array.from(t);if("Arguments"===d||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d))return s(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var d=0,r=new Array(e);d<e;d++)r[d]=t[d];return r}var l={andover:["10","12"],atlanta:["60","67"],austin:["50","53"],brookhaven:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],cincinnati:["30","32","35","36","37","38","61"],fresno:["15","24"],internet:["20","26","27","45","46","47"],kansas:["40","44"],memphis:["94","95"],ogden:["80","90"],philadelphia:["33","39","41","42","43","46","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],sba:["31"]};function c(t){for(var e=!1,d=!1,r=0;r<3;r++)if(!e&&/[AEIOU]/.test(t[r]))e=!0;else if(!d&&e&&"X"===t[r])d=!0;else if(r>0){if(e&&!d&&!/[AEIOU]/.test(t[r]))return!1;if(d&&!/X/.test(t[r]))return!1}return!0}var f={"bg-BG":/^\d{10}$/,"cs-CZ":/^\d{6}\/{0,1}\d{3,4}$/,"de-AT":/^\d{9}$/,"de-DE":/^[1-9]\d{10}$/,"dk-DK":/^\d{6}-{0,1}\d{4}$/,"el-CY":/^[09]\d{7}[A-Z]$/,"el-GR":/^([0-4]|[7-9])\d{8}$/,"en-CA":/^\d{9}$/,"en-GB":/^\d{10}$|^(?!GB|NK|TN|ZZ)(?![DFIQUV])[A-Z](?![DFIQUVO])[A-Z]\d{6}[ABCD ]$/i,"en-IE":/^\d{7}[A-W][A-IW]{0,1}$/i,"en-US":/^\d{2}[- ]{0,1}\d{7}$/,"es-ES":/^(\d{0,8}|[XYZKLM]\d{7})[A-HJ-NP-TV-Z]$/i,"et-EE":/^[1-6]\d{6}(00[1-9]|0[1-9][0-9]|[1-6][0-9]{2}|70[0-9]|710)\d$/,"fi-FI":/^\d{6}[-+A]\d{3}[0-9A-FHJ-NPR-Y]$/i,"fr-BE":/^\d{11}$/,"fr-FR":/^[0-3]\d{12}$|^[0-3]\d\s\d{2}(\s\d{3}){3}$/,"fr-LU":/^\d{13}$/,"hr-HR":/^\d{11}$/,"hu-HU":/^8\d{9}$/,"it-IT":/^[A-Z]{6}[L-NP-V0-9]{2}[A-EHLMPRST][L-NP-V0-9]{2}[A-ILMZ][L-NP-V0-9]{3}[A-Z]$/i,"lv-LV":/^\d{6}-{0,1}\d{5}$/,"mt-MT":/^\d{3,7}[APMGLHBZ]$|^([1-8])\1\d{7}$/i,"nl-NL":/^\d{9}$/,"pl-PL":/^\d{10,11}$/,"pt-BR":/(?:^\d{11}$)|(?:^\d{14}$)/,"pt-PT":/^\d{9}$/,"ro-RO":/^\d{13}$/,"sk-SK":/^\d{6}\/{0,1}\d{3,4}$/,"sl-SI":/^[1-9]\d{7}$/,"sv-SE":/^(\d{6}[-+]{0,1}\d{4}|(18|19|20)\d{6}[-+]{0,1}\d{4})$/};f["lb-LU"]=f["fr-LU"],f["lt-LT"]=f["et-EE"],f["nl-BE"]=f["fr-BE"],f["fr-CA"]=f["en-CA"];var $={"bg-BG":function(t){var e=t.slice(0,2),d=parseInt(t.slice(2,4),10);d>40?(d-=40,e="20".concat(e)):d>20?(d-=20,e="18".concat(e)):e="19".concat(e),d<10&&(d="0".concat(d));var r="".concat(e,"/").concat(d,"/").concat(t.slice(4,6));if(!(0,a.default)(r,"YYYY/MM/DD"))return!1;for(var n=t.split("").map((function(t){return parseInt(t,10)})),o=[2,4,8,5,10,9,7,3,6],i=0,u=0;u<o.length;u++)i+=n[u]*o[u];return(i=i%11==10?0:i%11)===n[9]},"cs-CZ":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(0,2),10);if(10===t.length)e=e<54?"20".concat(e):"19".concat(e);else{if("000"===t.slice(6))return!1;if(!(e<54))return!1;e="19".concat(e)}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var d=parseInt(t.slice(2,4),10);if(d>50&&(d-=50),d>20){if(parseInt(e,10)<2004)return!1;d-=20}d<10&&(d="0".concat(d));var r="".concat(e,"/").concat(d,"/").concat(t.slice(4,6));if(!(0,a.default)(r,"YYYY/MM/DD"))return!1;if(10===t.length&&parseInt(t,10)%11!=0){var n=parseInt(t.slice(0,9),10)%11;if(!(parseInt(e,10)<1986&&10===n))return!1;if(0!==parseInt(t.slice(9),10))return!1}return!0},"de-AT":function(t){return n.luhnCheck(t)},"de-DE":function(t){for(var e=t.split("").map((function(t){return parseInt(t,10)})),d=[],r=0;r<e.length-1;r++){d.push("");for(var a=0;a<e.length-1;a++)e[r]===e[a]&&(d[r]+=a)}if(2!==(d=d.filter((function(t){return t.length>1}))).length&&3!==d.length)return!1;if(3===d[0].length){for(var o=d[0].split("").map((function(t){return parseInt(t,10)})),i=0,u=0;u<o.length-1;u++)o[u]+1===o[u+1]&&(i+=1);if(2===i)return!1}return n.iso7064Check(t)},"dk-DK":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(4,6),10);switch(t.slice(6,7)){case"0":case"1":case"2":case"3":e="19".concat(e);break;case"4":case"9":e=e<37?"20".concat(e):"19".concat(e);break;default:if(e<37)e="20".concat(e);else{if(!(e>58))return!1;e="18".concat(e)}}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var d="".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2));if(!(0,a.default)(d,"YYYY/MM/DD"))return!1;for(var r=t.split("").map((function(t){return parseInt(t,10)})),n=0,o=4,i=0;i<9;i++)n+=r[i]*o,1===(o-=1)&&(o=7);return 1!==(n%=11)&&(0===n?0===r[9]:r[9]===11-n)},"el-CY":function(t){for(var e=t.slice(0,8).split("").map((function(t){return parseInt(t,10)})),d=0,r=1;r<e.length;r+=2)d+=e[r];for(var n=0;n<e.length;n+=2)e[n]<2?d+=1-e[n]:(d+=2*(e[n]-2)+5,e[n]>4&&(d+=2));return String.fromCharCode(d%26+65)===t.charAt(8)},"el-GR":function(t){for(var e=t.split("").map((function(t){return parseInt(t,10)})),d=0,r=0;r<8;r++)d+=e[r]*Math.pow(2,8-r);return d%11%10===e[8]},"en-CA":function(t){var e=t.split(""),d=e.filter((function(t,e){return e%2})).map((function(t){return 2*Number(t)})).join("").split(""),r=e.filter((function(t,e){return!(e%2)})).concat(d).map((function(t){return Number(t)})).reduce((function(t,e){return t+e}));return r%10==0},"en-IE":function(t){var e=n.reverseMultiplyAndSum(t.split("").slice(0,7).map((function(t){return parseInt(t,10)})),8);return 9===t.length&&"W"!==t[8]&&(e+=9*(t[8].charCodeAt(0)-64)),0===(e%=23)?"W"===t[7].toUpperCase():t[7].toUpperCase()===String.fromCharCode(64+e)},"en-US":function(t){return-1!==function(){var t=[];for(var e in l)l.hasOwnProperty(e)&&t.push.apply(t,u(l[e]));return t}().indexOf(t.slice(0,2))},"es-ES":function(t){var e=t.toUpperCase().split("");if(isNaN(parseInt(e[0],10))&&e.length>1){var d=0;switch(e[0]){case"Y":d=1;break;case"Z":d=2}e.splice(0,1,d)}else for(;e.length<9;)e.unshift(0);e=e.join("");var r=parseInt(e.slice(0,8),10)%23;return e[8]===["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][r]},"et-EE":function(t){var e=t.slice(1,3);switch(t.slice(0,1)){case"1":case"2":e="18".concat(e);break;case"3":case"4":e="19".concat(e);break;default:e="20".concat(e)}var d="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(!(0,a.default)(d,"YYYY/MM/DD"))return!1;for(var r=t.split("").map((function(t){return parseInt(t,10)})),n=0,o=1,i=0;i<10;i++)n+=r[i]*o,10===(o+=1)&&(o=1);if(n%11==10){n=0,o=3;for(var u=0;u<10;u++)n+=r[u]*o,10===(o+=1)&&(o=1);if(n%11==10)return 0===r[10]}return n%11===r[10]},"fi-FI":function(t){var e=t.slice(4,6);switch(t.slice(6,7)){case"+":e="18".concat(e);break;case"-":e="19".concat(e);break;default:e="20".concat(e)}var d="".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2));if(!(0,a.default)(d,"YYYY/MM/DD"))return!1;var r=parseInt(t.slice(0,6)+t.slice(7,10),10)%31;return r<10?r===parseInt(t.slice(10),10):["A","B","C","D","E","F","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y"][r-=10]===t.slice(10)},"fr-BE":function(t){if("00"!==t.slice(2,4)||"00"!==t.slice(4,6)){var e="".concat(t.slice(0,2),"/").concat(t.slice(2,4),"/").concat(t.slice(4,6));if(!(0,a.default)(e,"YY/MM/DD"))return!1}var d=97-parseInt(t.slice(0,9),10)%97,r=parseInt(t.slice(9,11),10);return d===r||(d=97-parseInt("2".concat(t.slice(0,9)),10)%97)===r},"fr-FR":function(t){return t=t.replace(/\s/g,""),parseInt(t.slice(0,10),10)%511===parseInt(t.slice(10,13),10)},"fr-LU":function(t){var e="".concat(t.slice(0,4),"/").concat(t.slice(4,6),"/").concat(t.slice(6,8));return!!(0,a.default)(e,"YYYY/MM/DD")&&(!!n.luhnCheck(t.slice(0,12))&&n.verhoeffCheck("".concat(t.slice(0,11)).concat(t[12])))},"hr-HR":function(t){return n.iso7064Check(t)},"hu-HU":function(t){for(var e=t.split("").map((function(t){return parseInt(t,10)})),d=8,r=1;r<9;r++)d+=e[r]*(r+1);return d%11===e[9]},"it-IT":function(t){var e=t.toUpperCase().split("");if(!c(e.slice(0,3)))return!1;if(!c(e.slice(3,6)))return!1;for(var d={L:"0",M:"1",N:"2",P:"3",Q:"4",R:"5",S:"6",T:"7",U:"8",V:"9"},r=0,n=[6,7,9,10,12,13,14];r<n.length;r++){var o=n[r];e[o]in d&&e.splice(o,1,d[e[o]])}var i={A:"01",B:"02",C:"03",D:"04",E:"05",H:"06",L:"07",M:"08",P:"09",R:"10",S:"11",T:"12"}[e[8]],u=parseInt(e[9]+e[10],10);u>40&&(u-=40),u<10&&(u="0".concat(u));var s="".concat(e[6]).concat(e[7],"/").concat(i,"/").concat(u);if(!(0,a.default)(s,"YY/MM/DD"))return!1;for(var l=0,f=1;f<e.length-1;f+=2){var $=parseInt(e[f],10);isNaN($)&&($=e[f].charCodeAt(0)-65),l+=$}for(var p={A:1,B:0,C:5,D:7,E:9,F:13,G:15,H:17,I:19,J:21,K:2,L:4,M:18,N:20,O:11,P:3,Q:6,R:8,S:12,T:14,U:16,V:10,W:22,X:25,Y:24,Z:23,0:1,1:0},v=0;v<e.length-1;v+=2){var y=0;if(e[v]in p)y=p[e[v]];else{var m=parseInt(e[v],10);y=2*m+1,m>4&&(y+=2)}l+=y}return String.fromCharCode(65+l%26)===e[15]},"lv-LV":function(t){var e=(t=t.replace(/\W/,"")).slice(0,2);if("32"!==e){if("00"!==t.slice(2,4)){var d=t.slice(4,6);switch(t[6]){case"0":d="18".concat(d);break;case"1":d="19".concat(d);break;default:d="20".concat(d)}var r="".concat(d,"/").concat(t.slice(2,4),"/").concat(e);if(!(0,a.default)(r,"YYYY/MM/DD"))return!1}for(var n=1101,o=[1,6,3,7,9,10,5,8,4,2],i=0;i<t.length-1;i++)n-=parseInt(t[i],10)*o[i];return parseInt(t[10],10)===n%11}return!0},"mt-MT":function(t){if(9!==t.length){for(var e=t.toUpperCase().split("");e.length<8;)e.unshift(0);switch(t[7]){case"A":case"P":if(0===parseInt(e[6],10))return!1;break;default:var d=parseInt(e.join("").slice(0,5),10);if(d>32e3)return!1;if(d===parseInt(e.join("").slice(5,7),10))return!1}}return!0},"nl-NL":function(t){return n.reverseMultiplyAndSum(t.split("").slice(0,8).map((function(t){return parseInt(t,10)})),9)%11===parseInt(t[8],10)},"pl-PL":function(t){if(10===t.length){for(var e=[6,5,7,2,3,4,5,6,7],d=0,r=0;r<e.length;r++)d+=parseInt(t[r],10)*e[r];return 10!==(d%=11)&&d===parseInt(t[9],10)}var n=t.slice(0,2),o=parseInt(t.slice(2,4),10);o>80?(n="18".concat(n),o-=80):o>60?(n="22".concat(n),o-=60):o>40?(n="21".concat(n),o-=40):o>20?(n="20".concat(n),o-=20):n="19".concat(n),o<10&&(o="0".concat(o));var i="".concat(n,"/").concat(o,"/").concat(t.slice(4,6));if(!(0,a.default)(i,"YYYY/MM/DD"))return!1;for(var u=0,s=1,l=0;l<t.length-1;l++)u+=parseInt(t[l],10)*s%10,(s+=2)>10?s=1:5===s&&(s+=2);return(u=10-u%10)===parseInt(t[10],10)},"pt-BR":function(t){if(11===t.length){var e,d;if(e=0,"11111111111"===t||"22222222222"===t||"33333333333"===t||"44444444444"===t||"55555555555"===t||"66666666666"===t||"77777777777"===t||"88888888888"===t||"99999999999"===t||"00000000000"===t)return!1;for(var r=1;r<=9;r++)e+=parseInt(t.substring(r-1,r),10)*(11-r);if(10===(d=10*e%11)&&(d=0),d!==parseInt(t.substring(9,10),10))return!1;e=0;for(var n=1;n<=10;n++)e+=parseInt(t.substring(n-1,n),10)*(12-n);return 10===(d=10*e%11)&&(d=0),d===parseInt(t.substring(10,11),10)}if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return!1;for(var a=t.length-2,o=t.substring(0,a),i=t.substring(a),u=0,s=a-7,l=a;l>=1;l--)u+=o.charAt(a-l)*s,(s-=1)<2&&(s=9);var c=u%11<2?0:11-u%11;if(c!==parseInt(i.charAt(0),10))return!1;a+=1,o=t.substring(0,a),u=0,s=a-7;for(var f=a;f>=1;f--)u+=o.charAt(a-f)*s,(s-=1)<2&&(s=9);return(c=u%11<2?0:11-u%11)===parseInt(i.charAt(1),10)},"pt-PT":function(t){var e=11-n.reverseMultiplyAndSum(t.split("").slice(0,8).map((function(t){return parseInt(t,10)})),9)%11;return e>9?0===parseInt(t[8],10):e===parseInt(t[8],10)},"ro-RO":function(t){if("9000"!==t.slice(0,4)){var e=t.slice(1,3);switch(t[0]){case"1":case"2":e="19".concat(e);break;case"3":case"4":e="18".concat(e);break;case"5":case"6":e="20".concat(e)}var d="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(8===d.length){if(!(0,a.default)(d,"YY/MM/DD"))return!1}else if(!(0,a.default)(d,"YYYY/MM/DD"))return!1;for(var r=t.split("").map((function(t){return parseInt(t,10)})),n=[2,7,9,1,4,6,3,5,8,2,7,9],o=0,i=0;i<n.length;i++)o+=r[i]*n[i];return o%11==10?1===r[12]:r[12]===o%11}return!0},"sk-SK":function(t){if(9===t.length){if("000"===(t=t.replace(/\W/,"")).slice(6))return!1;var e=parseInt(t.slice(0,2),10);if(e>53)return!1;e=e<10?"190".concat(e):"19".concat(e);var d=parseInt(t.slice(2,4),10);d>50&&(d-=50),d<10&&(d="0".concat(d));var r="".concat(e,"/").concat(d,"/").concat(t.slice(4,6));if(!(0,a.default)(r,"YYYY/MM/DD"))return!1}return!0},"sl-SI":function(t){var e=11-n.reverseMultiplyAndSum(t.split("").slice(0,7).map((function(t){return parseInt(t,10)})),8)%11;return 10===e?0===parseInt(t[7],10):e===parseInt(t[7],10)},"sv-SE":function(t){var e=t.slice(0);t.length>11&&(e=e.slice(2));var d="",r=e.slice(2,4),o=parseInt(e.slice(4,6),10);if(t.length>11)d=t.slice(0,4);else if(d=t.slice(0,2),11===t.length&&o<60){var i=(new Date).getFullYear().toString(),u=parseInt(i.slice(0,2),10);if(i=parseInt(i,10),"-"===t[6])d=parseInt("".concat(u).concat(d),10)>i?"".concat(u-1).concat(d):"".concat(u).concat(d);else if(d="".concat(u-1).concat(d),i-parseInt(d,10)<100)return!1}o>60&&(o-=60),o<10&&(o="0".concat(o));var s="".concat(d,"/").concat(r,"/").concat(o);if(8===s.length){if(!(0,a.default)(s,"YY/MM/DD"))return!1}else if(!(0,a.default)(s,"YYYY/MM/DD"))return!1;return n.luhnCheck(t.replace(/\W/,""))}};$["lb-LU"]=$["fr-LU"],$["lt-LT"]=$["et-EE"],$["nl-BE"]=$["fr-BE"],$["fr-CA"]=$["en-CA"];var p=/[-\\\/!@#$%\^&\*\(\)\+\=\[\]]+/g,v={"de-AT":p,"de-DE":/[\/\\]/g,"fr-BE":p};v["nl-BE"]=v["fr-BE"],t.exports=e.default,t.exports.default=e.default}(Is,Is.exports);var Ss=Is.exports,_s=N(Ss),Os={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.has(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=new Set(["aa","ab","ae","af","ak","am","an","ar","as","av","ay","az","az","ba","be","bg","bh","bi","bm","bn","bo","br","bs","ca","ce","ch","co","cr","cs","cu","cv","cy","da","de","dv","dz","ee","el","en","eo","es","et","eu","fa","ff","fi","fj","fo","fr","fy","ga","gd","gl","gn","gu","gv","ha","he","hi","ho","hr","ht","hu","hy","hz","ia","id","ie","ig","ii","ik","io","is","it","iu","ja","jv","ka","kg","ki","kj","kk","kl","km","kn","ko","kr","ks","ku","kv","kw","ky","la","lb","lg","li","ln","lo","lt","lu","lv","mg","mh","mi","mk","ml","mn","mr","ms","mt","my","na","nb","nd","ne","ng","nl","nn","no","nr","nv","ny","oc","oj","om","or","os","pa","pi","pl","ps","pt","qu","rm","rn","ro","ru","rw","sa","sc","sd","se","sg","si","sk","sl","sm","sn","so","sq","sr","ss","st","su","sv","sw","ta","te","tg","th","ti","tk","tl","tn","to","tr","ts","tt","tw","ty","ug","uk","ur","uz","ve","vi","vo","wa","wo","xh","yi","yo","za","zh","zu"]);t.exports=e.default,t.exports.default=e.default}(Os,Os.exports);var xs=Os.exports,Es={};Object.defineProperty(Es,"__esModule",{value:!0});var Cs=Es.default=function(t){return(0,Ns.default)(t),Ps.has(t.toUpperCase())};Es.CurrencyCodes=void 0;var Ns=function(t){return t&&t.__esModule?t:{default:t}}(R);var Ps=new Set(["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTN","BWP","BYN","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNY","COP","COU","CRC","CUC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EGP","ERN","ETB","EUR","FJD","FKP","GBP","GEL","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","INR","IQD","IRR","ISK","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLL","SOS","SRD","SSP","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRY","TTD","TWD","TZS","UAH","UGX","USD","USN","UYI","UYU","UYW","UZS","VES","VND","VUV","WST","XAF","XAG","XAU","XBA","XBB","XBC","XBD","XCD","XDR","XOF","XPD","XPF","XPT","XSU","XTS","XUA","XXX","YER","ZAR","ZMW","ZWL"]);var Ls=Ps;Es.CurrencyCodes=Ls;var Rs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if((0,d.default)(t),r.test(t))return!0;return!1};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[A-HJ-NP-Za-km-z1-9]*$/;t.exports=e.default,t.exports.default=e.default}(Rs,Rs.exports);var Ts=Rs.exports,Ds=N(Ts),ws={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var r=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(r,"")};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(ws,ws.exports);var Bs=ws.exports,Fs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e){var r=new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g");return t.replace(r,"")}var n=t.length-1;for(;/\s/.test(t.charAt(n));)n-=1;return t.slice(0,n+1)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(Fs,Fs.exports);var Zs=Fs.exports,Gs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)((0,r.default)(t,e),e)};var d=n(Zs),r=n(Bs);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(Gs,Gs.exports);var Us=Gs.exports,js={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(js,js.exports);var Hs=js.exports,Vs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),t.replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`").replace(/&amp;/g,"&")};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(Vs,Vs.exports);var Ks=Vs.exports,ks={exports:{}},Ws={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(Ws,Ws.exports);var Ys=Ws.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);var n=e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F";return(0,r.default)(t,n)};var d=n(R),r=n(Ys);function n(t){return t&&t.__esModule?t:{default:t}}t.exports=e.default,t.exports.default=e.default}(ks,ks.exports);var Xs=ks.exports,zs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){return(0,d.default)(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(zs,zs.exports);var Js=zs.exports,qs={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,d.default)(t);for(var r=t.length-1;r>=0;r--)if(-1===e.indexOf(t[r]))return!1;return!0};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);t.exports=e.default,t.exports.default=e.default}(qs,qs.exports);var Qs=qs.exports,tl={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){e=(0,d.default)(e,r);var s=t.split("@"),l=s.pop(),c=[s.join("@"),l];if(c[1]=c[1].toLowerCase(),"gmail.com"===c[1]||"googlemail.com"===c[1]){if(e.gmail_remove_subaddress&&(c[0]=c[0].split("+")[0]),e.gmail_remove_dots&&(c[0]=c[0].replace(/\.+/g,u)),!c[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(c[0]=c[0].toLowerCase()),c[1]=e.gmail_convert_googlemaildotcom?"gmail.com":c[1]}else if(n.indexOf(c[1])>=0){if(e.icloud_remove_subaddress&&(c[0]=c[0].split("+")[0]),!c[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(c[0]=c[0].toLowerCase())}else if(a.indexOf(c[1])>=0){if(e.outlookdotcom_remove_subaddress&&(c[0]=c[0].split("+")[0]),!c[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(c[0]=c[0].toLowerCase())}else if(o.indexOf(c[1])>=0){if(e.yahoo_remove_subaddress){var f=c[0].split("-");c[0]=f.length>1?f.slice(0,-1).join("-"):f[0]}if(!c[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(c[0]=c[0].toLowerCase())}else i.indexOf(c[1])>=0?((e.all_lowercase||e.yandex_lowercase)&&(c[0]=c[0].toLowerCase()),c[1]="yandex.ru"):e.all_lowercase&&(c[0]=c[0].toLowerCase());return c.join("@")};var d=function(t){return t&&t.__esModule?t:{default:t}}(D);var r={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},n=["icloud.com","me.com"],a=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],o=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],i=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function u(t){return t.length>1?t:""}t.exports=e.default,t.exports.default=e.default}(tl,tl.exports);var el=tl.exports,dl={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,d.default)(t),r.test(t)};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r=/^[^\s-_](?!.*?[-_]{2,})[a-z0-9-\\][^\s]*[^-_\s]$/;t.exports=e.default,t.exports.default=e.default}(dl,dl.exports);var rl=dl.exports,nl={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,d.default)(t),e in r)return r[e](t);if("any"===e){for(var n in r){if((0,r[n])(t))return!0}return!1}throw new Error("Invalid locale '".concat(e,"'"))};var d=function(t){return t&&t.__esModule?t:{default:t}}(R);var r={"cs-CZ":function(t){return/^(([ABCDEFHIJKLMNPRSTUVXYZ]|[0-9])-?){5,8}$/.test(t)},"de-DE":function(t){return/^((A|AA|AB|AC|AE|AH|AK|AM|AN|AÖ|AP|AS|AT|AU|AW|AZ|B|BA|BB|BC|BE|BF|BH|BI|BK|BL|BM|BN|BO|BÖ|BS|BT|BZ|C|CA|CB|CE|CO|CR|CW|D|DA|DD|DE|DH|DI|DL|DM|DN|DO|DU|DW|DZ|E|EA|EB|ED|EE|EF|EG|EH|EI|EL|EM|EN|ER|ES|EU|EW|F|FB|FD|FF|FG|FI|FL|FN|FO|FR|FS|FT|FÜ|FW|FZ|G|GA|GC|GD|GE|GF|GG|GI|GK|GL|GM|GN|GÖ|GP|GR|GS|GT|GÜ|GV|GW|GZ|H|HA|HB|HC|HD|HE|HF|HG|HH|HI|HK|HL|HM|HN|HO|HP|HR|HS|HU|HV|HX|HY|HZ|IK|IL|IN|IZ|J|JE|JL|K|KA|KB|KC|KE|KF|KG|KH|KI|KK|KL|KM|KN|KO|KR|KS|KT|KU|KW|KY|L|LA|LB|LC|LD|LF|LG|LH|LI|LL|LM|LN|LÖ|LP|LR|LU|M|MA|MB|MC|MD|ME|MG|MH|MI|MK|ML|MM|MN|MO|MQ|MR|MS|MÜ|MW|MY|MZ|N|NB|ND|NE|NF|NH|NI|NK|NM|NÖ|NP|NR|NT|NU|NW|NY|NZ|OA|OB|OC|OD|OE|OF|OG|OH|OK|OL|OP|OS|OZ|P|PA|PB|PE|PF|PI|PL|PM|PN|PR|PS|PW|PZ|R|RA|RC|RD|RE|RG|RH|RI|RL|RM|RN|RO|RP|RS|RT|RU|RV|RW|RZ|S|SB|SC|SE|SG|SI|SK|SL|SM|SN|SO|SP|SR|ST|SU|SW|SY|SZ|TE|TF|TG|TO|TP|TR|TS|TT|TÜ|ÜB|UE|UH|UL|UM|UN|V|VB|VG|VK|VR|VS|W|WA|WB|WE|WF|WI|WK|WL|WM|WN|WO|WR|WS|WT|WÜ|WW|WZ|Z|ZE|ZI|ZP|ZR|ZW|ZZ)[- ]?[A-Z]{1,2}[- ]?\d{1,4}|(ABG|ABI|AIB|AIC|ALF|ALZ|ANA|ANG|ANK|APD|ARN|ART|ASL|ASZ|AUR|AZE|BAD|BAR|BBG|BCH|BED|BER|BGD|BGL|BID|BIN|BIR|BIT|BIW|BKS|BLB|BLK|BNA|BOG|BOH|BOR|BOT|BRA|BRB|BRG|BRK|BRL|BRV|BSB|BSK|BTF|BÜD|BUL|BÜR|BÜS|BÜZ|CAS|CHA|CLP|CLZ|COC|COE|CUX|DAH|DAN|DAU|DBR|DEG|DEL|DGF|DIL|DIN|DIZ|DKB|DLG|DON|DUD|DÜW|EBE|EBN|EBS|ECK|EIC|EIL|EIN|EIS|EMD|EMS|ERB|ERH|ERK|ERZ|ESB|ESW|FDB|FDS|FEU|FFB|FKB|FLÖ|FOR|FRG|FRI|FRW|FTL|FÜS|GAN|GAP|GDB|GEL|GEO|GER|GHA|GHC|GLA|GMN|GNT|GOA|GOH|GRA|GRH|GRI|GRM|GRZ|GTH|GUB|GUN|GVM|HAB|HAL|HAM|HAS|HBN|HBS|HCH|HDH|HDL|HEB|HEF|HEI|HER|HET|HGN|HGW|HHM|HIG|HIP|HMÜ|HOG|HOH|HOL|HOM|HOR|HÖS|HOT|HRO|HSK|HST|HVL|HWI|IGB|ILL|JÜL|KEH|KEL|KEM|KIB|KLE|KLZ|KÖN|KÖT|KÖZ|KRU|KÜN|KUS|KYF|LAN|LAU|LBS|LBZ|LDK|LDS|LEO|LER|LEV|LIB|LIF|LIP|LÖB|LOS|LRO|LSZ|LÜN|LUP|LWL|MAB|MAI|MAK|MAL|MED|MEG|MEI|MEK|MEL|MER|MET|MGH|MGN|MHL|MIL|MKK|MOD|MOL|MON|MOS|MSE|MSH|MSP|MST|MTK|MTL|MÜB|MÜR|MYK|MZG|NAB|NAI|NAU|NDH|NEA|NEB|NEC|NEN|NES|NEW|NMB|NMS|NOH|NOL|NOM|NOR|NVP|NWM|OAL|OBB|OBG|OCH|OHA|ÖHR|OHV|OHZ|OPR|OSL|OVI|OVL|OVP|PAF|PAN|PAR|PCH|PEG|PIR|PLÖ|PRÜ|QFT|QLB|RDG|REG|REH|REI|RID|RIE|ROD|ROF|ROK|ROL|ROS|ROT|ROW|RSL|RÜD|RÜG|SAB|SAD|SAN|SAW|SBG|SBK|SCZ|SDH|SDL|SDT|SEB|SEE|SEF|SEL|SFB|SFT|SGH|SHA|SHG|SHK|SHL|SIG|SIM|SLE|SLF|SLK|SLN|SLS|SLÜ|SLZ|SMÜ|SOB|SOG|SOK|SÖM|SON|SPB|SPN|SRB|SRO|STA|STB|STD|STE|STL|SUL|SÜW|SWA|SZB|TBB|TDO|TET|TIR|TÖL|TUT|UEM|UER|UFF|USI|VAI|VEC|VER|VIB|VIE|VIT|VOH|WAF|WAK|WAN|WAR|WAT|WBS|WDA|WEL|WEN|WER|WES|WHV|WIL|WIS|WIT|WIZ|WLG|WMS|WND|WOB|WOH|WOL|WOR|WOS|WRN|WSF|WST|WSW|WTL|WTM|WUG|WÜM|WUN|WUR|WZL|ZEL|ZIG)[- ]?(([A-Z][- ]?\d{1,4})|([A-Z]{2}[- ]?\d{1,3})))[- ]?(E|H)?$/.test(t)},"de-LI":function(t){return/^FL[- ]?\d{1,5}[UZ]?$/.test(t)},"en-IN":function(t){return/^[A-Z]{2}[ -]?[0-9]{1,2}(?:[ -]?[A-Z])(?:[ -]?[A-Z]*)?[ -]?[0-9]{4}$/.test(t)},"es-AR":function(t){return/^(([A-Z]{2} ?[0-9]{3} ?[A-Z]{2})|([A-Z]{3} ?[0-9]{3}))$/.test(t)},"fi-FI":function(t){return/^(?=.{4,7})(([A-Z]{1,3}|[0-9]{1,3})[\s-]?([A-Z]{1,3}|[0-9]{1,5}))$/.test(t)},"hu-HU":function(t){return/^((((?!AAA)(([A-NPRSTVZWXY]{1})([A-PR-Z]{1})([A-HJ-NPR-Z]))|(A[ABC]I)|A[ABC]O|A[A-W]Q|BPI|BPO|UCO|UDO|XAO)-(?!000)\d{3})|(M\d{6})|((CK|DT|CD|HC|H[ABEFIKLMNPRSTVX]|MA|OT|R[A-Z]) \d{2}-\d{2})|(CD \d{3}-\d{3})|(C-(C|X) \d{4})|(X-(A|B|C) \d{4})|(([EPVZ]-\d{5}))|(S A[A-Z]{2} \d{2})|(SP \d{2}-\d{2}))$/.test(t)},"pt-BR":function(t){return/^[A-Z]{3}[ -]?[0-9][A-Z][0-9]{2}|[A-Z]{3}[ -]?[0-9]{4}$/.test(t)},"pt-PT":function(t){return/^([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})$/.test(t)},"sq-AL":function(t){return/^[A-Z]{2}[- ]?((\d{3}[- ]?(([A-Z]{2})|T))|(R[- ]?\d{3}))$/.test(t)},"sv-SE":function(t){return/^[A-HJ-PR-UW-Z]{3} ?[\d]{2}[A-HJ-PR-UW-Z1-9]$|(^[A-ZÅÄÖ ]{2,7}$)/.test(t.trim())}};t.exports=e.default,t.exports.default=e.default}(nl,nl.exports);var al=nl.exports,ol={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;(0,r.default)(t);var n=l(t);if((e=(0,d.default)(e||{},s)).returnScore)return c(n,e);return n.length>=e.minLength&&n.lowercaseCount>=e.minLowercase&&n.uppercaseCount>=e.minUppercase&&n.numberCount>=e.minNumbers&&n.symbolCount>=e.minSymbols};var d=n(D),r=n(R);function n(t){return t&&t.__esModule?t:{default:t}}var a=/^[A-Z]$/,o=/^[a-z]$/,i=/^[0-9]$/,u=/^[-#!$@£%^&*()_+|~=`{}\[\]:";'<>?,.\/ ]$/,s={minLength:8,minLowercase:1,minUppercase:1,minNumbers:1,minSymbols:1,returnScore:!1,pointsPerUnique:1,pointsPerRepeat:.5,pointsForContainingLower:10,pointsForContainingUpper:10,pointsForContainingNumber:10,pointsForContainingSymbol:10};function l(t){var e=function(t){var e={};return Array.from(t).forEach((function(t){e[t]?e[t]+=1:e[t]=1})),e}(t),d={length:t.length,uniqueChars:Object.keys(e).length,uppercaseCount:0,lowercaseCount:0,numberCount:0,symbolCount:0};return Object.keys(e).forEach((function(t){a.test(t)?d.uppercaseCount+=e[t]:o.test(t)?d.lowercaseCount+=e[t]:i.test(t)?d.numberCount+=e[t]:u.test(t)&&(d.symbolCount+=e[t])})),d}function c(t,e){var d=0;return d+=t.uniqueChars*e.pointsPerUnique,d+=(t.length-t.uniqueChars)*e.pointsPerRepeat,t.lowercaseCount>0&&(d+=e.pointsForContainingLower),t.uppercaseCount>0&&(d+=e.pointsForContainingUpper),t.numberCount>0&&(d+=e.pointsForContainingNumber),t.symbolCount>0&&(d+=e.pointsForContainingSymbol),d}t.exports=e.default,t.exports.default=e.default}(ol,ol.exports);var il=ol.exports,ul={};function sl(t){return sl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sl(t)}Object.defineProperty(ul,"__esModule",{value:!0}),ul.default=function(t,e){if((0,ll.default)(t),(0,ll.default)(e),e in $l)return $l[e](t);throw new Error("Invalid country code: '".concat(e,"'"))},ul.vatMatchers=void 0;var ll=function(t){return t&&t.__esModule?t:{default:t}}(R),cl=function(t){if(t&&t.__esModule)return t;if(null===t||"object"!==sl(t)&&"function"!=typeof t)return{default:t};var e=fl();if(e&&e.has(t))return e.get(t);var d={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var a=r?Object.getOwnPropertyDescriptor(t,n):null;a&&(a.get||a.set)?Object.defineProperty(d,n,a):d[n]=t[n]}d.default=t,e&&e.set(t,d);return d}(Ms);function fl(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return fl=function(){return t},t}var $l={AT:function(t){return/^(AT)?U\d{8}$/.test(t)},BE:function(t){return/^(BE)?\d{10}$/.test(t)},BG:function(t){return/^(BG)?\d{9,10}$/.test(t)},HR:function(t){return/^(HR)?\d{11}$/.test(t)},CY:function(t){return/^(CY)?\w{9}$/.test(t)},CZ:function(t){return/^(CZ)?\d{8,10}$/.test(t)},DK:function(t){return/^(DK)?\d{8}$/.test(t)},EE:function(t){return/^(EE)?\d{9}$/.test(t)},FI:function(t){return/^(FI)?\d{8}$/.test(t)},FR:function(t){return/^(FR)?\w{2}\d{9}$/.test(t)},DE:function(t){return/^(DE)?\d{9}$/.test(t)},EL:function(t){return/^(EL)?\d{9}$/.test(t)},HU:function(t){return/^(HU)?\d{8}$/.test(t)},IE:function(t){return/^(IE)?\d{7}\w{1}(W)?$/.test(t)},IT:function(t){return/^(IT)?\d{11}$/.test(t)},LV:function(t){return/^(LV)?\d{11}$/.test(t)},LT:function(t){return/^(LT)?\d{9,12}$/.test(t)},LU:function(t){return/^(LU)?\d{8}$/.test(t)},MT:function(t){return/^(MT)?\d{8}$/.test(t)},NL:function(t){return/^(NL)?\d{9}B\d{2}$/.test(t)},PL:function(t){return/^(PL)?(\d{10}|(\d{3}-\d{3}-\d{2}-\d{2})|(\d{3}-\d{2}-\d{2}-\d{3}))$/.test(t)},PT:function(t){var e=t.match(/^(PT)?(\d{9})$/);if(!e)return!1;var d=e[2],r=11-cl.reverseMultiplyAndSum(d.split("").slice(0,8).map((function(t){return parseInt(t,10)})),9)%11;return r>9?0===parseInt(d[8],10):r===parseInt(d[8],10)},RO:function(t){return/^(RO)?\d{2,10}$/.test(t)},SK:function(t){return/^(SK)?\d{10}$/.test(t)},SI:function(t){return/^(SI)?\d{8}$/.test(t)},ES:function(t){return/^(ES)?\w\d{7}[A-Z]$/.test(t)},SE:function(t){return/^(SE)?\d{12}$/.test(t)},AL:function(t){return/^(AL)?\w{9}[A-Z]$/.test(t)},MK:function(t){return/^(MK)?\d{13}$/.test(t)},AU:function(t){return/^(AU)?\d{11}$/.test(t)},BY:function(t){return/^(УНП )?\d{9}$/.test(t)},CA:function(t){return/^(CA)?\d{9}$/.test(t)},IS:function(t){return/^(IS)?\d{5,6}$/.test(t)},IN:function(t){return/^(IN)?\d{15}$/.test(t)},ID:function(t){return/^(ID)?(\d{15}|(\d{2}.\d{3}.\d{3}.\d{1}-\d{3}.\d{3}))$/.test(t)},IL:function(t){return/^(IL)?\d{9}$/.test(t)},KZ:function(t){return/^(KZ)?\d{9}$/.test(t)},NZ:function(t){return/^(NZ)?\d{9}$/.test(t)},NG:function(t){return/^(NG)?(\d{12}|(\d{8}-\d{4}))$/.test(t)},NO:function(t){return/^(NO)?\d{9}MVA$/.test(t)},PH:function(t){return/^(PH)?(\d{12}|\d{3} \d{3} \d{3} \d{3})$/.test(t)},RU:function(t){return/^(RU)?(\d{10}|\d{12})$/.test(t)},SM:function(t){return/^(SM)?\d{5}$/.test(t)},SA:function(t){return/^(SA)?\d{15}$/.test(t)},RS:function(t){return/^(RS)?\d{9}$/.test(t)},CH:function(t){return/^(CH)?(\d{6}|\d{9}|(\d{3}.\d{3})|(\d{3}.\d{3}.\d{3}))(TVA|MWST|IVA)$/.test(t)},TR:function(t){return/^(TR)?\d{10}$/.test(t)},UA:function(t){return/^(UA)?\d{12}$/.test(t)},GB:function(t){return/^GB((\d{3} \d{4} ([0-8][0-9]|9[0-6]))|(\d{9} \d{3})|(((GD[0-4])|(HA[5-9]))[0-9]{2}))$/.test(t)},UZ:function(t){return/^(UZ)?\d{9}$/.test(t)},AR:function(t){return/^(AR)?\d{11}$/.test(t)},BO:function(t){return/^(BO)?\d{7}$/.test(t)},BR:function(t){return/^(BR)?((\d{2}.\d{3}.\d{3}\/\d{4}-\d{2})|(\d{3}.\d{3}.\d{3}-\d{2}))$/.test(t)},CL:function(t){return/^(CL)?\d{8}-\d{1}$/.test(t)},CO:function(t){return/^(CO)?\d{10}$/.test(t)},CR:function(t){return/^(CR)?\d{9,12}$/.test(t)},EC:function(t){return/^(EC)?\d{13}$/.test(t)},SV:function(t){return/^(SV)?\d{4}-\d{6}-\d{3}-\d{1}$/.test(t)},GT:function(t){return/^(GT)?\d{7}-\d{1}$/.test(t)},HN:function(t){return/^(HN)?$/.test(t)},MX:function(t){return/^(MX)?\w{3,4}\d{6}\w{3}$/.test(t)},NI:function(t){return/^(NI)?\d{3}-\d{6}-\d{4}\w{1}$/.test(t)},PA:function(t){return/^(PA)?$/.test(t)},PY:function(t){return/^(PY)?\d{6,8}-\d{1}$/.test(t)},PE:function(t){return/^(PE)?\d{11}$/.test(t)},DO:function(t){return/^(DO)?(\d{11}|(\d{3}-\d{7}-\d{1})|[1,4,5]{1}\d{8}|([1,4,5]{1})-\d{2}-\d{5}-\d{1})$/.test(t)},UY:function(t){return/^(UY)?\d{12}$/.test(t)},VE:function(t){return/^(VE)?[J,G,V,E]{1}-(\d{9}|(\d{8}-\d{1}))$/.test(t)}};ul.vatMatchers=$l,function(t,e){function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=qt(Ju),n=qt(Ct),a=qt(Qu),o=qt(es),i=qt(rs),u=qt(Wt),s=qt(Ln),l=qt(td),c=qt(an),f=qt(Dd),$=qt(qe),p=qt(as),v=qt(Xe),y=qt(is),m=qt(ss),g=qt(Ho),h=qt(Ji),A=Jt(Qt),b=Jt(oe),I=qt(Yo),M=qt(mu),S=qt(Vd),_=qt(Ar),O=qt(gn),x=qt(cs),E=qt(be),C=qt(od),N=qt(fd),P=qt(hd),L=qt(Xr),R=qt(Ku),T=qt(tn),D=qt(Hd),B=Jt(nt),F=qt(ye),Z=qt(Cd),G=qt(fu),U=qt(Nt),j=qt(Sd),H=qt(Fu),V=qt(Ci),K=qt(ki),k=Jt(Ri),W=qt(ni),Y=qt($s),X=qt(Po),z=qt(vr),J=qt(lr),q=qt(vs),Q=qt(Mn),tt=qt(Pe),et=qt(cn),dt=qt(Vr),rt=qt(ms),at=qt(hs),ot=qt(bs),it=qt(Be),ut=qt(Fe),st=qt(Ui),lt=qt(hi),ct=qt(tr),ft=qt(Xd),$t=qt(wo),pt=qt(Ss),vt=Jt(Sr),yt=qt(Si),mt=qt(He),gt=qt(si),ht=qt(xs),At=qt(ar),bt=qt(Ru),It=qt(Pr),Mt=qt(Zr),St=qt(Es),_t=qt(Qo),Ot=qt(Ts),xt=qt(Oe),Et=qt(pi),Pt=qt(du),Lt=qt(iu),Rt=qt(w),Tt=Jt(bu),Dt=qt(Bs),wt=qt(Zs),Bt=qt(Us),Ft=qt(Hs),Zt=qt(Ks),Gt=qt(Xs),Ut=qt(Js),jt=qt(Ys),Ht=qt(Qs),Vt=qt(el),Kt=qt(rl),kt=qt(al),Yt=qt(il),Xt=qt(ul);function zt(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return zt=function(){return t},t}function Jt(t){if(t&&t.__esModule)return t;if(null===t||"object"!==d(t)&&"function"!=typeof t)return{default:t};var e=zt();if(e&&e.has(t))return e.get(t);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var o=n?Object.getOwnPropertyDescriptor(t,a):null;o&&(o.get||o.set)?Object.defineProperty(r,a,o):r[a]=t[a]}return r.default=t,e&&e.set(t,r),r}function qt(t){return t&&t.__esModule?t:{default:t}}var te={version:"13.9.0",toDate:r.default,toFloat:n.default,toInt:a.default,toBoolean:o.default,equals:i.default,contains:u.default,matches:s.default,isEmail:l.default,isURL:c.default,isMACAddress:f.default,isIP:$.default,isIPRange:p.default,isFQDN:v.default,isBoolean:g.default,isIBAN:k.default,isBIC:W.default,isAlpha:A.default,isAlphaLocales:A.locales,isAlphanumeric:b.default,isAlphanumericLocales:b.locales,isNumeric:I.default,isPassportNumber:M.default,isPort:S.default,isLowercase:_.default,isUppercase:O.default,isAscii:E.default,isFullWidth:C.default,isHalfWidth:N.default,isVariableWidth:P.default,isMultibyte:L.default,isSemVer:R.default,isSurrogatePair:T.default,isInt:D.default,isIMEI:x.default,isFloat:B.default,isFloatLocales:B.locales,isDecimal:F.default,isHexadecimal:Z.default,isOctal:G.default,isDivisibleBy:U.default,isHexColor:j.default,isRgbColor:H.default,isHSL:V.default,isISRC:K.default,isMD5:Y.default,isHash:X.default,isJWT:z.default,isJSON:J.default,isEmpty:q.default,isLength:Q.default,isLocale:h.default,isByteLength:tt.default,isUUID:et.default,isMongoId:dt.default,isAfter:rt.default,isBefore:at.default,isIn:ot.default,isLuhnNumber:it.default,isCreditCard:ut.default,isIdentityCard:st.default,isEAN:lt.default,isISIN:ct.default,isISBN:ft.default,isISSN:$t.default,isMobilePhone:vt.default,isMobilePhoneLocales:vt.locales,isPostalCode:Tt.default,isPostalCodeLocales:Tt.locales,isEthereumAddress:yt.default,isCurrency:mt.default,isBtcAddress:gt.default,isISO6391:ht.default,isISO8601:At.default,isRFC3339:bt.default,isISO31661Alpha2:It.default,isISO31661Alpha3:Mt.default,isISO4217:St.default,isBase32:_t.default,isBase58:Ot.default,isBase64:xt.default,isDataURI:Et.default,isMagnetURI:Pt.default,isMimeType:Lt.default,isLatLong:Rt.default,ltrim:Dt.default,rtrim:wt.default,trim:Bt.default,escape:Ft.default,unescape:Zt.default,stripLow:Gt.default,whitelist:Ut.default,blacklist:jt.default,isWhitelisted:Ht.default,normalizeEmail:Vt.default,toString:toString,isSlug:Kt.default,isStrongPassword:Yt.default,isTaxID:pt.default,isDate:y.default,isTime:m.default,isLicensePlate:kt.default,isVAT:Xt.default,ibanLocales:k.locales};e.default=te,t.exports=e.default,t.exports.default=e.default}(Xu,Xu.exports);var pl=Xu.exports,vl="isStrongPassword";function yl(t,e){return"string"==typeof t&&pl.isStrongPassword(t,e)}var ml="isTimeZone";function gl(t){try{return"string"==typeof t&&(Intl.DateTimeFormat(void 0,{timeZone:t}),!0)}catch(t){return!1}}var hl="isBase58";function Al(t){return"string"==typeof t&&Ds(t)}var bl="isTaxId";function Il(t,e){return"string"==typeof t&&_s(t,e||"en-US")}var Ml="isISO4217CurrencyCode";function Sl(t){return"string"==typeof t&&Cs(t)}var _l="isBoolean";function Ol(t){return t instanceof Boolean||"boolean"==typeof t}var xl="isDate";function El(t){return t instanceof Date&&!isNaN(t.getTime())}var Cl="isNumber";function Nl(t,e){if(void 0===e&&(e={}),"number"!=typeof t)return!1;if(t===1/0||t===-1/0)return!!e.allowInfinity;if(Number.isNaN(t))return!!e.allowNaN;if(void 0!==e.maxDecimalPlaces){var d=0;if(t%1!=0&&(d=t.toString().split(".")[1].length),d>e.maxDecimalPlaces)return!1}return Number.isFinite(t)}var Pl=function(t,e){var d="function"==typeof Symbol&&t[Symbol.iterator];if(!d)return t;var r,n,a=d.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(t){n={error:t}}finally{try{r&&!r.done&&(d=a.return)&&d.call(a)}finally{if(n)throw n.error}}return o},Ll="isEnum";function Rl(t,e){return Object.keys(e).map((function(t){return e[t]})).includes(t)}function Tl(t){return Object.entries(t).filter((function(t){var e=Pl(t,2),d=e[0];return e[1],isNaN(parseInt(d))})).map((function(t){var e=Pl(t,2);return e[0],e[1]}))}var Dl="isInt";function wl(t){return"number"==typeof t&&Number.isInteger(t)}var Bl="isString";function Fl(t){return t instanceof String||"string"==typeof t}var Zl="isArray";function Gl(t){return Array.isArray(t)}var Ul="isObject";function jl(t){return null!=t&&("object"==typeof t||"function"==typeof t)&&!Array.isArray(t)}var Hl="arrayContains";function Vl(t,e){return!!Array.isArray(t)&&e.every((function(e){return-1!==t.indexOf(e)}))}var Kl="arrayNotContains";function kl(t,e){return!!Array.isArray(t)&&e.every((function(e){return-1===t.indexOf(e)}))}var Wl="arrayNotEmpty";function Yl(t){return Array.isArray(t)&&t.length>0}var Xl="arrayMinSize";function zl(t,e){return Array.isArray(t)&&t.length>=e}var Jl="arrayMaxSize";function ql(t,e){return Array.isArray(t)&&t.length<=e}var Ql="arrayUnique";function tc(t,e){if(!Array.isArray(t))return!1;e&&(t=t.map((function(t){return null!=t?e(t):t})));var d=t.filter((function(t,e,d){return d.indexOf(t)===e}));return t.length===d.length}var ec="isNotEmptyObject";function dc(t,e){if(!jl(t))return!1;if(!1===(null==e?void 0:e.nullable))return!Object.values(t).every((function(t){return null==t}));for(var d in t)if(t.hasOwnProperty(d))return!0;return!1}var rc="isInstance";function nc(t,e){return e&&"function"==typeof e&&t instanceof e}t.ARRAY_CONTAINS=Hl,t.ARRAY_MAX_SIZE=Jl,t.ARRAY_MIN_SIZE=Xl,t.ARRAY_NOT_CONTAINS=Kl,t.ARRAY_NOT_EMPTY=Wl,t.ARRAY_UNIQUE=Ql,t.Allow=function(t){return function(d,r){var n={type:c.WHITELIST,target:d.constructor,propertyName:r,validationOptions:t};s().addValidationMetadata(new e(n))}},t.ArrayContains=function(t,e){return _({name:Hl,constraints:[t],validator:{validate:function(t,e){return Vl(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain $constraint1 values"}),e)}},e)},t.ArrayMaxSize=function(t,e){return _({name:Jl,constraints:[t],validator:{validate:function(t,e){return ql(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain no more than $constraint1 elements"}),e)}},e)},t.ArrayMinSize=function(t,e){return _({name:Xl,constraints:[t],validator:{validate:function(t,e){return zl(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain at least $constraint1 elements"}),e)}},e)},t.ArrayNotContains=function(t,e){return _({name:Kl,constraints:[t],validator:{validate:function(t,e){return kl(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property should not contain $constraint1 values"}),e)}},e)},t.ArrayNotEmpty=function(t){return _({name:Wl,validator:{validate:function(t,e){return Yl(t)},defaultMessage:S((function(t){return t+"$property should not be empty"}),t)}},t)},t.ArrayUnique=function(t,e){var d="function"==typeof t?t:void 0,r="function"!=typeof t?t:e;return _({name:Ql,validator:{validate:function(t,e){return tc(t,d)},defaultMessage:S((function(t){return t+"All $property's elements must be unique"}),r)}},r)},t.CONTAINS=Xt,t.Contains=function(t,e){return _({name:Xt,constraints:[t],validator:{validate:function(t,e){return zt(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain a $constraint1 string"}),e)}},e)},t.EQUALS=V,t.Equals=function(t,e){return _({name:V,constraints:[t],validator:{validate:function(t,e){return K(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be equal to $constraint1"}),e)}},e)},t.IS_ALPHA=ne,t.IS_ALPHANUMERIC=ce,t.IS_ARRAY=Zl,t.IS_ASCII=Me,t.IS_BASE32=ei,t.IS_BASE58=hl,t.IS_BASE64=Ee,t.IS_BIC=oi,t.IS_BOOLEAN=_l,t.IS_BOOLEAN_STRING=Ko,t.IS_BTC_ADDRESS=ci,t.IS_BYTE_LENGTH=Re,t.IS_CREDIT_CARD=Ge,t.IS_CURRENCY=Ke,t.IS_DATA_URI=yi,t.IS_DATE=xl,t.IS_DATE_STRING=Go,t.IS_DECIMAL=ge,t.IS_DEFINED=O,t.IS_DIVISIBLE_BY=Lt,t.IS_EAN=bi,t.IS_EMAIL=dd,t.IS_EMPTY=Y,t.IS_ENUM=Ll,t.IS_ETHEREUM_ADDRESS=Oi,t.IS_FIREBASE_PUSH_ID=vn,t.IS_FQDN=nd,t.IS_FULL_WIDTH=ld,t.IS_HALF_WIDTH=yd,t.IS_HASH=Ro,t.IS_HEXADECIMAL=Pd,t.IS_HEX_COLOR=Od,t.IS_HSL=Pi,t.IS_IBAN=Fi,t.IS_IDENTITY_CARD=Hi,t.IS_IN=q,t.IS_INSTANCE=rc,t.IS_INT=Dl,t.IS_IP=Zd,t.IS_ISBN=Jd,t.IS_ISIN=dr,t.IS_ISO31661_ALPHA_2=wr,t.IS_ISO31661_ALPHA_3=Ur,t.IS_ISO4217_CURRENCY_CODE=Ml,t.IS_ISO8601=ir,t.IS_ISRC=Yi,t.IS_ISSN=Fo,t.IS_JSON=fr,t.IS_JWT=mr,t.IS_LATITUDE=G,t.IS_LATLONG=F,t.IS_LENGTH=_n,t.IS_LOCALE=Qi,t.IS_LONGITUDE=j,t.IS_LOWERCASE=Ir,t.IS_MAC_ADDRESS=Bd,t.IS_MAGNET_URI=nu,t.IS_MILITARY_TIME=Eo,t.IS_MIME_TYPE=su,t.IS_MOBILE_PHONE=Cr,t.IS_MONGO_ID=kr,t.IS_MULTIBYTE=Jr,t.IS_NEGATIVE=wt,t.IS_NOT_EMPTY=z,t.IS_NOT_EMPTY_OBJECT=ec,t.IS_NOT_IN=tt,t.IS_NUMBER=Cl,t.IS_NUMBER_STRING=zo,t.IS_OBJECT=Ul,t.IS_OCTAL=pu,t.IS_OPTIONAL=E,t.IS_PASSPORT_NUMBER=hu,t.IS_PHONE_NUMBER=Oo,t.IS_PORT=kd,t.IS_POSITIVE=Tt,t.IS_POSTAL_CODE=Nu,t.IS_RFC_3339=Du,t.IS_RGB_COLOR=Gu,t.IS_SEM_VER=Wu,t.IS_STRING=Bl,t.IS_STRONG_PASSWORD=vl,t.IS_SURROGATE_PAIR=dn,t.IS_TAX_ID=bl,t.IS_TIMEZONE=ml,t.IS_UPPERCASE=An,t.IS_URL=un,t.IS_UUID=$n,t.IS_VARIABLE_WIDTH=bd,t.IsAlpha=function(t,e){return _({name:ne,constraints:[t],validator:{validate:function(t,e){return ae(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain only letters (a-zA-Z)"}),e)}},e)},t.IsAlphanumeric=function(t,e){return _({name:ce,constraints:[t],validator:{validate:function(t,e){return fe(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must contain only letters and numbers"}),e)}},e)},t.IsArray=function(t){return _({name:Zl,validator:{validate:function(t,e){return Gl(t)},defaultMessage:S((function(t){return t+"$property must be an array"}),t)}},t)},t.IsAscii=function(t){return _({name:Me,validator:{validate:function(t,e){return Se(t)},defaultMessage:S((function(t){return t+"$property must contain only ASCII characters"}),t)}},t)},t.IsBIC=function(t){return _({name:oi,validator:{validate:function(t,e){return ii(t)},defaultMessage:S((function(t){return t+"$property must be a BIC or SWIFT code"}),t)}},t)},t.IsBase32=function(t){return _({name:ei,validator:{validate:function(t,e){return di(t)},defaultMessage:S((function(t){return t+"$property must be base32 encoded"}),t)}},t)},t.IsBase58=function(t){return _({name:hl,validator:{validate:function(t,e){return Al(t)},defaultMessage:S((function(t){return t+"$property must be base58 encoded"}),t)}},t)},t.IsBase64=function(t,e){return _({name:Ee,constraints:[t],validator:{validate:function(t,e){return Ce(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be base64 encoded"}),e)}},e)},t.IsBoolean=function(t){return _({name:_l,validator:{validate:function(t,e){return Ol(t)},defaultMessage:S((function(t){return t+"$property must be a boolean value"}),t)}},t)},t.IsBooleanString=function(t){return _({name:Ko,validator:{validate:function(t,e){return ko(t)},defaultMessage:S((function(t){return t+"$property must be a boolean string"}),t)}},t)},t.IsBtcAddress=function(t){return _({name:ci,validator:{validate:function(t,e){return fi(t)},defaultMessage:S((function(t){return t+"$property must be a BTC address"}),t)}},t)},t.IsByteLength=function(t,e,d){return _({name:Re,constraints:[t,e],validator:{validate:function(t,e){return Te(t,null==e?void 0:e.constraints[0],null==e?void 0:e.constraints[1])},defaultMessage:S((function(t){return t+"$property's byte length must fall into ($constraint1, $constraint2) range"}),d)}},d)},t.IsCreditCard=function(t){return _({name:Ge,validator:{validate:function(t,e){return Ue(t)},defaultMessage:S((function(t){return t+"$property must be a credit card"}),t)}},t)},t.IsCurrency=function(t,e){return _({name:Ke,constraints:[t],validator:{validate:function(t,e){return ke(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a currency"}),e)}},e)},t.IsDataURI=function(t){return _({name:yi,validator:{validate:function(t,e){return mi(t)},defaultMessage:S((function(t){return t+"$property must be a data uri format"}),t)}},t)},t.IsDate=function(t){return _({name:xl,validator:{validate:function(t,e){return El(t)},defaultMessage:S((function(t){return t+"$property must be a Date instance"}),t)}},t)},t.IsDateString=function(t,e){return _({name:Go,constraints:[t],validator:{validate:function(e){return Uo(e,t)},defaultMessage:S((function(t){return t+"$property must be a valid ISO 8601 date string"}),e)}},e)},t.IsDecimal=function(t,e){return _({name:ge,constraints:[t],validator:{validate:function(t,e){return he(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property is not a valid decimal number."}),e)}},e)},t.IsDefined=function(t){return _({name:O,validator:{validate:function(t){return x(t)},defaultMessage:S((function(t){return t+"$property should not be null or undefined"}),t)}},t)},t.IsDivisibleBy=function(t,e){return _({name:Lt,constraints:[t],validator:{validate:function(t,e){return Rt(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be divisible by $constraint1"}),e)}},e)},t.IsEAN=function(t){return _({name:bi,validator:{validate:function(t,e){return Ii(t)},defaultMessage:S((function(t){return t+"$property must be an EAN (European Article Number)"}),t)}},t)},t.IsEmail=function(t,e){return _({name:dd,constraints:[t],validator:{validate:function(t,e){return rd(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be an email"}),e)}},e)},t.IsEmpty=function(t){return _({name:Y,validator:{validate:function(t,e){return X(t)},defaultMessage:S((function(t){return t+"$property must be empty"}),t)}},t)},t.IsEnum=function(t,e){return _({name:Ll,constraints:[t,Tl(t)],validator:{validate:function(t,e){return Rl(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be one of the following values: $constraint2"}),e)}},e)},t.IsEthereumAddress=function(t){return _({name:Oi,validator:{validate:function(t,e){return xi(t)},defaultMessage:S((function(t){return t+"$property must be an Ethereum address"}),t)}},t)},t.IsFQDN=function(t,e){return _({name:nd,constraints:[t],validator:{validate:function(t,e){return ad(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a valid domain name"}),e)}},e)},t.IsFirebasePushId=function(t){return _({name:vn,validator:{validate:function(t,e){return yn(t)},defaultMessage:S((function(t){return t+"$property must be a Firebase Push Id"}),t)}},t)},t.IsFullWidth=function(t){return _({name:ld,validator:{validate:function(t,e){return cd(t)},defaultMessage:S((function(t){return t+"$property must contain a full-width characters"}),t)}},t)},t.IsHSL=function(t){return _({name:Pi,validator:{validate:function(t,e){return Li(t)},defaultMessage:S((function(t){return t+"$property must be a HSL color"}),t)}},t)},t.IsHalfWidth=function(t){return _({name:yd,validator:{validate:function(t,e){return md(t)},defaultMessage:S((function(t){return t+"$property must contain a half-width characters"}),t)}},t)},t.IsHash=function(t,e){return _({name:Ro,constraints:[t],validator:{validate:function(t,e){return To(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a hash of type $constraint1"}),e)}},e)},t.IsHexColor=function(t){return _({name:Od,validator:{validate:function(t,e){return xd(t)},defaultMessage:S((function(t){return t+"$property must be a hexadecimal color"}),t)}},t)},t.IsHexadecimal=function(t){return _({name:Pd,validator:{validate:function(t,e){return Ld(t)},defaultMessage:S((function(t){return t+"$property must be a hexadecimal number"}),t)}},t)},t.IsIBAN=function(t){return _({name:Fi,validator:{validate:function(t,e){return Zi(t)},defaultMessage:S((function(t){return t+"$property must be an IBAN"}),t)}},t)},t.IsIP=function(t,e){return _({name:Zd,constraints:[t],validator:{validate:function(t,e){return Gd(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be an ip address"}),e)}},e)},t.IsISBN=function(t,e){return _({name:Jd,constraints:[t],validator:{validate:function(t,e){return qd(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be an ISBN"}),e)}},e)},t.IsISIN=function(t){return _({name:dr,validator:{validate:function(t,e){return rr(t)},defaultMessage:S((function(t){return t+"$property must be an ISIN (stock/security identifier)"}),t)}},t)},t.IsISO31661Alpha2=function(t){return _({name:wr,validator:{validate:function(t,e){return Br(t)},defaultMessage:S((function(t){return t+"$property must be a valid ISO31661 Alpha2 code"}),t)}},t)},t.IsISO31661Alpha3=function(t){return _({name:Ur,validator:{validate:function(t,e){return jr(t)},defaultMessage:S((function(t){return t+"$property must be a valid ISO31661 Alpha3 code"}),t)}},t)},t.IsISO4217CurrencyCode=function(t){return _({name:Ml,validator:{validate:function(t,e){return Sl(t)},defaultMessage:S((function(t){return t+"$property must be a valid ISO4217 currency code"}),t)}},t)},t.IsISO8601=function(t,e){return _({name:ir,constraints:[t],validator:{validate:function(t,e){return ur(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a valid ISO 8601 date string"}),e)}},e)},t.IsISRC=function(t){return _({name:Yi,validator:{validate:function(t,e){return Xi(t)},defaultMessage:S((function(t){return t+"$property must be an ISRC"}),t)}},t)},t.IsISSN=function(t,e){return _({name:Fo,constraints:[t],validator:{validate:function(t,e){return Zo(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a ISSN"}),e)}},e)},t.IsIdentityCard=function(t,e){return _({name:Hi,constraints:[t],validator:{validate:function(t,e){return Vi(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a identity card number"}),e)}},e)},t.IsIn=function(t,e){return _({name:q,constraints:[t],validator:{validate:function(t,e){return Q(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be one of the following values: $constraint1"}),e)}},e)},t.IsInstance=function(t,e){return _({name:rc,constraints:[t],validator:{validate:function(t,e){return nc(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t,e){return(null==e?void 0:e.constraints[0])?t+"$property must be an instance of ".concat(null==e?void 0:e.constraints[0].name):t+"".concat(rc," decorator expects and object as value, but got falsy value.")}),e)}},e)},t.IsInt=function(t){return _({name:Dl,validator:{validate:function(t,e){return wl(t)},defaultMessage:S((function(t){return t+"$property must be an integer number"}),t)}},t)},t.IsJSON=function(t){return _({name:fr,validator:{validate:function(t,e){return $r(t)},defaultMessage:S((function(t){return t+"$property must be a json string"}),t)}},t)},t.IsJWT=function(t){return _({name:mr,validator:{validate:function(t,e){return gr(t)},defaultMessage:S((function(t){return t+"$property must be a jwt string"}),t)}},t)},t.IsLatLong=function(t){return _({name:F,validator:{validate:function(t,e){return Z(t)},defaultMessage:S((function(t){return t+"$property must be a latitude,longitude string"}),t)}},t)},t.IsLatitude=function(t){return _({name:G,validator:{validate:function(t,e){return U(t)},defaultMessage:S((function(t){return t+"$property must be a latitude string or number"}),t)}},t)},t.IsLocale=function(t){return _({name:Qi,validator:{validate:function(t,e){return tu(t)},defaultMessage:S((function(t){return t+"$property must be locale"}),t)}},t)},t.IsLongitude=function(t){return _({name:j,validator:{validate:function(t,e){return H(t)},defaultMessage:S((function(t){return t+"$property must be a longitude string or number"}),t)}},t)},t.IsLowercase=function(t){return _({name:Ir,validator:{validate:function(t,e){return Mr(t)},defaultMessage:S((function(t){return t+"$property must be a lowercase string"}),t)}},t)},t.IsMACAddress=function(t,e){var d=Rd(t)?void 0:t,r=Rd(t)?t:e;return _({name:Bd,constraints:[d],validator:{validate:function(t,e){return Fd(t,d)},defaultMessage:S((function(t){return t+"$property must be a MAC Address"}),r)}},r)},t.IsMagnetURI=function(t){return _({name:nu,validator:{validate:function(t,e){return au(t)},defaultMessage:S((function(t){return t+"$property must be magnet uri format"}),t)}},t)},t.IsMilitaryTime=function(t){return _({name:Eo,validator:{validate:function(t,e){return Co(t)},defaultMessage:S((function(t){return t+"$property must be a valid representation of military time in the format HH:MM"}),t)}},t)},t.IsMimeType=function(t){return _({name:su,validator:{validate:function(t,e){return lu(t)},defaultMessage:S((function(t){return t+"$property must be MIME type format"}),t)}},t)},t.IsMobilePhone=function(t,e,d){return _({name:Cr,constraints:[t,e],validator:{validate:function(t,e){return Nr(t,null==e?void 0:e.constraints[0],null==e?void 0:e.constraints[1])},defaultMessage:S((function(t){return t+"$property must be a phone number"}),d)}},d)},t.IsMongoId=function(t){return _({name:kr,validator:{validate:function(t,e){return Wr(t)},defaultMessage:S((function(t){return t+"$property must be a mongodb id"}),t)}},t)},t.IsMultibyte=function(t){return _({name:Jr,validator:{validate:function(t,e){return qr(t)},defaultMessage:S((function(t){return t+"$property must contain one or more multibyte chars"}),t)}},t)},t.IsNegative=function(t){return _({name:wt,validator:{validate:function(t,e){return Bt(t)},defaultMessage:S((function(t){return t+"$property must be a negative number"}),t)}},t)},t.IsNotEmpty=function(t){return _({name:z,validator:{validate:function(t,e){return J(t)},defaultMessage:S((function(t){return t+"$property should not be empty"}),t)}},t)},t.IsNotEmptyObject=function(t,e){return _({name:ec,constraints:[t],validator:{validate:function(t,e){return dc(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a non-empty object"}),e)}},e)},t.IsNotIn=function(t,e){return _({name:tt,constraints:[t],validator:{validate:function(t,e){return et(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property should not be one of the following values: $constraint1"}),e)}},e)},t.IsNumber=function(t,e){return void 0===t&&(t={}),_({name:Cl,constraints:[t],validator:{validate:function(t,e){return Nl(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a number conforming to the specified constraints"}),e)}},e)},t.IsNumberString=function(t,e){return _({name:zo,constraints:[t],validator:{validate:function(t,e){return Jo(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a number string"}),e)}},e)},t.IsObject=function(t){return _({name:Ul,validator:{validate:function(t,e){return jl(t)},defaultMessage:S((function(t){return t+"$property must be an object"}),t)}},t)},t.IsOctal=function(t){return _({name:pu,validator:{validate:function(t,e){return vu(t)},defaultMessage:S((function(t){return t+"$property must be valid octal number"}),t)}},t)},t.IsOptional=function(t){return function(d,r){var n={type:c.CONDITIONAL_VALIDATION,name:E,target:d.constructor,propertyName:r,constraints:[function(t,e){return null!==t[r]&&void 0!==t[r]}],validationOptions:t};s().addValidationMetadata(new e(n))}},t.IsPassportNumber=function(t,e){return _({name:hu,constraints:[t],validator:{validate:function(t,e){return Au(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be valid passport number"}),e)}},e)},t.IsPhoneNumber=function(t,e){return _({name:Oo,constraints:[t],validator:{validate:function(t,e){return xo(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a valid phone number"}),e)}},e)},t.IsPort=function(t){return _({name:kd,validator:{validate:function(t,e){return Wd(t)},defaultMessage:S((function(t){return t+"$property must be a port"}),t)}},t)},t.IsPositive=function(t){return _({name:Tt,validator:{validate:function(t,e){return Dt(t)},defaultMessage:S((function(t){return t+"$property must be a positive number"}),t)}},t)},t.IsPostalCode=function(t,e){return _({name:Nu,constraints:[t],validator:{validate:function(t,e){return Pu(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a postal code"}),e)}},e)},t.IsRFC3339=function(t){return _({name:Du,validator:{validate:function(t,e){return wu(t)},defaultMessage:S((function(t){return t+"$property must be RFC 3339 date"}),t)}},t)},t.IsRgbColor=function(t,e){return _({name:Gu,constraints:[t],validator:{validate:function(t,e){return Uu(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be RGB color"}),e)}},e)},t.IsSemVer=function(t){return _({name:Wu,validator:{validate:function(t,e){return Yu(t)},defaultMessage:S((function(t){return t+"$property must be a Semantic Versioning Specification"}),t)}},t)},t.IsString=function(t){return _({name:Bl,validator:{validate:function(t,e){return Fl(t)},defaultMessage:S((function(t){return t+"$property must be a string"}),t)}},t)},t.IsStrongPassword=function(t,e){return _({name:vl,constraints:[t],validator:{validate:function(t,e){return yl(t,e.constraints[0])},defaultMessage:S((function(t){return t+"$property is not strong enough"}),e)}},e)},t.IsSurrogatePair=function(t){return _({name:dn,validator:{validate:function(t,e){return rn(t)},defaultMessage:S((function(t){return t+"$property must contain any surrogate pairs chars"}),t)}},t)},t.IsTaxId=function(t,e){return _({name:bl,constraints:[t],validator:{validate:function(t,e){return Il(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a Tax Identification Number"}),e)}},e)},t.IsTimeZone=function(t){return _({name:ml,validator:{validate:function(t,e){return gl(t)},defaultMessage:S((function(t){return t+"$property must be a valid IANA time-zone"}),t)}},t)},t.IsUUID=function(t,e){return _({name:$n,constraints:[t],validator:{validate:function(t,e){return pn(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a UUID"}),e)}},e)},t.IsUppercase=function(t){return _({name:An,validator:{validate:function(t,e){return bn(t)},defaultMessage:S((function(t){return t+"$property must be uppercase"}),t)}},t)},t.IsUrl=function(t,e){return _({name:un,constraints:[t],validator:{validate:function(t,e){return sn(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be a URL address"}),e)}},e)},t.IsVariableWidth=function(t){return _({name:bd,validator:{validate:function(t,e){return Id(t)},defaultMessage:S((function(t){return t+"$property must contain a full-width and half-width characters"}),t)}},t)},t.Length=function(t,e,d){return _({name:_n,constraints:[t,e],validator:{validate:function(t,e){return On(t,null==e?void 0:e.constraints[0],null==e?void 0:e.constraints[1])},defaultMessage:S((function(t,e){var d=null!==(null==e?void 0:e.constraints[0])&&void 0!==(null==e?void 0:e.constraints[0]),r=null!==(null==e?void 0:e.constraints[1])&&void 0!==(null==e?void 0:e.constraints[1]);return d&&(!e.value||e.value.length<(null==e?void 0:e.constraints[0]))?t+"$property must be longer than or equal to $constraint1 characters":r&&e.value.length>(null==e?void 0:e.constraints[1])?t+"$property must be shorter than or equal to $constraint2 characters":t+"$property must be longer than or equal to $constraint1 and shorter than or equal to $constraint2 characters"}),d)}},d)},t.MATCHES=Tn,t.MAX="max",t.MAX_DATE=jt,t.MAX_LENGTH=xn,t.MIN="min",t.MIN_DATE=Gt,t.MIN_LENGTH=Cn,t.Matches=function(t,e,d){var r;return e&&e instanceof Object&&!d?d=e:r=e,_({name:Tn,constraints:[t,r],validator:{validate:function(t,e){return Dn(t,null==e?void 0:e.constraints[0],null==e?void 0:e.constraints[1])},defaultMessage:S((function(t,e){return t+"$property must match $constraint1 regular expression"}),d)}},d)},t.Max=function(t,e){return _({name:"max",constraints:[t],validator:{validate:function(t,e){return Ft(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must not be greater than $constraint1"}),e)}},e)},t.MaxDate=function(t,e){return _({name:jt,constraints:[t],validator:{validate:function(t,e){return Ht(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return"maximal allowed date for "+t+"$property is $constraint1"}),e)}},e)},t.MaxLength=function(t,e){return _({name:xn,constraints:[t],validator:{validate:function(t,e){return En(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be shorter than or equal to $constraint1 characters"}),e)}},e)},t.MetadataStorage=u,t.Min=function(t,e){return _({name:"min",constraints:[t],validator:{validate:function(t,e){return Zt(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must not be less than $constraint1"}),e)}},e)},t.MinDate=function(t,e){return _({name:Gt,constraints:[t],validator:{validate:function(t,e){return Ut(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return"minimal allowed date for "+t+"$property is $constraint1"}),e)}},e)},t.MinLength=function(t,e){return _({name:Cn,constraints:[t],validator:{validate:function(t,e){return Nn(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property must be longer than or equal to $constraint1 characters"}),e)}},e)},t.NOT_CONTAINS=Jt,t.NOT_EQUALS=k,t.NotContains=function(t,e){return _({name:Jt,constraints:[t],validator:{validate:function(t,e){return qt(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property should not contain a $constraint1 string"}),e)}},e)},t.NotEquals=function(t,e){return _({name:k,constraints:[t],validator:{validate:function(t,e){return W(t,null==e?void 0:e.constraints[0])},defaultMessage:S((function(t){return t+"$property should not be equal to $constraint1"}),e)}},e)},t.Validate=function(t,d,r){return function(n,a){var o={type:c.CUSTOM_VALIDATION,target:n.constructor,propertyName:a,constraintCls:t,constraints:Array.isArray(d)?d:void 0,validationOptions:Array.isArray(d)?r:d};s().addValidationMetadata(new e(o))}},t.ValidateBy=_,t.ValidateIf=function(t,d){return function(r,n){var a={type:c.CONDITIONAL_VALIDATION,target:r.constructor,propertyName:n,constraints:[t],validationOptions:d};s().addValidationMetadata(new e(a))}},t.ValidateNested=function(t){var d=C({},t),r=d.each?"each value in ":"";return d.message=d.message||r+"nested property $property must be either object or array",function(t,r){var n={type:c.NESTED_VALIDATION,target:t.constructor,propertyName:r,validationOptions:d};s().addValidationMetadata(new e(n))}},t.ValidatePromise=function(t){return function(d,r){var n={type:c.PROMISE_VALIDATION,target:d.constructor,propertyName:r,validationOptions:t};s().addValidationMetadata(new e(n))}},t.ValidationError=l,t.ValidationTypes=c,t.Validator=h,t.ValidatorConstraint=function(t){return function(e){var d=t&&t.async,r=t&&t.name?t.name:"";r||(r=e.name)||(r=r.replace(/\.?([A-Z]+)/g,(function(t,e){return"_"+e.toLowerCase()})).replace(/^_/,""));var n=new I(e,r,d);s().addConstraintMetadata(n)}},t.arrayContains=Vl,t.arrayMaxSize=ql,t.arrayMinSize=zl,t.arrayNotContains=kl,t.arrayNotEmpty=Yl,t.arrayUnique=tc,t.buildMessage=S,t.contains=zt,t.equals=K,t.getFromContainer=b,t.getMetadataStorage=s,t.isAlpha=ae,t.isAlphanumeric=fe,t.isArray=Gl,t.isAscii=Se,t.isBIC=ii,t.isBase32=di,t.isBase58=Al,t.isBase64=Ce,t.isBoolean=Ol,t.isBooleanString=ko,t.isBtcAddress=fi,t.isByteLength=Te,t.isCreditCard=Ue,t.isCurrency=ke,t.isDataURI=mi,t.isDate=El,t.isDateString=Uo,t.isDecimal=he,t.isDefined=x,t.isDivisibleBy=Rt,t.isEAN=Ii,t.isEmail=rd,t.isEmpty=X,t.isEnum=Rl,t.isEthereumAddress=xi,t.isFQDN=ad,t.isFirebasePushId=yn,t.isFullWidth=cd,t.isHSL=Li,t.isHalfWidth=md,t.isHash=To,t.isHexColor=xd,t.isHexadecimal=Ld,t.isIBAN=Zi,t.isIP=Gd,t.isISBN=qd,t.isISIN=rr,t.isISO31661Alpha2=Br,t.isISO31661Alpha3=jr,t.isISO4217CurrencyCode=Sl,t.isISO8601=ur,t.isISRC=Xi,t.isISSN=Zo,t.isIdentityCard=Vi,t.isIn=Q,t.isInstance=nc,t.isInt=wl,t.isJSON=$r,t.isJWT=gr,t.isLatLong=Z,t.isLatitude=U,t.isLocale=tu,t.isLongitude=H,t.isLowercase=Mr,t.isMACAddress=Fd,t.isMagnetURI=au,t.isMilitaryTime=Co,t.isMimeType=lu,t.isMobilePhone=Nr,t.isMongoId=Wr,t.isMultibyte=qr,t.isNegative=Bt,t.isNotEmpty=J,t.isNotEmptyObject=dc,t.isNotIn=et,t.isNumber=Nl,t.isNumberString=Jo,t.isObject=jl,t.isOctal=vu,t.isPassportNumber=Au,t.isPhoneNumber=xo,t.isPort=Wd,t.isPositive=Dt,t.isPostalCode=Pu,t.isRFC3339=wu,t.isRgbColor=Uu,t.isSemVer=Yu,t.isString=Fl,t.isStrongPassword=yl,t.isSurrogatePair=rn,t.isTaxId=Il,t.isTimeZone=gl,t.isURL=sn,t.isUUID=pn,t.isUppercase=bn,t.isValidationOptions=Rd,t.isVariableWidth=Id,t.length=On,t.matches=Dn,t.max=Ft,t.maxDate=Ht,t.maxLength=En,t.min=Zt,t.minDate=Ut,t.minLength=Nn,t.notContains=qt,t.notEquals=W,t.registerDecorator=M,t.registerSchema=function(t){s().addValidationSchema(t)},t.useContainer=function(t,e){f=t,$=e},t.validate=function(t,e,d){return"string"==typeof t?b(h).validate(t,e,d):b(h).validate(t,e)},t.validateOrReject=function(t,e,d){return"string"==typeof t?b(h).validateOrReject(t,e,d):b(h).validateOrReject(t,e)},t.validateSync=function(t,e,d){return"string"==typeof t?b(h).validateSync(t,e,d):b(h).validateSync(t,e)},Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=class-validator.umd.min.js.map
